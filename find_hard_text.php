<?php

/**
 * Script để tìm kiếm hard text tiếng Việt trong các file .php và .blade.php
 */

class HardTextFinder
{
    private $results = [];
    private $excludePaths = [
        'vendor/',
        'node_modules/',
        'storage/framework/views/',
        'bootstrap/cache/',
        'lang/',
        'database/migrations/',
        'database/seeders/',
        'database/factories/',
        'tests/',
    ];

    private $vietnamesePattern = '/[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ]/u';

    public function findHardText($directory = '.')
    {
        $this->scanDirectory($directory);
        return $this->results;
    }

    private function scanDirectory($directory)
    {
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($directory, RecursiveDirectoryIterator::SKIP_DOTS)
        );

        foreach ($iterator as $file) {
            if ($file->isFile()) {
                $filePath = $file->getPathname();
                
                // Skip excluded paths
                if ($this->shouldSkipFile($filePath)) {
                    continue;
                }

                // Only process .php and .blade.php files
                if (preg_match('/\.(php|blade\.php)$/', $filePath)) {
                    $this->processFile($filePath);
                }
            }
        }
    }

    private function shouldSkipFile($filePath)
    {
        foreach ($this->excludePaths as $excludePath) {
            if (strpos($filePath, $excludePath) !== false) {
                return true;
            }
        }
        return false;
    }

    private function processFile($filePath)
    {
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        foreach ($lines as $lineNumber => $line) {
            $hardTexts = $this->findHardTextInLine($line);
            
            if (!empty($hardTexts)) {
                foreach ($hardTexts as $hardText) {
                    $this->results[] = [
                        'file' => $filePath,
                        'line' => $lineNumber + 1,
                        'text' => $hardText,
                        'context' => trim($line)
                    ];
                }
            }
        }
    }

    private function findHardTextInLine($line)
    {
        $hardTexts = [];
        
        // Skip comments
        if (preg_match('/^\s*(\/\/|\/\*|\*|#)/', $line)) {
            return $hardTexts;
        }

        // Skip lines that already use translation functions
        if (preg_match('/(__|trans|@lang)\s*\(/', $line)) {
            return $hardTexts;
        }

        // Find strings in quotes that contain Vietnamese characters
        $patterns = [
            // Double quotes
            '/"([^__('common.aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooo_1')]*)"/',
            // Single quotes
            "/'([^__('common.aaaaaaaaaaaaaaaaaeeeeeeeeeeeiiiiiooooooooooooooooo_1')]*)'/"
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $line, $matches)) {
                foreach ($matches[1] as $match) {
                    // Skip if it's a variable or contains PHP code
                    if (strpos($match, '$') !== false || strpos($match, '{{') !== false) {
                        continue;
                    }
                    
                    // Skip if it's just a single character or very short
                    if (mb_strlen(trim($match)) < 2) {
                        continue;
                    }
                    
                    // Skip if it's a file path or URL
                    if (preg_match('/\.(jpg|jpeg|png|gif|svg|css|js|php|html|avif)$/i', $match) || 
                        strpos($match, '/') !== false || 
                        strpos($match, 'http') !== false) {
                        continue;
                    }
                    
                    $hardTexts[] = trim($match);
                }
            }
        }

        return array_unique($hardTexts);
    }

    public function generateReport()
    {
        echo __('common.bAo_cAo_hard_text_tiEng_viEt_nn');
        echo "Tổng số hard text tìm thấy: " . count($this->results) . "\n\n";

        $groupedByFile = [];
        foreach ($this->results as $result) {
            $groupedByFile[$result['file']][] = $result;
        }

        foreach ($groupedByFile as $file => $texts) {
            echo "FILE: $file\n";
            echo str_repeat("-", 80) . "\n";
            
            foreach ($texts as $text) {
                echo "  Dòng {$text['line']}: {$text['text']}\n";
                echo "  Context: {$text['context']}\n\n";
            }
            echo "\n";
        }
    }

    public function generateTranslationFiles()
    {
        $translations = [];
        $counter = 1;

        foreach ($this->results as $result) {
            $text = $result['text'];
            $key = $this->generateKey($text, $counter);
            
            if (!isset($translations[$key])) {
                $translations[$key] = $text;
                $counter++;
            }
        }

        return $translations;
    }

    private function generateKey($text, $counter)
    {
        // Generate a meaningful key from the text
        $key = strtolower($text);
        $key = preg_replace('/[^a-zA-Z0-9\s]/', '', $key);
        $key = preg_replace('/\s+/', '_', trim($key));
        $key = substr($key, 0, 50); // Limit length
        
        if (empty($key)) {
            $key = "text_$counter";
        }
        
        return $key;
    }
}

// Run the script
$finder = new HardTextFinder();
$results = $finder->findHardText('.');

if (empty($results)) {
    echo __('common.khong_tim_thay_hard_text_tieng_viet_naon');
} else {
    $finder->generateReport();
    
    // Generate translation files
    $translations = $finder->generateTranslationFiles();
    
    echo "\n=== TRANSLATION KEYS ===\n";
    foreach ($translations as $key => $text) {
        echo "'$key' => '$text',\n";
    }
}
