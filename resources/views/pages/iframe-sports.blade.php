<x-layout>
    <div id="iframe-container" class="container !max-w-full !px-0 loader-image flex h-full min-h-screen loading-image">
        <div id="loader" class="inset-0 flex flex-col justify-center items-center z-1 w-[100vw] h-[calc(100dvh-108px)] xl:h-[calc(100dvh-132px)]">
            <img src="{{ asset('asset/images/sports/soccer.avif') }}" class="size-[160px] mb-4"/>
            <div class="w-[250px] h-2.5 bg-neutral-150 rounded-[2px] mb-2.5">
                <div id="progress-bar" class="h-full bg-secondary-500 rounded-[2px] transition-all duration-300"
                    style="width: 0%"></div>
            </div>
            <p class="text-[14px] leading-[20px] text-neutral-800"><PERSON>ang tải.. </p>
        </div>
        <iframe id="iframe" class="hidden w-full" frameborder="0" allowfullscreen>
        </iframe>
    </div>
    <div id="iframe-maintenace" class="container hidden justify-center xl:min-h-[600px] items-center min-h-[calc(100vh_-_120px)] py-6">
        <div class="flex flex-col-reverse justify-center items-center gap-[30px] my-4 xl:flex-row xl:gap-x-[100px]">
            <div class="flex flex-col items-center gap-[16px] max-w-[320px] xl:items-start">
                <p class="xl:text-[24px] xl:leading-[36px] font-bold text-[18px] text-neutral-950 leading-[calc(26/18)] capitalize">
                    Đang bảo trì
                </p>
                <p class="xl:text-[16px] xl:leading-[24px] text-[14px] leading-[calc(20/14] text-neutral-800 text-center xl:text-left">
                    Website đang được bảo trì, chúng tôi sẽ sớm trở lại. Quý khách vui lòng quay lại sau.
                </p>
                <x-kit.button class="capitalize" onclick="openLiveChat()" target="_blank">Liên hệ hỗ trợ</x-kit.button>
            </div>
            <img src="{{ asset('asset/images/errors/maintenance.avif') }}"
                class="xl:max-w-[465px] max-w-[311px] aspect-[311/232] xl:aspect-[465/338]"
                alt="maintenance">
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener("DOMContentLoaded", async () => {
            const progressBar = document.getElementById("progress-bar");
            let progress = 0;
            const interval = setInterval(() => {
                progress += 1;
                if (progress >= 99) {
                    clearInterval(interval);
                }
                progressBar.style.width = progress + '%';
            }, 40);
            const iframeContainer = document.getElementById("iframe-container");
            const iframe = document.getElementById("iframe");
            const loader = document.getElementById("loader");
            const iframeMaintenace = document.getElementById("iframe-maintenace");
            const iframeUrl = @json($iframeUrl);
            const endpoint = @json($endpoint);

            if (iframeUrl) {
                iframe.src = iframeUrl;
                iframe.onload = () => {
                    loader.classList.add('hidden');
                    iframe.classList.remove('hidden');
                };
            } else {
                const { status, data } = await fetchData(endpoint, {}, {}, "");

                if (status === 'OK') {
                    const currentDomain = window.location.origin;
                    const urlObj = new URL(data?.url);
                    urlObj.searchParams.set('loginUrl', `${currentDomain}?type=modal-login`);
                    urlObj.searchParams.set('registerUrl', `${currentDomain}?type=modal-login`);

                    iframe.src = urlObj?.href;
                    iframe.onload = () => {
                        loader.classList.add('hidden');
                        iframe.classList.remove('hidden');
                    };
                    return;
                }
                setTimeout(() => {
                    iframeContainer.remove();
                    iframeMaintenace.classList.remove('hidden');
                    iframeMaintenace.classList.add('flex');
                }, 2000)
            }
        });
    </script>
    @endpush
</x-layout>
