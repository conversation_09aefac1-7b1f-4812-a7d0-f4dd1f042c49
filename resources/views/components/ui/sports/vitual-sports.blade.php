@props(['listSports'])

<section>
    <div class="flex flex-col gap-3 xl:gap-4">
        <x-ui.title-section title=__('sports.the_thao_1') titleHighlight=__('sports.Ao') titleButton=""></x-ui.title-section>
        <ul class="grid grid-cols-1 gap-2 list-vitual-sports xl:hidden">
            @foreach ($listSports as $sport)
                <li class="sport-item relative cursor-pointer flex-shrink-0 rounded-lg effect-hover xl:min-w-full min-w-[116px]"
                    onclick="openSport({
                        link: '{{ $sport['url'] }}',
                        apiUrl: '{{ $sport['apiUrl'] ?? '' }}',
                        loginRequired: '{{ $sport['loginRequired'] ?? false }}',
                        })">
                    <div class="group relative overflow-hidden rounded-[6px] xl:rounded-[8px]">
                        <img src="{{ asset($sport['imgmb']) }}" class="w-full object-cover" loading="lazy" alt="{{ $sport['name'] . '_imgmb' }}" />
                    </div>
                </li>
            @endforeach
        </ul>
        <div class="hidden flex-col gap-3 xl:flex">
            <ul class="grid grid-cols-[460fr_310fr_460fr] gap-[5px] list-vitual-sports">
                @foreach (array_slice($listSports, 0, 2) as $index => $sport)
                    <li class="sport-item relative cursor-pointer flex-shrink-0 rounded-lg effect-hover xl:min-w-full min-w-[116px]"
                        onclick="openSport({
                            link: '{{ $sport['url'] }}',
                            apiUrl: '{{ $sport['apiUrl'] ?? '' }}',
                            loginRequired: '{{ $sport['loginRequired'] ?? false }}',
                            })">
                        <div class="group relative overflow-hidden rounded-[6px] xl:rounded-[8px]">
                            <img src="{{ asset($sport['img']) }}" class="w-full group-hover:opacity-0" loading="lazy"
                                alt="{{ $sport['name'] . '_img' }}" />
                            <img src="{{ asset($sport['imghover']) }}" class="w-full absolute top-0 left-0 opacity-0 group-hover:opacity-100" loading="lazy"
                                alt="{{ $sport['name'] . '_imghover' }}" />
                        </div>
                    </li>

                    @if ($index === 0)
                        <div class="relative">
                            <img class="absolute top-0 left-0 aspect-[310/356]" src="{{ asset('asset/images/sports/virtual-bg.avif') }}" loading="lazy" alt="virtual sport" />
                        </div>
                    @endif
                @endforeach
            </ul>
            <ul class="flex justify-between items-center list-vitual-sports">
                @foreach (array_slice($listSports, 2, 2) as $index => $sport)
                    <li class="sport-item relative cursor-pointer flex-shrink-0 rounded-lg effect-hover w-[43.22%]"
                        onclick="openSport({
                            link: '{{ $sport['url'] }}',
                            apiUrl: '{{ $sport['apiUrl'] ?? '' }}',
                            loginRequired: '{{ $sport['loginRequired'] ?? false }}',
                            })">
                        <div class="group relative overflow-hidden rounded-[6px] xl:rounded-[8px]">
                            <img src="{{ asset($sport['img']) }}" class="w-full group-hover:opacity-0" loading="lazy"
                                alt="{{ $sport['name'] . '_img' }}" />
                            <img src="{{ asset($sport['imghover']) }}" class="w-full absolute top-0 left-0 opacity-0 group-hover:opacity-100" loading="lazy"
                                alt="{{ $sport['name'] . '_imghover' }}" />
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</section>
