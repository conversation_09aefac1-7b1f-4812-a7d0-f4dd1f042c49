@props(['title', 'games', 'activeFilter', 'filters', 'gamesTotal', 'routeUrl', 'swiperConfig', 'type' => 'game', 'streamGames' => [], 'newStreamGames' => []])
@php
    use App\Helpers\DetectDeviceHelper;
    $filterValue = isset(request()->filter ) ? request()->filter : '';
    $hiddenFilter = false;
    $currentSlug = request()->route('slug');
    $currentPath = request()->path();
    $showLiveHighlight = ($type === 'casino' && $currentSlug !== 'favorite' && ($currentPath === 'song-bai-livecasino-truc-tuyen' || $currentPath === 'song-bai-livecasino-truc-tuyen/all'));
    $uniqueProviders = [];
    foreach ($games as $game) {
        if (isset($game->partner_provider) && !in_array($game->partner_provider, $uniqueProviders)) {
            $uniqueProviders[] = $game->partner_provider;
        }
    }
@endphp

<div class="js-game-container-list relative mb-20 pb-1 xl:mb-[60px] xl:pb-0 w-full min-h-[440px] xl:min-h-[460px]">
    <div class="{{$showLiveHighlight? "pr-[10px]":''}}">
        @if (request()->route('slug') !== 'da-ga')
        <div>
            <x-ui.types-filter :types="$filters['typeOptions'] ?? []" :$activeFilter id="type-buttons" :$swiperConfig :pageType="$type"/>
        </div>
        @endif
        @if (request()->route('slug') !== 'da-ga' && (request()->route('slug') !== 'favorite' || !empty($games)))
        <div class="flex justify-between items-center flex-wrap xl:mt-4 xl:mb-6 mt-2 mb-3 xl:flex-row gap-y-2">

            <div class="hidden xl:flex items-center flex-wrap gap-2">
                @if (request()->route('slug') !== 'favorite')
                    <x-ui.filter :filters="$filters['sortOptions']" :$activeFilter id="filter-buttons" />
                @endif
            </div>

            <div class="js-game-container-filter items-center justify-between gap-1 xl:gap-2 w-full xl:w-auto relative {{$hiddenFilter ? 'hidden' : 'flex'}}">
                <div class="flex items-center gap-1 w-full">
                @if (isset($filters['sortOptions']) && count($filters['sortOptions']) > 0 && request()->route('slug') !== 'favorite')
                    <div class="filter-dropdown flex-1 h-[36px] [&_.dropdown-button]:capitalize flex xl:hidden">
                        @php
                            $sortDefault = $filters['sortOptions'][1];
                            if (request()->get(key: 'filter')) {
                                foreach ($filters['sortOptions'] as $sort) {
                                    if ($sort['value'] === request()->get(key: 'filter')) {
                                        $sortDefault = $sort;
                                        break;
                                    }
                                }
                            }

                            $filterArray = array_filter(
                                $filters['sortOptions'],
                                function ($item) {
                                    return !isset($item['checkAuth']) || (isset($item['checkAuth']) && Auth::check());
                                }
                            );

                            $validOptions = array_map(function($item){
                                $item["icon"] = $item["icon"] . '-mb';
                                return $item;
                            },$filterArray)

                        @endphp
                        <x-kit.dropdown-new 
                            placeholderIcon="" 
                            id="filter" 
                            :options="$validOptions" 
                            :defaultValue="$sortDefault" 
                            activeClass="filter-active"
                            class="filter-btn sort-game-item capitalize" 
                            dropdownListClass="!max-h-[176px]"
                        />
                    </div>
                @endif

                @php
                    $allOption = (object) ['label' => 'Nhà Cung Cấp', 'value' => 'all', 'key' => 'all', 'icon' => 'all'];
                    $providerOptions = array_merge([$allOption], ($filters['providerOptions'] ?? []));
                    $defaultProviderValue = null;
                    if (!request()->get(key: 'p')) {
                        $defaultProviderValue = $providerOptions[0];
                    } else {
                        foreach ($providerOptions as $provider) {
                            if ($provider->value === request()->get(key: 'p')) {
                                $defaultProviderValue = $provider;
                                break;
                            }
                        }
                    }
                    if (isset($defaultProviderValue) && $defaultProviderValue->value === 'all') {
                        $defaultProviderValue->icon = 'all';
                    }
                    $defaultValue = $defaultProviderValue->value ?? '';
                @endphp
                    <div
                        class="js-provider-dropdown provider-dropdown min-w-[calc(50%_-_20px)] flex-1 xl:w-[200px] xl:min-w-[200px] h-[36px] xl:h-[40px] flex"
                        data-recents="{{ json_encode($filters['providerRecents']) }}"
                        data-provider-options='@json($uniqueProviders)'
                    >
                        <x-kit.dropdown-new 
                            placeholder="NCC" 
                            itemClass="js-provider-btn text-neutral-800 h-11" 
                            id="provider" 
                            :options="$providerOptions" 
                            :value="$defaultValue"
                            :defaultValue="$defaultProviderValue" 
                            class="px-3 py-2 provider-btn" 
                            isHasIcon 
                            classValue="!text-neutral-1000" 
                            activeClass="provider-active" 
                            dropdownContainerClass="js-provider-dropdown-container {{ isset($filters['providerOptions']) && count($filters['providerOptions']) > 1 ? '' : 'hidden' }}"
                            dropdownListClass="!max-h-[396px]"/>
                    </div>
                </div>
                <button id="search-button"
                    class="xl:!hidden min-w-[36px] w-[36px] h-[36px] bg-neutral-50 rounded-[8px] flex items-center justify-center">
                    <img src="{{ asset('asset/icons/ic-search.svg') }}" class="size-[20px] search-icon" alt="icon" />
                    <img src="{{ asset('asset/icons/ic-close.svg') }}" class="w-[24px] h-[24px] close-icon hidden" alt="icon" />
                </button>

                <div class="filter-search mobile-search xl:block xl:w-[200px]">
                    <x-kit.search-input
                        id="searchKeyword"
                        class="xl:w-[200px] xl:!h-[40px] md:w-full !h-[36px]"
                        placeholder="{{ __('common.search') }}"
                        value="{{ isset(request()->keyword ) && request()->keyword ? substr(request()->keyword, 0, 20) : '' }}"
                        showClose>
                    </x-kit.search-input>
                </div>

            </div>
        </div>
        @endif
    </div>
    @if ($showLiveHighlight)
        <div class="xl:!hidden [&_.container]:!pl-0 [&_.live-highlight-container]:pr-[10px] {{
            !in_array($filterValue,['new','recent']) && !request()->keyword && ((request()->p && request()->p === 'all') || !request()->p) ? '' :'hidden'}}" id="livestreamBox">
            <x-ui.home.live type="lobby" :gameList="$streamGames" :newGameList="$newStreamGames"/>
        </div>
    @endif
    <div class="{{$showLiveHighlight? "pr-[10px]":''}}">
        <div id="game-container" class="grid gap-x-1.5 gap-y-3 grid-cols-2 md:grid-cols-3 xl:grid-cols-5 xl:gap-x-5 xl:gap-y-6 pb-[22px] xl:pb-6 {{ empty($games) ? 'hidden' : '' }}">
            @foreach ($games as $game)
                <x-ui.card 
                    :game="$game" 
                    name="{{ $game->name ?? 'title' }}" 
                    image="{{ $game->image ?? '' }}"
                    type="{{ $type }}"
                    data-api="{{ $game->api_url ?? '' }}"
                    id="{{ $game->partner_game_id ?? '' }}"
                    favorite="{{ isset($game->is_favorite) && $game->is_favorite ? 'favorite' : '' }}"
                    provider="{{ $game->partner_txt ?? '' }}"
                    partner="{{ $game->partner ?? '' }}"
                    table_id="{{ $game->table_id ?? '' }}"
                    tags="{{ $game->tags ?? '' }}"
                    class="flex flex-col items-center text-marron loader-image-transparent" />
            @endforeach
        </div>

        <div class="flex justify-center js-games-loadmore {{ isset($gamesTotal) && count($games) < $gamesTotal ? '' : 'hidden' }}">
            <button
                id="loadmore"
                type="button"
                class="border border-solid border-primary-500 hover:border-primary-400 text-sm font-medium text-primary-700 h-[32px] xl:h-[40px] min-w-[156px] xl:min-w-[168px] rounded-full">
                    Xem Thêm <span class="js-current-game-count pl-[0px]">{{ count($games) }}</span>/<span class="js-game-total">{{ $gamesTotal ?? count($games) }}</span>
            </button>
        </div>

        <div  class="js-empty-games {{ empty($games) ? '' : 'hidden' }}">
            <x-ui.game.empty />
        </div>
    </div>
</div>

@pushOnce('scripts')
@vite(['resources/js/game-filters.js'])

<script>
    const filters = @json($filters);
    const games = @json($games);
    const activeFilter = @json($activeFilter);
    const type = @json($type);
    window.addEventListener("load", (event) => {
        handleDropdown();
        
        const searchBtn = document.getElementById('search-button');
        const searchInput = document.querySelector('.mobile-search');
        const searchIcon = searchBtn?.querySelector('.search-icon');
        const closeIcon = searchBtn?.querySelector('.close-icon');

        // Add click event listener to document
        document.addEventListener('click', (event) => {
            const isClickInside = searchBtn?.contains(event.target) || searchInput?.contains(event
                .target);

            if (!isClickInside && !searchInput?.classList?.contains('hidden')) {
                closeSearch();
            }
        });

        // Function to close search
        const closeSearch = () => {
            searchInput?.classList?.add('hidden');
            searchInput?.classList?.add('xl:block');
            searchInput?.classList?.remove('show');
            searchIcon?.classList?.remove('hidden');
            closeIcon?.classList?.add('hidden');
        };

        searchBtn?.addEventListener('click', (event) => {
            event.preventDefault();
            // event.stopPropagation(); // Prevent document click from immediately closing
            searchInput?.classList?.toggle('hidden');
            searchInput?.classList?.toggle('xl:block');
            searchIcon?.classList?.toggle('hidden');
            closeIcon?.classList?.toggle('hidden');
            setTimeout(() => {
                searchInput?.classList?.toggle('show');
            }, 10);
            if (!searchInput?.classList?.contains('hidden')) {
                searchInput?.focus();
            }
        });

        const createElement = (game) => {
            const gameJson = encodeURIComponent(JSON.stringify(game));
            const gameFavorite = game.is_favorite ? 'favorite' : '';
            const gameTags = game.tags ? game.tags : '';
            const gamePartner = game.partner || gameJson.partner;
            const gameName = game?.name.replace(/'/g, "&#39;");

            const livestreams = [
                'rik_vgmn_108',
                'rik_vgmn_109',
                'rik_vgmn_110',
                'rik_vgmn_111',
                'go_qs_txgo-101',
                'go_qs_xocdia-102',
                'go_vgmn_109',
                'b52_vgmn_108',
                'b52_vgmn_109',
                'b52_vgmn_110',
                '789club_G1X_305',
                '789club_G1X_306',
                'sunwin_G1S_305',
                'sunwin_G1X_306',
                'sunwin_G1S_306'
            ]

            if (livestreams.includes(`${gamePartner}_${game.partner_game_id}`)) {
                if (game.is_favorite) {
                    return  `<x-ui.card
                        name="${gameName}"
                        image="${game.image}"
                        type="${type}"
                        game="${JSON.stringify(game)}"
                        partner="${game.partner || gameJson.partner}"
                        data-game-id="${game.partner_game_id}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        favorite="favorite"
                        table_id="${game.table_id}"
                        deny_info="${game.deny_info}"
                        maintain="${game.maintain}"
                        tags='${gameTags}'
                        provider="${game.partner_txt}"
                        isLiveStream
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
                }
                return  `<x-ui.card
                            name="${gameName}"
                            image="${game.image}"
                            type="${type}"
                            game="${JSON.stringify(game)}"
                            partner="${game.partner || gameJson.partner}"
                            data-game-id="${game.partner_game_id}"
                            data-api="${game.api_url}"
                            id="${game.partner_game_id}"
                            favorite="''"
                            provider="${game.partner_txt}"
                            tags="${gameTags}"
                            table_id="${game.table_id}"
                            deny_info="${game.deny_info}"
                            maintain="${game.maintain}"
                            isLiveStream
                            class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
            }

            if (game.is_favorite) {
                return  `<x-ui.card
                    name="${gameName}"
                    image="${game.image}"
                    type="${type}"
                    game="${JSON.stringify(game)}"
                    partner="${game.partner || gameJson.partner}"
                    data-game-id="${game.partner_game_id}"
                    data-api="${game.api_url}"
                    id="${game.partner_game_id}"
                    favorite="favorite"
                    tags='${gameTags}'
                    provider="${game.partner_txt}"
                    table_id="${game.table_id}"
                    deny_info="${game.deny_info}"
                    maintain="${game.maintain}"
                    class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
            }
            return  `<x-ui.card
                        name="${gameName}"
                        image="${game.image}"
                        type="${type}"
                        game="${JSON.stringify(game)}"
                        partner="${game.partner || gameJson.partner}"
                        data-game-id="${game.partner_game_id}"
                        data-api="${game.api_url}"
                        id="${game.partner_game_id}"
                        table_id="${game.table_id}"
                        deny_info="${game.deny_info}"
                        maintain="${game.maintain}"
                        favorite="''"
                        provider="${game.partner_txt}"
                        tags="${gameTags}"
                        class="flex flex-col items-center text-marron loader-image-transparent"></x-ui.card>`;
        };

        handlePageEvent({
            games,
            activeFilter,
            onCreateElement: createElement,
        })
    });

    window.addEventListener('DOMContentLoaded', async () => {
        checkLabel();
        let listProvider = ['all'];
        let providerRecents = $('.js-provider-dropdown').data('recents');
        const queryParams = getQueryParamsFromSearchParams();

        try {
            const endpoinProvider = {
                "cong-game": "/game/provider",
                "song-bai-livecasino-truc-tuyen": '/casino/provider'
            }
            const { page, type } = getSlugsFromUrl();
            const endpoint = endpoinProvider[page] ?? endpoinProvider['cong-game'];
  
            const response = await fetchData(endpoint, {}, { useProxy: true }, '', '/api/v1');
            if (response.code === 200) {
                if(queryParams?.filter === 'recent') {
                    providerRecents = response.data?.recent || [];
                } else {
                    providerRecents = response.data?.[type || 'all'] || [];
                }
            }
        } catch (error) {
            console.log(error.message);
        }

        if(providerRecents){
            listProvider = [...listProvider, ...providerRecents.map(item => item.key)];
        }

        if (queryParams?.filter === 'recent' && providerRecents && providerRecents.length <= 0) {
            $('.js-provider-dropdown-container').addClass('hidden');
            games.forEach((game) => {
                if(game?.partner_provider && !listProvider.includes(game?.partner_provider)){
                    listProvider.push(game?.partner_provider);
                }
            });

            $('.js-provider-dropdown-container').addClass('hidden');
        } else {
            $('.js-provider-dropdown-container').removeClass('hidden');
        }
        if(listProvider.length <= 2){
            $('.provider-dropdown').addClass('hidden xl:hidden');
        } else {
            $('.provider-dropdown').removeClass('hidden xl:hidden');
            $('.provider-dropdown .dropdown-list-wrap ul li').each(function(){
                const providerValue = $(this).data("value");
                if(!listProvider.some(item => item.toLowerCase() === providerValue.toLowerCase())){
                    $(this).addClass('hidden');
                } else {
                    $(this).removeClass('hidden');
                }
            })
        }
        // if(activeFilter?.filter =='recent'){
        //     let listProvider = ['all'];
        //     const providerRecents = $('.js-provider-dropdown').data('recents');
        //     if(providerRecents){
        //         listProvider = [...listProvider, ...providerRecents.map(item => item.key)];
        //     }

        //     if (providerRecents && providerRecents.length <= 0) {
        //         games.forEach((game) => {
        //             if(game?.partner_provider && !listProvider.includes(game?.partner_provider)){
        //                 listProvider.push(game?.partner_provider);
        //             }
        //         });
        //     }

        //     if(listProvider.length <=2){
        //         $('.provider-dropdown').addClass('hidden xl:hidden');
        //     } else {
        //         $('.provider-dropdown').removeClass('hidden xl:hidden');
        //         $('.provider-dropdown .dropdown-list-wrap ul li').each(function(){
        //             const providerValue = $(this).data("value");
        //             if(!listProvider.some(item => item.toLowerCase() === providerValue.toLowerCase())){
        //                 $(this).addClass('hidden');
        //             }
        //         })
        //     }
            
        // }else {
        //     $('.provider-dropdown').remove('hidden xl:hidden');
        //     $('.provider-dropdown .dropdown-list-wrap ul li').removeClass('hidden');
        // }
    })
</script>
@endpushOnce
