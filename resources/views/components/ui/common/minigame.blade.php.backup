@php
    $token = '';
    if (\Illuminate\Support\Facades\Auth::check()) {
        $user = \Illuminate\Support\Facades\Auth::user();
        if (isset($user) && isset($user->package_id) && $user->package_id !== 2) {
            $token = $user->tp_token;
        }
    }
    $partner = env('BRAND_NAME', '');

    $minigameUrl = '';
    if (Auth::check()) {
        $minigameUrl = env('MINIGAME_URL') ?? 'https://assets.vgjt.info/js/mn.js';
    }
@endphp
@if (!$token || $token === '')
    <div id="minigame-container" class="draggable-element mini-game touch-none">
        <div
            class="w-full h-full [inset:72vh_20px_auto_auto]">
            <div class="relative cursor-pointer">
                <img
                    src="{{asset('/asset/images/minigame/bg.avif')}}"
                    alt="minigame"
                    class="img-text cursor-pointer w-full h-full" />
                <img
                    src="{{asset('/asset/images/minigame/close.avif')}}"
                    alt="close"
                    class="btn-close js-minigame-close absolute top-[4%] right-[4%] z-10 h-[20%] lg:hover:scale-105 lg:hover:filter lg:hover:brightness-125" />
                <img
                    src="{{asset('/asset/images/minigame/mini.avif')}}"
                    alt="mini"
                    class="mini ping absolute top-[35%] -left-[5%] z-10 h-[30%]" />
                <img
                    src="{{asset('/asset/images/minigame/game.avif')}}"
                    alt="game"
                    class="game-text ping cursor-pointer absolute top-[60%] left-[5%] z-10 h-[22%]"
                />
            </div>
        </div>
    </div>
@else
    <c2-minigame
        token="{{ $token }}"
        partner="{{ $partner }}"
        pos="{{App\Helpers\DetectDeviceHelper::isMobile() ? '50vh 20px auto auto' : '80vh 70px auto auto'}}"
        class="mini-game-ct"
    ></c2-minigame>
@endif
@pushOnce('scripts')
    @vite('resources/js/mini-game.js')
    <script>
    
        function forceRerender() {
            const scriptId = 'vergopjt-script';
            const scriptList = document.querySelectorAll("script[type='text/javascript']");
            const convertedNodeList = Array.from(scriptList);
            const testScript = convertedNodeList.find(script => script.id === scriptId);
            
            if (testScript !== undefined) {
                testScript.parentNode.removeChild(testScript);
            }
    
            const script = document.createElement('script');
            script.id = scriptId;
            script.type = 'text/javascript';
            script.src = '{{ $minigameUrl }}';
            script.onload = function () {
                const miniGame = $('c2-minigame');

                if (miniGame) {
                    const dragItem = miniGame.find('.c2-mn-hover');

                    if (dragItem.length > 0) {
                        dragItem.find('img').on('click', function (e) {
                            const cookies = getCookies();
                            const user = JSON.parse(cookies.user || "{}");
                            
                            const overlay = miniGame.find('div').filter(function () {
                                return $(this).attr('style')?.includes('background: transparent');
                            });


                            if (user?.package_id == 2) {
                                e.stopPropagation();
                                e.preventDefault();

                                
                                if (overlay.length > 0) {
                                    overlay[0].click();
                                }

                                openNotiModal(
                                    "Game không chơi được",
                                    'Bạn đang tham gia khuyến mãi không được phép chơi trò chơi này',
                                    "Trang chủ",
                                    "Xem khuyến mãi",
                                    "/asset/images/popup/img-can-not-play-game.avif",
                                    () => {
                                        window.location.href = "/";
                                    },
                                    () => {
                                        window.location.href = "/account/promotion";
                                    }
                                );

                            } else {
                                if (user?.is_updated_fullname == 0) {
                                e.stopPropagation();
                                e.preventDefault();

                                if (overlay.length > 0) {
                                    overlay[0].click();
                                }

                                openChangeName();
                                }
                            }
                        })
                    }
                }
             
            };

            document.body.appendChild(script);
        }
    
        document.addEventListener('DOMContentLoaded', function() {
            forceRerender();
        });
    </script>
@endPushOnce