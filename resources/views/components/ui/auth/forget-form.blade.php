<div class="forget-form h-full w-screen md:max-w-[440px]">
    <div class="form-modal__background absolute top-0 left-0 block aspect-[390/294] min-w-full md:hidden">
        <img alt="forget-form" src="{{ asset('asset/images/auth/login-mb.avif') }}" class="min-w-full h-full">
        <button 
            class="absolute top-[12px] right-[12px] w-[24px] h-[24px] bg-auth-close rounded flex items-center justify-center"
            onclick="closeAuthModal()" 
            class="text-xl"
        >
        <img 
            alt="close" 
            src="{{ asset('asset/images/close.avif') }}" 
            class="w-[10px] h-[10px] object-cover"/>
        </button>
        <div class="flex flex-col gap-y-2 top-[6.1875rem] left-[22px] absolute">
            <img src="{{ asset('asset/images/brand/logo.svg') }}" alt="logo brand" class="w-[145px] aspect-[145/46]" />
            <p class="text-sm font-medium text-neutral-1000">Nhà cái trực tuyến hàng đầu</p>
        </div>
    </div>
    <div class="form-modal__wrap relative flex h-full rounded-t-[18px] overflow-hidden md:rounded-[18px]">
        <div class="relative z-0 flex flex-col items-center flex-grow py-[32px] px-[20px] bg-neutral md:p-[32px]">
            <div class="w-full">
                <div class="flex flex-col">
                    <p class="mb-[16px] text-[18px] leading-[26px] font-semibold text-neutral-1000 uppercase">Khôi Phục Mật Khẩu</p>
                    <form class="flex flex-col gap-[24px]" id="forget-form-modal">
                        <x-kit.input 
                            noBackground
                            inPopup 
                            label="Email" 
                            isRequire 
                            class="forget-pass-email" 
                            placeholder=__('auth.nhap_dia_chi_email_cua_ban') 
                            id="forget-email" 
                            inputClassName="xl:placeholder:!text-neutral-400 xl:placeholder:!text-neutral-600"
                            name="email">
                        </x-kit.input>
                      
                        <x-kit.button 
                            disabled 
                            class="button-submit w-full rounded-full !transform-none !duration-0" 
                            style="filled" 
                            buttonType="submit" 
                            type="primary" 
                            size="large">
                            Gửi Ngay
                        </x-kit.button>
                        <ul class="forget-pass-note hidden h-[104px] flex-col gap-[4px] pr-[12px] pl-[32px] pt-[12px] pb-[16px] list-disc rounded-[12px] border border-note bg-forget-pass-note">
                            <li class="leading-[18px]">
                                <p class="text-[12px] leading-[18px] text-neutral-1000">Chỉ dành cho khách hàng đã thực hiện định danh Telegram.</p>
                            </li>
                            <li class="leading-[18px]">
                                <p class="text-[12px] leading-[18px] text-neutral-1000">Chat /start với 
                                    <a href="{{ env('TELEGRAM_ID_BOT_URL') }}" target="_blank" class="text-primary-700">
                                        BOT Telegram
                                    </a> hoặc liên hệ 
                                    <button 
                                        onclick="openLiveChatPopup()" 
                                        class="text-primary-700">
                                            Livechat
                                    </button> 
                                    24/7 nếu bạn không nhận được OTP.
                                </p>
                            </li>
                        </ul>
                        <p class="text-center text-[12px] leading-[18px] text-neutral-800">
                            Trở lại 
                            <a class="text-warning-700 cursor-pointer font-medium" onclick="openLogin()">
                                Đăng Nhập
                            </a>
                        </p>
                    </form>
                </div>               
            </div>
        </div>
        <button 
            class="absolute top-[10px] right-[10px] z-[1] hidden items-center justify-center w-[32px] h-[32px] bg-neutral-100 hover:bg-neutral-200 rounded-full md:flex"
            onclick="closeAuthModal()" 
            class="text-xl"
        >
            <img 
                alt="close" 
                src="{{ asset('asset/images/close-gold.avif') }}" 
                class="w-[8px] aspect-square"/>
        </button>
    </div>
</div>

@push('scripts')
    <script>
        function openLiveChatPopup() {
            closeAuthModal();
            openLiveChat();
        }
    </script>
@endpush
