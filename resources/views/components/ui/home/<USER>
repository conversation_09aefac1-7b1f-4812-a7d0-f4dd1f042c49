<div class="px-[36px] pt-[32px] pb-[54px] rounded-2xl bg-cover bg-center bg-[url('/public/asset/images/home/<USER>/bg-instruct.avif')]">
    <p class="font-semibold leading-[26px] mr-auto text-[18px] text-neutral-950 capitalize xl:leading-[43px] xl:text-[36px]__('home.huong_span_class')text-secondary-400">dẫn</span></p>
    <div class="max-w-full mt-[31px]">
        <a href="/huong-dan-nap-tien" class="flex items-center gap-x-[15px] mb-[16px] w-max group">
            <div class="flex items-center gap-x-[20px] min-w-[210px]">
                <img
                    src="{{ asset('asset/images/home/<USER>/icon-deposit.avif') }}"
                    alt="hdnt"
                    class="size-[40px] group-hover:opacity-80 transition-opacity">
                <p class="text-neutral-1200 text-base leading-[18px] font-semibold">Hướng dẫn nạp tiền</p>
            </div>
            <img
                src="{{ asset('asset/images/home/<USER>/icon-arrow.svg') }}"
                alt="hdnt"
                class="size-[33px] group-hover:opacity-80 transition-opacity">
        </a>
        <a href="/huong-dan-rut-tien" class="flex items-center gap-x-[15px] mb-[26px] w-max group">
            <div class="flex items-center gap-x-[20px] min-w-[210px]">
                <img
                    src="{{ asset('asset/images/home/<USER>/icon-withdraw.avif') }}"
                    alt="hdrt"
                    class="size-[40px] group-hover:opacity-80 transition-opacity">
                <p class="text-neutral-1200 text-base leading-[18px] font-semibold">Hướng dẫn rút tiền</p>
            </div>
            <img
                src="{{ asset('asset/images/home/<USER>/icon-arrow.svg') }}"
                alt="hdrt"
                class="size-[33px] group-hover:opacity-80 transition-opacity">
        </a>
        <a href="/huong-dan-giao-dich-p2p" class="text-neutral-1650 text-sm leading-[18px] font-semibold border border-neutral-1700 rounded-[50px] w-[97px] h-[36px] flex items-center justify-center hover:opacity-80 transition-opacity">Xem Thêm</a>
    </div>
</div>
