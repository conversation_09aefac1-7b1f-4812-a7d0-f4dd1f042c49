@props([
'swiperConfig' => null,
'list' => [],
'class' => '',
])

@php
    $listBanner = $list;
    $listHeroBanner = request()->get('listHeroBanner');

    if ($listHeroBanner && count($listHeroBanner) > 0) {
        $listBanner = $listHeroBanner;
    }
@endphp

@if (!empty($swiperConfig) && count($listBanner) > 0)
<div class="hero-banner pt-[8px] pb-[12px] px-[10px] xl:pb-0 {{ $class }}" x-data="{}">
    <div class="hero-banner__wrapper max-xl:!px-0 max-xl:aspect-[370/110] max-xl:overflow-hidden">
        <x-kit.swiper :swiperConfig="$swiperConfig" swiperRequiredClass="heroSwiper">
            @foreach ($listBanner as $key =>$item)
            @if ($loop->first)
            {{-- This is the first iteration --}}
            @pushOnce('preloadLink')
                @if (isset($item['image']))
                    @if (is_array($item['image']))
                        <link rel="preload" as="image" href="{{ asset($item['image'][app()->getLocale()]) }}" type="image/webp">
                    @else
                        <link rel="preload" as="image" href="{{ asset($item['image']) }}" type="image/webp">
                    @endif
                @endif
            @endPushOnce
            @endif
            <div class="swiper-slide">
                @if (isset($item['checkAuth']) && $item['checkAuth'] && !Auth::check())
                <span aria-label="hero-banner" onclick="openLogin()" class="cursor-pointer">
                    <div class="xl:container">
                        <picture>
                            <source media="(min-width: 1200px)" srcset="{{ asset(is_array($item['image']) ? $item['image'][app()->getLocale()] : $item['image']) }}" type="image/webp">
                            <img src="{{ asset(path: is_array($item['image-mb']) ? $item['image-mb'][app()->getLocale()] : $item['image-mb']) }}"
                                class="w-full aspect-[370/110] rounded-[8px] xl:rounded-none xl:aspect-[1240/538]"
                                alt="hero-banner" loading="{{ $key !== 0 ? 'lazy' : 'eager'}}" />
                        </picture>
                    </div>
                </span>
                @elseif (isset($item['type']) && $item['type']== 'game')
                {{-- This is a game type banner --}}
                <a href="{{ $item['link'] .'?token='. (Auth::user()->tp_token ?? '') }}" aria-label="hero-banner" class="cursor-pointer"
                     target="{{ App\Helpers\DetectDeviceHelper::isMobile() ? '_blank' : '_self' }}">
                    <div class="xl:container">
                        <picture>
                            <source media="(min-width: 1200px)" srcset="{{ asset(is_array($item['image']) ? $item['image'][app()->getLocale()] : $item['image']) }}" type="image/webp">
                            <img src="{{ asset(path: is_array($item['image-mb']) ? $item['image-mb'][app()->getLocale()] : $item['image-mb']) }}"
                                class="w-full aspect-[370/110] rounded-[8px] xl:rounded-none xl:aspect-[1240/538]"
                                alt="hero-banner" loading="{{ $key !== 0 ? 'lazy' : 'eager'}}" />
                        </picture>
                    </div>
                </a>
                @else

                    @if (isset($item['type']) && $item['type'] == 'final-club-world-cup-event')
                        <x-ui.home.final-club-world-cup-banner></x-ui.home.final-club-world-cup-banner>
                    @elseif (isset($item['type']) && $item['type'] == 'reward-event')
                        <x-ui.home.reward-event-banner></x-ui.home.reward-event-banner>
                    @elseif (isset($item['type']) && $item['type'] == 'techplay')
                        <x-ui.home.techplay-banner></x-ui.home.techplay-banner>
                    @else
                      <a href="{{ $item['link'] }}" aria-label="hero-banner" class="cursor-pointer">
                            <div class="xl:container">
                                <picture>
                                    <source media="(min-width: 1200px)" srcset="{{ asset(is_array($item['image']) ? $item['image'][app()->getLocale()] : $item['image']) }}" type="image/webp">
                                    <img src="{{ asset(path: is_array($item['image-mb']) ? $item['image-mb'][app()->getLocale()] : $item['image-mb']) }}"
                                        class="w-full aspect-[370/110] rounded-[8px] xl:rounded-none xl:aspect-[1240/538]"
                                        alt="hero-banner" loading="{{ $key !== 0 ? 'lazy' : 'eager'}}" />
                                </picture>
                            </div>
                        </a>
                    @endif
                @endif
            </div>
            @endforeach
        </x-kit.swiper>
    </div>
</div>
@endif