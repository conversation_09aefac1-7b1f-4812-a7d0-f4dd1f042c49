<!-- type: base/bank/your-bank/bank-account -->

@props([
    'placeholder' => '',
    'placeholderIcon' => null,
    'search' => false,
    'type' => 'base',
    'options' => [],
    'defaultValue' => null,
    'class' => '',
    'name' => '',
    'label' => '',
    'isRequire' => false,
    'id' => '',
    'isShowAddBank' => 'false',
    'itemClass' => '',
    'isHasIcon' => false,
    'classValue' => '',
    'activeClass' => '',
    'dropdownListClass' => '',
    'dropdownContainerClass' => ''
])

@php
    $dropdownType = config('type.dropdownType');
    $defaultLabel = '';
    $default = '';
    if (is_string($defaultValue)) {
        $options = array_filter($options, function ($option) use ($defaultValue) {
            return $option['value'] === $defaultValue;
        });
        $defaultLabel = array_pop($options)['label'] ?? $defaultValue;
        $default = array_pop($options)['value'] ?? $defaultValue;
    } else if (isset($defaultValue->label)) {
        $defaultLabel = $defaultValue->label;
        $default = $defaultValue->value;
    } else if (isset($defaultValue['label'])) {
        $defaultLabel = $defaultValue['label'];
        $default = $defaultValue['value'];
    } else {
        $defaultLabel = $placeholder;
    }
@endphp

<div class="dropdown-container flex flex-col gap-[8px] w-full [&.error-validate_.dropdown-button]:border-danger-500 [&:has(.selected)_.dropdown-icon-other]:bg-secondary-500 {{ $dropdownContainerClass }}" data-type="{{ $type }}" placeholderIcon="{{ $placeholderIcon }}">
    @if ($label)
        <p class="text-[12px] leading-[18px] font-normal text-neutral-850">
            {{ $label }}
        </p>
    @endif

    <div class="dropdown-wrapper relative w-full">
        <div class="flex items-center gap-[8px]">
            <button 
                type="button"
                class='dropdown-button flex items-center justify-between max-[385px]:gap-1 gap-[8px] flex-1 h-[36px] xl:h-[40px] border border-transparent p-[8px] [.active_&]:border-info-500 rounded-[8px] bg-neutral-50 xl:py-[10px] xl:px-[12px]'
            >
                @if ($type === $dropdownType['base'])
                    <i  class="dropdown-icon-base hidden xl:inline text-[20px] {{ isset($defaultValue->icon) ? 'icon-' . strtolower($defaultValue->icon) . ' text-primary-500' : $placeholderIcon . ' text-neutral-600' }}"></i>
                @elseif ($type === $dropdownType['bank'])
                    <img alt="bank dropdown" class="dropdown-image-bank hidden w-[20px] h-[20px] rounded-full"/>
                    @if ($placeholderIcon)
                        <div class="dropdown-icon-bank flex justify-center items-center w-[20px] h-[20px] rounded-full bg-neutral-600 text-neutral">
                            <i class="text-[15px] {{ $placeholderIcon }}"></i>
                        </div>
                    @endif
                @else
                    <div class="dropdown-icon-other flex justify-center items-center w-[20px] h-[20px] rounded-full text-neutral bg-neutral-600">
                        <i class="text-[15px] {{ $placeholderIcon }}"></i>
                    </div>
                @endif
    
                <p class="dropdown-text max-[393px]:tracking-[-0.5px] flex-1 text-[12px] whitespace-nowrap xl:text-sm leading-[18px] flex items-center gap-1 font-normal text-neutral-1000 text-left {{ $classValue }}">
                    {{ $defaultLabel }}</p>
                <i class="dropdown-arrow icon-arrow-down text-[20px] text-neutral-600 [.active_&]:rotate-180"></i>
            </button>
    
            @if ($search)
                <x-kit.search-input placeholder="Tìm Kiếm" class="dropdown-search-mobile hidden" showClose></x-kit.search-input>
                <button class="dropdown-open-search flex items-center justify-center w-[40px] h-[40px] rounded-[8px] bg-neutral-150 xl:hidden" type="button">
                    <i class="text-[23px] block text-neutral-600 icon-search [.active-search_&]:hidden"></i>
                    <i class="text-[23px] hidden text-neutral-600 icon-close [.active-search_&]:block"></i>
                </button>
            @endif
        </div>
        <div class="dropdown-list absolute top-[calc(100%_+_4px)] left-0 translate-y-1 z-[7] hidden flex-col w-full bg-neutral border border-neutral-150 rounded-lg shadow-shadow-4px overflow-hidden pr-1 box-border {{$dropdownListClass}}">
            @if ($search)
                <div class="hidden p-3 xl:block">
                    <x-kit.search-input class="search-mobile" placeholder="Tìm Kiếm" showClose></x-kit.search-input>
                </div>
            @endif
    
            <div @class([
                'dropdown-list-wrap overflow-y-auto scrollbar',
                $type === $dropdownType['yourBank'] ? 'max-h-[416px]' : 'max-h-[396px]',
            ])>
                <ul>
                    @foreach ($options as $item)
                        @php
                            $item = is_array($item) ? (object) $item : $item;
                        @endphp
                        <li data-value="{{ $item->value }}" data-label="{{ $item->label }}"
                            data-icon="{{ isset($item->icon) ? $item->icon : '' }}" class="group dropdown-item h-[44px] {{ $itemClass }}"
                            data-image="{{ isset($item->image) ? $item->image : '' }}"
                            data-status="{{ isset($item->bank_status) ? $item->bank_status : '' }}"
                            data-account-name="{{ isset($item->bank_account_name) ? $item->bank_account_name : '' }}"
                            data-account-no="{{ isset($item->bank_account_no) ? $item->bank_account_no : '' }}"
                            class="{{ $classValue === $item->value ? 'selected' : '' }}"
                            >
                            <button type="button" data-value="{{ $item->value }}" value="{{ $item->value }}"
                                data-label="{{ $item->label }}" data-icon="{{ isset($item->icon) ? $item->icon : '' }}"
                                data-image="{{ isset($item->image) ? $item->image : '' }}" 
                                @class([
                                    'flex w-full justify-between p-3 cursor-pointer items-center text-[12px] leading-[18px] xl:text-sm xl:group-hover:bg-neutral-50 w-full h-[44px] ',
                                    $class,
                                    $default === $item->value ? $activeClass : '',
                                ])>
                                <div class="flex gap-2 pointer-events-none items-center h-[20px]">
                                    @if ($type === $dropdownType['base'])
                                        @if ($item->icon)
                                            <i class="icon-{{ strtolower($item->icon) }} text-neutral-600 text-[20px]"></i>
                                        @endif
                                    @elseif ($type === $dropdownType['bank'])
                                        <img src="{{ $item->image }}" alt="bank dropdown" class="w-[20px] h-[20px]" />
                                    @else
                                        <div
                                            class="flex justify-center items-center w-[20px] h-[20px] rounded-full bg-neutral-600 text-neutral group-hover:bg-primary-500 [.selected_&]:bg-primary-500">
                                            <i class="text-[15px] {{ $placeholderIcon }}"></i>
                                        </div>
                                    @endif
    
                                    <div class="flex flex-col gap-1">
                                        <span @class([
                                            'dropdown-label text-xs xl:text-sm leading-[20px] font-medium text-left [.selected_&]:text-primary-700',
                                            $type === $dropdownType['yourBank']
                                                ? 'text-neutral-1000'
                                                : 'text-neutral-800',
                                        ]) value="{{ $item->value }}"
                                            data-value="{{ $item->value }}">
                                            {{ $item->label }}
                                        </span>
                                        @if ($type === $dropdownType['yourBank'])
                                            <div
                                                class="flex item-center gap-2 text-[12px] leading-[18px] font-nornal text-neutral-600">
                                                <span>{{ $item-> bank_account_no }}</span>
                                                <span class="w-[1px] h-[18px] bg-neutral-150"></span>
                                                <span>{{ $item-> bank_account_name }}</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
    
                                @if ($type === $dropdownType['yourBank'])
                                    <x-kit.state :label="$item -> bank_txt" :style="$item -> bank_status === 1 ? 'warning' :'success'"></x-kit.state>
                                @endif
                            </button>
                        </li>
                    @endforeach
                </ul>
            </div>
    
            @if ($type === $dropdownType['yourBank'] && $isShowAddBank)
                <div class="dropdown-add-bank flex items-center gap-2 py-[10px] px-3 bg-neutral shadow-footer-button-dropdown cursor-pointer">
                    <x-kit.button style='filled' type="tertiary" size="small" symbol="icon-plus"></x-kit.button>
                    <p class="text-[14px] leading-[20px] font-normal text-neutral-800">Thêm tài khoản</p>
                </div>
            @endif
        </div>
    </div>
    <input class="dropdown-select input-field absolute opacity-0 pointer-events-none"  field-type='select' value="{{ $default }}" name="{{ $name }}" data-id="{{ $id }}"/>

    @if ($isRequire)
        <span class="input-error text-[12px] hidden leading-[18px] font-normal text-danger-600"></span>
    @endif
</div>
