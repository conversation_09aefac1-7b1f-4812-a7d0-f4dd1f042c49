// Reward Event
const bindRewardEventModal = () => {
    const eventModal = $("#reward-event-modal");
    const eventShow = eventModal.find('.reward-event-modal-show');
    const eventClose = eventModal.find('.reward-event-modal-close');
    const eventDeposit = eventModal.find('.reward-event-modal-deposit');

    const handleShowEventModal = () => {
        if (!eventShow.is(':checked')) {
            const now = Date.now();
            const futureTimestamp = now + 48 * 60 * 60 * 1000;
            localStorage.setItem('timeShowRewardEventModal', futureTimestamp.toString());  
        }
    }

    eventClose.on('click', function () {
        handleShowEventModal();
        closeModal();
        window.handleOpenListPopup();
    })

    eventDeposit.on('click', function () {
        handleShowEventModal();
        window.location.href = '/san-thuong-gio-vang';
    })

    eventShow.on('change', function () {
        if (eventShow.is(':checked')) {
            localStorage.setItem('hiddenRewardEventModal', 'true'); 
        } else {
            localStorage.removeItem('hiddenRewardEventModal'); 
        }
    })
}

const checkRewardGoldenHour = async () => {
    try {
        const { status, data } = await fetchData('/lixi/newyear', {}, "", "", "");

        if (status === 'OK') {
            return data.check;
        }
        return false;
    } catch (error) {
        return false
    }

}

window.closeResultRewardModal = () => {
    if (window.location.pathname === '/san-thuong-gio-vang') {
        window.location.reload();
    } else {
        closeModal();
        window.handleOpenListPopup();
    }
}

const claimRewardGoldenHour = async () => {
    $('body').addClass('pointer-events-none');
    $('.reward-event-open-button').addClass('hidden').removeClass('block');
    $('.reward-event-loading').addClass('block').removeClass('hidden');
    try {
        const res = await submitData('/lixi/newyear', {}, "", "", "");
        if (res?.status === 'OK') {
            openModal(rewardGoldenHourResultModal, true, 'reward-golden-hour-result-modal');

            $('.js-reward-money').text(`${res?.amount_txt} VND`);
            $('body').removeClass('pointer-events-none');
        } else {
            openModal(rewardGoldenHourExpireModal, true, 'reward-golden-hour-expire-modal');
        }
        $('body').removeClass('pointer-events-none');
        $('.reward-event-open-button').addClass('block').removeClass('hidden');
        $('.reward-event-loading').addClass('hidden').removeClass('block');
    } catch (error) {
        closeModal();
        window.handleOpenListPopup();
        $('body').removeClass('pointer-events-none');
        $('.reward-event-open-button').addClass('block').removeClass('hidden');
        $('.reward-event-loading').addClass('hidden').removeClass('block');
    }
}

const handleGiftRewardPopup = async () => {
    const check = await checkRewardGoldenHour();
    if (check && rewardGoldenHourGiftModal) {
        window.claimRewardGoldenHour = claimRewardGoldenHour;
        openModal(rewardGoldenHourGiftModal, true, 'reward-golden-hour-gift-modal');
    } else {
        window.handleOpenListPopup();
    }
}

// Top Racing Event
const bindTopRacingEventModal = () => {
    const eventModal = $("#top-racing-event-modal");
    const eventShow = eventModal.find('.top-racing-event-modal-show');
    const eventClose = eventModal.find('.top-racing-event-modal-close');
    const eventDeposit = eventModal.find('.top-racing-event-modal-deposit');

    const handleShowEventModal = () => {
        if (!eventShow.is(':checked')) {
            const now = Date.now();
            const futureTimestamp = now + 48 * 60 * 60 * 1000;
            localStorage.setItem('timeShowTopRacingEventModal', futureTimestamp.toString());  
        }
    }

    eventClose.on('click', function () {
        handleShowEventModal();
        closeModal();
        window.handleOpenListPopup();
    })

    eventDeposit.on('click', function () {
        handleShowEventModal();
        window.location.href = '/dua-top-tich-luy';
    })

    eventShow.on('change', function () {
        if (eventShow.is(':checked')) {
            localStorage.setItem('hiddenTopRacingEventModal', 'true'); 
        } else {
            localStorage.removeItem('hiddenTopRacingEventModal'); 
        }
    })
}

//final club world cup
const bindFinalClubWorldCupEventModal = () => {
    const eventModal = $("#final-club-world-cup-event-modal");
    const eventShow = eventModal.find('.final-club-world-cup-event-modal-show');
    const eventClose = eventModal.find('.final-club-world-cup-event-modal-close');
    const eventButton = eventModal.find('.final-club-world-cup-event-modal-button');


    eventClose.on('click', function () {
        closeModal();
        window.handleOpenListPopup();
    })

    eventShow.on('change', function () {
        if (eventShow.is(':checked')) {
            const now = Date.now();
            const futureTimestamp = now + 24 * 60 * 60 * 1000;
            localStorage.setItem('timeShowFinalClubWorldCupEventModal', futureTimestamp.toString());  
        } else {
            localStorage.removeItem('timeShowFinalClubWorldCupEventModal');  
        }
    });

    const showInfoModal = function () { 
        openModal(finalClubWorldCupInfoModal, false, 'final-club-world-cup-info');
    }

    window.showFinalClubWorldCubInfoModal = showInfoModal;

    eventButton.on('click', function () {
        showInfoModal();
    })
}

const handleOpenGiftFinalClubWorldCupPopup = async (id='',money = 0) => {
    const giftMoney = $('.js-final-club-money');
    const closeGiftModal = $('.js-final-club-close');
    giftMoney.text(`${money}`);

    closeGiftModal.on('click', async function () {
        try {
            await submitData(`/popup/close/${id}`, {}, "", "");
            closeModal();
            window.handleOpenListPopup();
        } catch (e) {
            closeModal();
            window.handleOpenListPopup();
        }
    })
}

const parseDateDMY = (dateStr, timeStr = "00:00:00") => {
    const [day, month, year] = dateStr.split('/');
    return new Date(`${year}-${month}-${day}T${timeStr}`);
}

const isWithinDateRange = (inputDateStr, startDateStr, endDateStr) => {
    const inputDate = new Date(inputDateStr);
    const startDate = parseDateDMY(startDateStr, "00:00:00");
    const endDate = parseDateDMY(endDateStr, "23:59:59.999");

    return inputDate >= startDate && inputDate <= endDate;
}

const handleOpenGiftTopRacingPopup = async (id = "", money = 0, date = "") => {
    const closeGiftModal = $('.top-racing-close-modal');
    const closeGiftButton = $('.top-racing-close-button');
    const racingGift = $('.top-racing-gift');
    const racingContent1 = $('.top-racing-content-1');
    const racingContent2 = $('.top-racing-content-2');

    racingGift.text(`${formatMoney(money)} VND`);

    closeGiftModal.on('click', async function () {
        try {
            await submitData(`/popup/close/${id}`, {}, "", "");
            closeModal();
            window.handleOpenListPopup();
        } catch (e) {
            closeModal();
            window.handleOpenListPopup();
        }
    })

    closeGiftButton.on('click', async function () {
        try {
            await submitData(`/popup/close/${id}`, {}, "", "");
            window.location.href = '/cong-game';
        } catch (e) {
            window.location.href = '/cong-game';
        }
    })

    if (listTime?.length > 0) {
        const time = listTime.find((item) => {
            return isWithinDateRange(date, item?.start, item?.end)
        })

        if (time) {
            racingContent1.text(`Đua Top Tích Lũy Vòng ${time?.key}:`)
            racingContent2.text(`${time?.start} → ${time?.end}.`)
        }
    }
}

const parseDateFromDDMMYYYY = (dateStr) => {
    const [day, month, year] = dateStr.split('/');

    return new Date(`${year}-${month}-${day}`);
}

const sortByNearestStartTime = (array) => {
    const now = new Date();

    return array.sort((a, b) => {
        if (a?.idModal === 'final-club-world-cup-event-modal' && b?.idModal !== 'final-club-world-cup-event-modal') {
            return -1; // final-club-world away first
        }
        if (b?.idModal === 'final-club-world-cup-event-modal' && a?.idModal !== 'final-club-world-cup-event-modal') {
            return 1; // final-club-world away first
        }

        const dateA = parseDateFromDDMMYYYY(a?.startTime);
        const dateB = parseDateFromDDMMYYYY(b?.startTime);

        const diffA = Math.abs(dateA - now);
        const diffB = Math.abs(dateB - now);

        return diffA - diffB;
    });
};


const handleGiftPopup = async () => {
    if(listGiftPopupApi.length == 0){
        const popupRes = await fetchData('/popup', "", {}, "", "");
    
        if (popupRes?.status === "OK") {
            const listPopup = popupRes?.data ?? [];
            const listGiftEvent = listPopup?.filter((item) => item.is_show);
            window.listGiftPopupApi = [...listGiftEvent];
    
            window.handleOpenListPopup();
        } else {
            window.handleOpenListPopup();
        }

    } else {
        window.handleOpenListPopup();
    }
}

function handleShowGiftPopup (data) {
    if (data.method === "top_racing" ) {
        openModal(topRacingEventGiftModal, true, 'top-racing-gift-modal');
        handleOpenGiftTopRacingPopup(data?.id, data?.amount, data?.created_time);

    } else if(data.method === "cashback_final_fifa") {
        openModal(finalClubWorldCupGiftModal, true, 'final-club-world-cup-gift'); 
        handleOpenGiftFinalClubWorldCupPopup(data?.id, data?.amount_txt || data?.amount || 0)
    }
}
// General Event Handle
window.listWelcomePopup = [];
window.listGiftPopup = [];

window.listGiftPopupApi = [];

window.handleOpenListPopup = async () => {
    if (window.listWelcomePopup.length > 0) {
        const handle = window.listWelcomePopup[0];
        window.listWelcomePopup.shift();
        handle();
    } else {
        if (window.listGiftPopup.length > 0) {
            const handle = window.listGiftPopup[0];
            window.listGiftPopup.shift();
            await handle();
        } else if (window.listGiftPopupApi.length > 0) {
            const data = window.listGiftPopupApi[0];
            window.listGiftPopupApi.shift();
            handleShowGiftPopup(data);
        } 
    }
}

window.addEventListener('DOMContentLoaded', async function () {
    const pathname = window.location.pathname;
    const isKSport = pathname === '/ca-do-bong-da/ksports';

    const listEvent = [
        {
            valid: !isKSport ? window.validFinalClubWorldCupEvent : false,
            hidden: 'hiddenFinalClubWorldCupEventModal',
            timeShow: 'timeShowFinalClubWorldCupEventModal',
            modal: finalClubWorldCupModal,
            idModal: 'final-club-world-cup-event-modal',
            handleModal: bindFinalClubWorldCupEventModal,
            startTime: startFinalClubWorldCupEvent ?? "",
            handleGiftPopup: handleGiftPopup,
            isSticky:false
        },
        {
            valid: window.validRewardEvent ?? false,
            hidden: 'hiddenRewardEventModal',
            timeShow: 'timeShowRewardEventModal',
            modal: rewardEventModal,
            idModal: 'reward-event-modal',
            handleModal: bindRewardEventModal,
            startTime: startRewardEvent ?? "",
            handleGiftPopup: handleGiftRewardPopup,
            isSticky:true
        },
        {
            valid: window.validTopRacingEvent ?? false,
            hidden: 'hiddenTopRacingEventModal',
            timeShow: 'timeShowTopRacingEventModal',
            modal: topRacingEventModal,
            idModal: 'top-racing-event-modal',
            handleModal: bindTopRacingEventModal,
            startTime: startTopRacingEvent ?? "",
            handleGiftPopup: handleGiftPopup,
            isSticky:true
        },

    ];

    const sortListEvent = sortByNearestStartTime(listEvent);

    sortListEvent.forEach((item) => {
        if (item?.valid) {
            const handleOpenWelcome = () => {
                const hidden = localStorage.getItem(item?.hidden);
                const timeShow = localStorage.getItem(item?.timeShow);

                if (!hidden) {
                    if (!timeShow) {
                        openModal(item?.modal, item.isSticky, item?.idModal);
                        item?.handleModal();
                    } else {
                        const now = Date.now();

                        if (now > Number(timeShow)) {
                            openModal(item?.modal, item.isSticky, item?.idModal);
                            item?.handleModal();
                        } else {
                            window.handleOpenListPopup();
                        }
                    }
                } else {
                    window.handleOpenListPopup();
                }
            }

            window.listWelcomePopup.push(() => handleOpenWelcome());
            const cookies = getCookies();

            if (cookies && cookies.user) {
                window.listGiftPopup.push(item?.handleGiftPopup);
            }
        }
    })

    window.handleOpenListPopup();
})
