document.addEventListener('DOMContentLoaded', () => {
    if (screen.width < 1200) {
        const chartItem = $('.chart-item');
        const promotionContainer = $('.promotion-container');
        const promotionDetailWrap = $('.promotion-detail-wrap');
        const promotionClose = $('.promotion-close');
        const promotionTitle = $('.promotion-title');
        const promotionBet = $('.promotion-bet');
        const promotionRefund = $('.promotion-refund');

        const handleClose = () => {
            promotionContainer.removeClass('flex').addClass('hidden');
            $('body').removeClass('overflow-hidden');
            $('body').removeClass('no-scroll');
            $('body').removeClass('block-scroll');
            chartItem.removeClass('active');
        }

        promotionClose.on('click', function () {
            handleClose();
        })

        chartItem.on('click', function () {
            const { date, bet, refund } = $(this).data();

            const [day, month] = date?.split('/')

            promotionBet.text(bet);
            promotionRefund.text(refund);

            promotionTitle.text(`Hoàn trả ngày ${day}/${month}`);
            $(this).addClass('active');
            $('body').addClass('overflow-hidden');
            $('body').addClass('no-scroll');
            $('body').addClass('block-scroll');
            promotionContainer.removeClass('hidden').addClass('flex');
            promotionDetailWrap
                .css({ bottom: '-100%' }) 
                .animate({ bottom: '0' }, 200);
        });

        $(document).on('click', function (event) {
            const target = $(event.target);

            if (!target.hasClass('chart-item') && $(promotionDetailWrap).has(target).length === 0 && !target.hasClass('promotion-detail')) {
                handleClose();
            }
        })
    }
})
