document.addEventListener("DOMContentLoaded", () => {
    const buttonStatus = $('.js-status-button');

    const copyText = () => {
        // Copy button handler
    $('.js-copy-btn').on('click', function() {
        const $btn = $(this);
        const field = $btn.data('field');
        const $closestFlex = $(this).closest('.flex');

        const textElement = field === 'card_serial'
          ? $closestFlex.find('.js-status-serial')
          : $closestFlex.find('.js-status-code');
        
        const text = $.trim(textElement.text());

        if (!text) return;
        
        const textToCopy = field === 'amount' ? text.replace(/,/g, '') : text;
        
        navigator.clipboard.writeText(textToCopy)
            .then(() => {
                $('.js-copy-icon').removeClass('active');
                $btn.find('.js-copy-icon').addClass('active')
                
                setTimeout(() => {
                    $('.js-copy-icon').removeClass('active');
                }, 2000);
            })
            .catch(err => console.error('Failed to copy text: ', err));
        });
    }
    copyText();
    buttonStatus.on('click', function (event) {
        const target = $(this);
        const position = target.offset();
        const {top} = position;

        const scrollTop = $(window).scrollTop();

        if (top < scrollTop + 132 + 88 && !isMobile()) {
            $('.status-list-pc').addClass('status-history-reverse').removeClass('xl:bottom-full');
            $('.shadow-shadow-status-list').addClass('shadow-shadow-status-list-reverse').removeClass('top-full').removeClass('z-[1]');
        } else {
            $('.status-list-pc').removeClass('status-history-reverse').addClass('xl:bottom-full');
            $('.shadow-shadow-status-list').removeClass('shadow-shadow-status-list-reverse').addClass('top-full').addClass('z-[1]');
        }

        if (target.hasClass('status-button-mb')) {
            event.stopPropagation();
        }

        const id = target.data()?.id;

        const statusList = $(`.js-status-list-${id}`);
        const statusClose = statusList.find('.js-status-close');
        const statusOverlay = statusList.find('.js-status-overlay');
        const statusSerial = statusList.find('.js-status-serial');
        const statusCode = statusList.find('.js-status-code');
        const statusButton = statusList.find('.js-status-button');
        const statusSwiper = statusList.find('.swiper-wrapper ');

        const isSetClose = statusClose.attr('isSet');
        const isSetOverlay = statusOverlay.attr('isSet');

        if (typeof isSetClose === 'undefined' || isSetClose === false) {
            statusClose.attr('isSet', '');
            statusClose.on('click', function (event) {
                event.stopPropagation();
                handleStatusList()
            })
        }

        if (typeof isSetOverlay === 'undefined' || isSetOverlay === false) {
            statusOverlay.attr('isSet', '');
            statusOverlay.on('click', function (event) {
                event.stopPropagation();
                handleStatusList()
            })
        }

        statusButton.on('click', function () {
            const target = $(this).addClass('active');
            const {serial, code} = target.data();
            statusButton.removeClass('active');
            statusSerial.text(serial);
            statusCode.text(code);

            target.addClass('active');
        })

        statusList.on('click', function (event) {
            event.stopPropagation();
        })

        const handleStatusList = () => {
            statusSwiper.css("transform", "translate3d(0px, 0px, 0px)");

            setTimeout(() => {
                if (target.hasClass('status-button-active')) {
                    statusList.removeClass('active');
                    target.removeClass('status-button-active');
                    const {serial, code} = statusList.data();

                    statusSerial.text(serial);
                    statusCode.text(code);

                    statusButton.each(function (index, item) {
                        if (index === 0 || index === statusButton.length / 2) {
                            $(this).addClass('active');
                        } else {
                            $(this).removeClass('active');
                        }
                    })

                    if ($(window).width() < 1200) {
                        $('body').removeClass('mobile-overflow-hidden');
                    }

                } else {
                    statusList.addClass('active');
                    target.addClass('status-button-active');

                    if ($(window).width() < 1200) {
                        $('body').addClass('mobile-overflow-hidden');
                    }
                }
            });
        }

        handleStatusList();
        copyText();
    });

    if ($(window).width() >= 1200) {
        $(document).on('click', function (event) {
            var target = $(event.target);
            const statusButtonActive = $('.status-button-active');
            const statusListActive = $('.status-list-pc.active');
            if (statusButtonActive.length > 0) {
                if (!target.hasClass('status-button-active') && !$.contains(statusListActive[0], target[0])) {
                    statusButtonActive.removeClass('status-button-active');
                    statusListActive.removeClass('active');

                    const {serial, code} = statusListActive.data();

                    const statusSerial = statusListActive.find('.js-status-serial');
                    const statusCode = statusListActive.find('.js-status-code');
                    const statusButton = statusListActive.find('.js-status-button');

                    statusSerial.text(serial);
                    statusCode.text(code);

                    statusButton.each(function (index, item) {
                        if (index === 0) {
                            $(this).addClass('active');
                        } else {
                            $(this).removeClass('active');
                        }
                    })
                }   
            }
        });
    }
    $('.history-time').each(function () {
        const target = $(this);
        const { time } = target.data();

        const newTime = new Date(time);
        const formatTime = formatDateTime(newTime,"DD/MM/YYYY HH:mm");

        target.text(formatTime);
    })
})
