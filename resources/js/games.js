const openDocumentGame = (newTab, data) => {
    if (!newTab) {
        newTab = window.open("", "_blank");
    }
    newTab.document.open();
    newTab.document.write(data.url);
    newTab.document.close();
};

const iframeGames = [
    {
        key: 'lode',
        url: '/lode-3-mien',
        mobileUrl: 'https://games.vgjt.info/ld3m.html?style=blue&token=',
    },
    {
        key: 'lode_virtual',
        url: '/lo-de-sieu-toc',
        mobileUrl: 'https://m-web-rng.quayso.club/game/60?token=',
    },
    {
        key: 'mega645',
        url: '/mega645',
    },
    {
        key: 'power655',
        url: '/power655',
    }
]

async function openGame(game, changeName = false) {
    const PROVIDER_OPEN_HTML_TYPE = ["pgsoft"];
    const cookies = getCookies();
    const user = JSON.parse(cookies?.user || "{}");
    
    //check login
    if (!cookies || !cookies.user) {
        openLogin();
        return;
    }
    
    // check promo
    if ( (game?.deny_info && game?.deny_info !== "false") || game?.deny_info === "1" || user?.package_id == 2) {
        openNotiModal(
            "GAME KHÔNG CHƠI ĐƯỢC",
            "Bạn không thể chơi game này khi đang tham gia khuyến mãi.",
            "Trang chủ",
            "Xem khuyến mãi",
            "/asset/images/popup/img-can-not-play-game.avif",
            () => { window.location.href = "/"; },
            () => { window.location.href = "/account/promotion"; }
        );

        return;
    } 

    // check fullname
    if (user?.is_updated_fullname == 0 && !changeName) {
        openChangeName(false, game);
        return;
    }

    // check maintain
    if (game?.maintain === true || game?.maintain === "true") {
        openNotiModal(
            "Game đang bảo trì",
            "Game này hiện đang được bảo trì. <br /> Vui lòng chọn trò chơi khác.",
            "Trang chủ",
            "Sảnh game",
            "/asset/images/popup/img-game-maintainance.avif",
            () => { window.location.href = "/"; },
            () => {  window.location.href = type === "game" ? "/cong-game" : "/song-bai-livecasino-truc-tuyen"; }
        );

        return;
    }
  
    // handle iframe game
    if (game.partner_game_id && iframeGames.find(item => item.key === game.partner_game_id)) {
        const iframeGame = iframeGames.find(item => item.key === game.partner_game_id);

        if (iframeGame) {
            if (window.isMobile() && iframeGame.mobileUrl) {
                const user = cookies.user ? JSON.parse(cookies.user) : null;
                window.open(`${iframeGame.mobileUrl}${user?.tp_token || ''}`, "_blank")
            } else {
                window.open(iframeGame.url, "_blank")
            }
            return;
        } else {
            if (iframeGame) {
                if (window.isMobile() && iframeGame.mobileUrl) {
                    const user = cookies.user ? JSON.parse(cookies.user) : null;
                    window.open(`${iframeGame.mobileUrl}${user?.tp_token || ''}`, "_blank")
                } else {
                    window.open(iframeGame.url, "_blank")
                }
                return;
            }
        }
    }

    // handel open normal game
    let newTab = window.isSafari() ? window.open("", "_blank") : null;

    try {
        const type = game.type ? game.type : "game";
        let gameUrl = type === "game" ? "/gameUrl" : "/casinoUrl";
        let params = {
            partnerProvider: game.partner || '',
            partnerGameId: game.partner_game_id || '',
            name: game.name || '',
            image: game.image || '',
        };

        if (type !== "game") {
            params = {
                partnerGameId: game.partner_game_id,
                partnerCode: game.partner,
            };
        }

        if (game.api_url) {
            gameUrl = game.api_url;
            params = {};
        }  else if (game.partnerCode && game.id) {
            params = {
                partnerProvider: game.partnerCode || '',
                partnerGameId: game.id || '',
            };
        }

        if (game.game_api_url) {
            gameUrl = game.game_api_url;
            params = {};
        }

        const { status, data, message = "" } = await fetchData(gameUrl, params, {}, "");
        if (status === "OK") {
            const urlRegex = /^(https?:\/\/)?([a-z\d]([a-z\d-]*[a-z\d])*\.)+[a-z]{2,}/i;

            if (
                (!data.url && !data.url_mobile) ||
                ((!urlRegex.test(data.url) &&
                !urlRegex.test(data.url_mobile)) && game.partner !== 'pgsoft') ||
                data?.url?.code === 400 ||
                data?.url_mobile?.code === 400
            ) {
                openNotiModal(
                    "Game đang bảo trì",
                    "Game này hiện đang được bảo trì. <br /> Vui lòng chọn trò chơi khác.",
                    "Trang chủ",
                    "Sảnh game",
                    "/asset/images/popup/img-game-maintainance.avif",
                    () => { window.location.href = "/"; },
                    () => { window.location.href = type === "game" ? "/cong-game" : "/song-bai-livecasino-truc-tuyen"; }
                );

                newTab?.close();
                return;
            }
            
            let url = window.isMobile() ? (data.url_mobile || data.url) : data.url;

            if (game.game_api_url === "/lodeVirtualUrl") {
                url = url.replace("https://w", "https://m-w");
            }

            if (PROVIDER_OPEN_HTML_TYPE.includes(game.partner)) {
                openDocumentGame(newTab, data);
                return;
            } else {
                if (newTab) {
                    newTab.location.href = url;
                } else {
                    window.open(url, "_blank");
                }
            }
        } else if (status === "SHOW_MESSAGE") {
            openNotiModal(
                "Game không chơi được",
                message,
                "Trang chủ",
                "Xem khuyến mãi",
                "/asset/images/popup/img-can-not-play-game.avif",
                () => { window.location.href = "/"; },
                () => { window.location.href = "/account/promotion"; }
            );

            newTab?.close();
        } else if (!data.url) {
            openNotiModal(
                "Game đang bảo trì",
                "Game này hiện đang được bảo trì. <br /> Vui lòng chọn trò chơi khác.",
                "Trang chủ",
                "Sảnh game",
                "/asset/images/popup/img-game-maintainance.avif",
                () => { window.location.href = "/"; },
                () => { window.location.href = type === "game" ? "/cong-game" : "/song-bai-livecasino-truc-tuyen"; }
            );

            newTab?.close();
        }
        return;
    } catch (error) {
        useToast("error", error.message);
        
        newTab?.close();
    }
}

window.openGame = openGame;
