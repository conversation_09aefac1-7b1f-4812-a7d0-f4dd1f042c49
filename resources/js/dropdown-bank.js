const handleDropdownBank = () => {
    $('.dropdown-bank-container').each(function () {
        const isSet = $(this).attr('isSet');

        if (typeof isSet === 'undefined' || isSet === false) {
            $(this).attr('isSet', '');
            const target = $(this);
            const dropdownWrapper = target.find('.dropdown-bank-wrapper');
            const dropdownItems = target.find('.dropdown-bank-item');
            const dropdownImageBank = target.find('.dropdown-bank-image');
            const dropdownText = target.find('.dropdown-bank-text');
            const dropdownButton = target.find('.dropdown-bank-button');
            const searchContainer = target.find('.search-container');
            const dropdownBankClose = target.find('.dropdown-bank-close');
            const searchInput = searchContainer.find('.search-input');
            const searchRemove = searchContainer.find('.search-remove');
            const dropdownMain = target.find('.dropdown-bank-main');
            const dropdownSub = target.find('.dropdown-bank-sub');
            const dropdownEmpty = target.find('.dropdown-bank-empty');
            const dropdownOverlay = target.find('#overlay-dropdown-bank');
            const dropdownList = target.find('.dropdown-bank-list');

            const handleCloseDropdown = () => {
                dropdownList.animate(
                    { bottom: '-100%' }, 
                    10,
                    function () {
                        target.removeClass('active-drop');
                        dropdownItems.closest('li').removeClass('hidden');
                        searchInput.val('');
                        searchRemove.addClass('invisible');
                        handleShowEmpty(false);
                        dropdownOverlay.addClass('hidden');
                        dropdownList.addClass('hidden');

                        $('body').removeClass('mobile-overflow-hidden');
                    }
                );
            };

            const handleShowEmpty = (value) => {
                if (value) {
                    dropdownMain.addClass('hidden');
                    dropdownSub.addClass('hidden');
                    dropdownEmpty.removeClass('hidden');
                } else {
                    dropdownMain.removeClass('hidden');
                    dropdownSub.removeClass('hidden');
                    dropdownEmpty.addClass('hidden');
                }
            };

            dropdownBankClose.on('click', function () {
                handleCloseDropdown();
            });

            searchInput.on('input', function (event) {
                const search = event.target.value.toLowerCase();

                let isEmpty = true;

                dropdownItems.each(function () {
                    const { label } = $(this).data();

                    if (label.toLowerCase().includes(search)) {
                        $(this).closest('li').removeClass('hidden');
                        isEmpty = false;
                    } else {
                        $(this).closest('li').addClass('hidden');
                    }
                });

                const allSubItemsHidden = dropdownSub.find('ul li:not(.hidden)').length === 0;
                const allMainItemsHidden = dropdownMain.find('li:not(.hidden)').length === 0;
                
                handleShowEmpty(isEmpty);

                if (allMainItemsHidden) {
                    dropdownMain.addClass('hidden');
                } else {
                    dropdownMain.removeClass('hidden');
                }
                
                if (allSubItemsHidden) {
                    dropdownSub.addClass('hidden');
                } else {
                    dropdownSub.removeClass('hidden');
                }
            });

            searchRemove.on('click', function () {
                dropdownItems.each(function () {
                    $(this).closest('li').removeClass('hidden');
                });

                handleShowEmpty(false);
            });

            dropdownItems.each(function () {
                $(this).on('click', function () {
                    handleCloseDropdown();
                    const { label, image } = $(this).data();
                    dropdownText.text(label);
                    dropdownImageBank.attr('src', image);
                });
            });

            dropdownButton.on('click', function () {
                if (target.hasClass('active-drop')) {
                    handleCloseDropdown();
                } else {
                    setTimeout(() => {
                        dropdownSub.find('ul').scrollTop(0);

                        if (dropdownMain) {
                            dropdownMain.scrollTop(0);
                        }
                    })
                
                    target.addClass('active-drop');
                    dropdownOverlay.removeClass('hidden');
                    
                    $('body').addClass('mobile-overflow-hidden');
                  
                    dropdownList
                        .removeClass('hidden') 
                        .css({ bottom: '-100%' }) 
                        .animate({ bottom: '0' }, 10); 
                }
            });

            dropdownOverlay.on('click', function () {
                handleCloseDropdown();
            });
        }
    });
};

window.handleDropdownBank = handleDropdownBank;

window.addEventListener('DOMContentLoaded', () => {
    handleDropdownBank();

    $(document).on('click', function (event) {
        if (!$(event.target).closest('.dropdown-bank-container').length) {
            $('.dropdown-bank-container.active-drop').each(function () {
                const $container = $(this);
                const $list = $container.find('.dropdown-bank-list');
                const dropdownOverlay = $container.find('#overlay-dropdown-bank');

                $list.animate(
                    { bottom: '-100%' },
                    10,
                    function () {
                        $container.removeClass('active-drop');
                        $list.addClass('hidden'); 
                        dropdownOverlay.addClass('hidden');
                    }
                );
            });
        }
    });
});
