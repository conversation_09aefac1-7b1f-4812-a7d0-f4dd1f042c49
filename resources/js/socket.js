import { io } from "https://cdn.socket.io/4.8.1/socket.io.esm.min.js";
const wsUrl = import.meta.env.VITE_WS_URL;

const cookies = () => {
    if (!document.cookie) return {};

    return document.cookie
        .split(";")
        .filter((cookie) => cookie.trim())
        .reduce((cookies, cookie) => {
            try {
                const [name, ...valueParts] = cookie
                    .split("=")
                    .map((part) => part.trim());
                const value = valueParts.join("=");

                if (name && value !== undefined) {
                    cookies[decodeURIComponent(name)] =
                        decodeURIComponent(value);
                }
                return cookies;
            } catch (e) {
                console.warn("Error parsing cookie:", cookie, e);
                return cookies;
            }
        }, {});
}
const token = cookies()?.user ? JSON.parse(cookies().user)?.token || null : null;
if (!token) {
    const notificationCurrent = localStorage.getItem('notification');
    if (!!notificationCurrent) {
        localStorage.removeItem('notification');
    }
}
// WS
const socket = io(wsUrl, {
    autoConnect: true,
    reconnectionAttempts: 10,
    path: "/api/v1/ws",
    withCredentials: true,
    transports: ["websocket"],
    query: {token}
});
socket.io.on("error", (error) => {
    console.error(error);
});
let jackpotsData = {};

window.listenJackpot = (partner) => {
    localStorage.removeItem('jackpotData');
    socket.on("jackpotbroadcast", (payload) => {
        jackpotsData = payload;
        updateJackpotValue();
        setTimeout(() => {
            localStorage.setItem('jackpotData', JSON.stringify(jackpotsData));
        }, 2000);
    });
}

window.listenNotification = () => {
    socket.on("notification", (payload) => {
        const notification = payload?.map(item => {
            if (item.link) {
                const url = new URL(item.link);
                url.hostname = window.location.hostname;
                item.link = url.toString();
            }
            return item;
        });
        if (notification?.length) {
            if (window.innerWidth >= 1300) {
                window.renderNotification(notification);
            } else {
                const totalNotification = notification?.filter((e) => !e.is_readed) || [];
                $("#total-notification").removeClass("hidden").addClass("flex").text(totalNotification.length > 99 ? "99+" : totalNotification.length);

                if (window?.renderNotificationMB) {
                    window.renderNotificationMB(notification);
                }
            }
        }
    });
}
const getUserActive = (partner, data, $viewers) => {
    const previewGame = localStorage.getItem('livestream-game') ? JSON.parse(localStorage.getItem('livestream-game')) : $('.js-livestream-play').data('game');

    let formatData;

    if (partner === 'vingame') {
        if (data.length > 0) {
            const target = data.find(x => x.gameId === previewGame?.partner_game_id);

            if (target) {
                formatData = target;
            }
        }
    } else {
        formatData = data;
    }

    if (previewGame && partner === previewGame.partner) {
        if (formatData?.mapGameId === previewGame?.partner_game_id || formatData?.gameId === previewGame?.partner_game_id) {
            const oldValue = parseInt($('.js-viewers').text()) || 0;
            const newValue = parseInt(formatData.activeUsers) || 0;

            if (newValue) {
                $(`.js-live-game-item-preview-viewers`).addClass('flex').removeClass('hidden');
                if (oldValue !== newValue) {
                    animateCounter($('.js-viewers'), oldValue, newValue, 5000, '');
                }
            } else {
                $(`.js-live-game-item-preview-viewers`).addClass('hidden').removeClass('flex');
            }
            if (oldValue !== newValue) {
                animateCounter($('.js-viewers'), oldValue, newValue, 5000, '');
            }
        } else {
            $(`.js-live-game-item-preview-viewers`).addClass('hidden').removeClass('flex');

        }
    }

    $viewers.each((index, element) => {
        const gameId = $(element).data('game-id');

        if (partner === 'vingame') {
            if (data.length > 0) {
                const target = data.find(x => x.gameId === gameId);

                if (target) {
                    formatData = target;
                }
            }
        } else {
            formatData = data;
        }

        if (formatData?.mapGameId == gameId || formatData?.gameId == gameId) {
            const oldValue = parseInt($(element).text()) || 0;
            const newValue = parseInt(formatData?.activeUsers) || 0;
            if (newValue > 0) {
                $(element).closest('.js-item-viewers-container').removeClass('opacity-0').addClass('opacity-100');
                
            } else {
                $(element).closest('.js-item-viewers-container').removeClass('opacity-100').addClass('opacity-0');
            }
            if (oldValue !== newValue) {
                animateCounter(element, oldValue, newValue, 5000, '', gameId);
                if ($(`.js-live-game-item.active .js-item-viewers[data-game-id="${gameId}"]`).length > 0) {
                    animateCounter(
                        $('.js-live-game-item-preview-viewers .js-viewers'),
                        $('.js-live-game-item-preview-viewers .js-viewers').text().replaceAll(',', '').trim(),
                        newValue,
                    5000, '');
                }
            }
        }
    });
}

const liveCasinoSocket = (partner) => {
    socket.on(`${partner}-gamels`, (payload) => {
        const data = JSON.parse(payload);

        const formatPartner = partner === 'techplay' ? 'vingame' : partner;

        const $viewers = $(`.js-${formatPartner}-viewers`);
        getUserActive(formatPartner, data, $viewers);
    });
}

listenNotification();
// liveCasinoSocket('b52');
// liveCasinoSocket('go');
// liveCasinoSocket('rik');
liveCasinoSocket('vingame');
liveCasinoSocket('techplay');
listenJackpot();

const updateJackpotValue = (jackpots = jackpotsData) => {
    const totalJackpot = Object.values(jackpots).reduce((acc, curr) => acc + curr, 0);
    let oldTotalJackpot = 0;
    if(totalJackpot > 1_000_000) {
        oldTotalJackpot =  Math.floor(totalJackpot / 1_000_000) * 1_000_000;
    }
    animateCounter($(".js-total-jackpot"), oldTotalJackpot, totalJackpot, 5000, '', 'total');

    Object.keys(jackpots).forEach((key) => {
        const jackpotId = key; // key corresponds to the jackpotId
        const jackpotValue = jackpots[key];
        $(`div[data-jackpotId='${jackpotId}'] span.prize`).text(jackpotValue);
        
        const $jackpotValue = $(`.js-jackpot-value-${jackpotId}`);
        const jackpottext = $(`.js-jackpot-value-${jackpotId} .js-jackpot-value`);
        const jackpotHomeValue = $(`.js-jackpot-value-${jackpotId} .live-game-item-jackpot`);
        if ($jackpotValue.length > 0) {
            $jackpotValue.removeClass('hidden').addClass('flex');
            const originOldValue = parseInt(jackpottext.text().replaceAll(',','')) || 0;
            let oldValue = originOldValue;
            const newValue = parseInt(jackpotValue) || 0;

            if(oldValue === 0 && newValue > 1_000_000){
                oldValue =  Math.floor(newValue / 1_000_000) * 1_000_000;
            }

            if (newValue) {
                $(`.js-jackpot-value-${jackpotId}`).removeClass('hidden').addClass('flex');
            } else {
                $(`.js-jackpot-value-${jackpotId}`).addClass('hidden').removeClass('flex');
            }
            if (originOldValue !== newValue) {
                animateCounter(jackpottext, oldValue, newValue, 5000, '', jackpotId);
                if (jackpotHomeValue) {
                    animateCounter(jackpotHomeValue, oldValue, newValue, 5000, '', jackpotId);
                }
                if (newValue) {
                    animateCounter($(`.js-jackpot-value-${jackpotId} .js-jackpot-home-value`), oldValue, newValue, 5000, '', jackpotId);
                } else {
                    $(`.js-jackpot-value-${jackpotId}`).addClass('hidden');
                }
            }
        }

        const $gameItemPreviewJackpot = $(`.js-live-game-item-preview-jackpot`);
        const jackpotPreviewText = $(`.js-live-game-item-preview-jackpot .js-jackpot-value`);
        const jackpotPreviewData = $gameItemPreviewJackpot.attr('data-game-id');
        if ($gameItemPreviewJackpot.length > 0 && jackpotPreviewData == jackpotId) {
            const originOldValue = parseInt(jackpottext.text()) || 0;
            let oldValue = originOldValue;
            const newValue = parseInt(jackpotValue) || 0;

            if (newValue) {
                $gameItemPreviewJackpot.removeClass('hidden').addClass('flex');

                if(oldValue === 0 && newValue > 1_000_000){
                    oldValue =  Math.floor(newValue / 1_000_000) * 1_000_000;
                }
                if (originOldValue !== newValue) {
                    animateCounter(jackpotPreviewText, oldValue, newValue, 5000, '', jackpotId);
                }
            } else {
                $gameItemPreviewJackpot.addClass('hidden').removeClass('flex');
            }
        }
    });
};

window.updateJackpotValue = updateJackpotValue;
window.GlobalSocket = socket;
