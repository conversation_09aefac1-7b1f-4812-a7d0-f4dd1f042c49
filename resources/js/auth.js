const siteKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY || '';

const handleRecaptcha = async () => {
    if (!siteKey) return '';

    return new Promise((resolve, reject) => {
        grecaptcha.ready(function() {
            grecaptcha.execute(siteKey, {action: 'submit'})
                .then(function(token) {
                    resolve(token);
                })
                .catch(function(error) {
                    reject(error);
                });
        });
    });
}

window.handleRecaptcha = handleRecaptcha;

const clearAllCache = async () => {
    const cacheNames = await caches.keys();
    for (const name of cacheNames) {
        await caches.delete(name);
    }
};

function bindLoginForm(redirect = "") {
    const loginForm = $(".login-form");
    const isSet = loginForm.attr("isSet");

    if (typeof isSet === "undefined" || isSet === false) {
        loginForm.attr("isSet", "");
        const buttonSubmit = loginForm.find(".button-submit");
        const form = loginForm.find("#login-form-modal");
        const inputFields = loginForm.find(".input-field");

        form.validate({
            rules: {
                username: {
                    required: true,
                    alphanumeric: true,
                    minlength: 6,
                    maxlength: 29,
                },
                pwd: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
            },
            messages: {
                username: {
                    required: "Vui lòng nhập tên đăng nhập",
                    minlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                    maxlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                },
                pwd: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const openErrorLoginModal = (message = "") => {
            let displayModal = errorLoginModal;
            switch (message) {
                case "Không tìm thấy người dùng.":
                    displayModal = errorLoginNotfoundModal;
                    break;
                case "Tài khoản của bạn đã bị khoá, vui lòng liên hệ support để được giải quyết.":
                    displayModal = errorLoginBlockModal;
                    break;
                default:
                    break;
            }
            openModal(displayModal, false, "error-login-modal");
            $("#error-login-text").text(message);
        };

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => {
                if (item.name === 'pwd') {
                    payload['password'] = item.value
                } else {
                    payload[item.name] = item.value
                }
            });

            try {
                buttonSubmit.attr("disabled", "");
                const token = await handleRecaptcha();
                payload.token = token;
                // Check affiliate params
                const affParams =
                    JSON.parse(localStorage.getItem("affParams")) || {};
                if (affParams) {
                    Object.assign(payload, affParams);
                }
                if (window.dataLayer && Array.isArray(window.dataLayer)) {
                    window.dataLayer.push({ event: "formSubmitted", formName: "Form_Login" });
                }
                const res = await submitData("/login", payload, "", "");

                if (res.status) {
                    useToast("success", res?.message);
                    await clearAllCache();

                    if (redirect) {
                        // Forces a full reload
                        setTimeout(
                            () => window.location.replace(redirect),
                            500
                        );
                    } else {
                        // update flow login
                        if (screen.width < 1200) {
                            window.scrollTo({ top: 0, behavior: "smooth" });
                        }

                        setTimeout(() => {
                            // Forces a full reload
                            reloadAllTabs();
                            window.location.replace(window.location.href);
                        }, 0);
                    }
                } else {
                    buttonSubmit.removeAttr("disabled");
                    openErrorLoginModal(res?.message);
                }
            } catch (error) {
                openErrorLoginModal(res?.message);
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

function bindSignupForm() {
    const signupForm = $(".signup-form");
    const isSet = signupForm.attr("isSet");

    if (typeof isSet === "undefined" || isSet === false) {
        signupForm.attr("isSet", "");
        const buttonSubmit = signupForm.find(".button-submit");
        const form = signupForm.find("#signup-form-modal");
        const inputFields = signupForm.find(".input-field");

        form.validate({
            rules: {
                username: {
                    required: true,
                    alphanumeric: true,
                    minlength: 6,
                },
                phone: {
                    required: true,
                    mobileValidation: true,
                    minlength: 10,
                },
                pwd: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
            },
            messages: {
                username: {
                    required: "Vui lòng nhập tên đăng nhập",
                    minlength: "Tên đăng nhập từ 6 đến 29 ký tự",
                },
                phone: {
                    required: "Vui lòng nhập số điện thoại",
                    minlength: "Nhập ít nhất 10 chữ số",
                },
                pwd: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => {
                if (item.name === 'pwd') {
                    payload['password'] = item.value
                } else {
                    payload[item.name] = item.value
                }
            });

            payload["confirmPassword"] = payload["password"];

            try {
                buttonSubmit.attr("disabled", "");
                const token = await handleRecaptcha();
                payload.token = token;
                // Check affiliate params
                const affParams =
                    JSON.parse(localStorage.getItem("affParams")) || {};
                if (affParams) {
                    Object.assign(payload, affParams);
                }
                if (window.dataLayer && Array.isArray(window.dataLayer)) {
                    window.dataLayer.push({ event: "formSubmitted", formName: "Form\_Register" });
                }
                const res = await submitData("/register", payload, "", "");

                if (res?.status) {
                    useToast("success", "Đăng ký tài khoản thành công");
                    await clearAllCache();
                    // update flow login - giu nguyen trang truoc dang nhap
                    if (screen.width < 1200) {
                        window.scrollTo({ top: 0, behavior: "smooth" });
                    }

                    setTimeout(() => {
                        window.location.reload();
                        reloadAllTabs();
                    }, 0);
                } else {
                    buttonSubmit.removeAttr("disabled");
                    useToast("error", res?.message);
                }
            } catch (error) {
                useToast("error", error?.message);
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

function bindForgetForm() {
    const forgetForm = $(".forget-form");
    const isSet = forgetForm.attr("isSet");

    if (typeof isSet === "undefined" || isSet === false) {
        forgetForm.attr("isSet", "");
        const buttonSubmit = forgetForm.find(".button-submit");
        const form = forgetForm.find("#forget-form-modal");
        const inputFields = forgetForm.find(".input-field");

        form.validate({
            rules: {
                email: {
                    required: true,
                    emailPattern: true,
                },
            },
            messages: {
                email: {
                    required: "Vui lòng nhập email",
                    email: "Địa chỉ email không hợp lệ",
                    emailPattern: "Địa chỉ email không hợp lệ",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value));

            try {
                buttonSubmit.attr("disabled", "");
                const res = await fetchData("/user/sendOtp", payload, "", "");

                if (res?.code === 200) {
                    if (res?.status === "OK") {
                        useToast("success", res?.message);
                        openAuthModal(newPassModal, false, "new-pass-modal");
                        handleInput();

                        if (payload["email"]) {
                            $(".new-pass-form").attr("email", payload["email"]);
                        } else {
                            $(".new-pass-form").attr(
                                "username",
                                payload["username"]
                            );
                        }
                        bindChangePassForm();
                    } else {
                        useToast("error", res?.message);
                    }
                }
            } catch (error) {
                useToast("error", error?.message);
            } finally {
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

function bindChangePassForm() {
    const changePassForm = $(".new-pass-form");
    const isSet = changePassForm.attr("isSet");
    const email = changePassForm.attr("email");
    const username = changePassForm.attr("username");

    if (typeof isSet === "undefined" || isSet === false) {
        changePassForm.attr("isSet", "");
        const inputFields = changePassForm.find(".input-field");
        const form = changePassForm.find("#new-pass-form-modal");
        const buttonSubmit = changePassForm.find(".button-submit");
        const buttonSendAgain = changePassForm.find(".button-send-again");

        const inputEmail = changePassForm.find(".input-email");
        const inputUserName = changePassForm.find(".input-username");
        const inputPassword = changePassForm.find(".input-password");

        if (email) {
            inputEmail.val(email);
        } else {
            inputUserName.val(username);
        }

        buttonSendAgain.on("click", async function () {
            const payload = {};

            if (email) {
                payload["email"] = email;
            } else {
                payload["username"] = username;
            }

            const res = await fetchData("/user/sendOtp", payload, "", "");

            if (res?.code === 200) {
                if (res?.status !== "VALIDATE_FAILED") {
                    useToast("success", res?.message);
                } else {
                    useToast("error", res?.message);
                }
            }
        });

        form.validate({
            rules: {
                otp: {
                    required: true,
                    minlength: 6,
                    maxlength: 6,
                },
                password: {
                    required: true,
                    noSpace: true,
                    minlength: 6,
                    maxlength: 32,
                },
                confirmPassword: {
                    required: true,
                    noSpace: true,
                    equalTo: inputPassword.find(".input-field"),
                },
            },
            messages: {
                otp: {
                    required: "Vui lòng nhập mã kích hoạt",
                    minlength: "Mã kích hoạt phải có 6 ký tự",
                    maxlength: "Mã kích hoạt phải có 6 ký tự",
                },
                password: {
                    required: "Vui lòng nhập mật khẩu",
                    minlength: "Mật khẩu từ 6 đến 32 ký tự",
                    maxlength: "Mật khẩu từ 6 đến 32 ký tự",
                },
                confirmPassword: {
                    required: "Vui lòng nhập xác nhận mât khẩu",
                    equalTo: "Mật khẩu mới không trùng khớp",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value));

            try {
                buttonSubmit.attr("disabled", "");
                const res = await submitData("/change-pass", payload, "", "");

                if (res?.status === "ERROR" || !res?.status) {
                    useToast("error", res?.message);
                } else {
                    buttonSubmit.removeAttr("disabled");
                    useToast("success", "Cập nhật mật khẩu thành công");
                    closeAuthModal();
                    openLogin();
                }
            } catch (error) {
                buttonSubmit.removeAttr("disabled");
                useToast("error", error?.message);
            }
        };
    }
}

function bindChangeNameForm(game = null) {
    const form = $("#change-name-form-modal");

    if (form) {
        const inputFields = form.find(".input-field");
        const buttonSubmit = form.find(".button-submit");

        form.validate({
            rules: {
                fullname: {
                    required: true,
                    minlength: 6,
                    alphanumeric: true,
                    maxlength: 29,
                },
            },
            messages: {
                fullname: {
                    required: "Vui lòng nhập tên hiển thị",
                    minlength: "Tên hiển thị từ 6 đến 29 ký tự",
                    alphanumeric: "Tên hiển thị chỉ được gồm: a-z, 0-9",
                },
            },
            errorClass: "error",
            errorPlacement: function (error, element) {
                validationField(element, "error", error);
            },
            success: function (label) {
                validationField(label, "success");
            },
            highlight: function (element) {
                validationField(element, "error", "");
            },
            submitHandler: function (form, e) {
                e.preventDefault();
                // Remove focus from the form
                $(form).find("input:focus").blur();
                handleSubmitForm(form);
            },
        });

        inputFields.each(function () {
            $(this).on("change input", function () {
                checkShowSubmit(form, buttonSubmit);
                $(this).valid();
            });
            if ($(this).val()) {
                $(this).trigger("change");
                $(this).trigger("input");
            }
        });

        const handleSubmitForm = async (form) => {
            const formEl = $(form);
            const data = formEl.serializeArray();
            const payload = {};

            data.forEach((item) => (payload[item.name] = item.value?.trim()));

            try {
                buttonSubmit.attr("disabled", "");
                const res = await submitData(
                    "/account/change-name",
                    payload,
                    "",
                    ""
                );
                if (res?.status === "OK") {
                    useToast("success", res?.message);
                    $(".js-fullname-account").text(payload["fullname"]);

                    if (game) {
                        await openGame(game, true);
                    }
                    
                    await fetchData("/refresh", {}, {}, "", "");
                    setTimeout(() => location.reload(), 1500);
                } else {
                    useToast("error", res?.message);
                    buttonSubmit.removeAttr("disabled");
                }
            } catch (error) {
                useToast("error", error?.message);
                buttonSubmit.removeAttr("disabled");
            }
        };
    }
}

window.bindLoginForm = bindLoginForm;
window.bindSignupForm = bindSignupForm;
window.bindForgetForm = bindForgetForm;
window.bindChangePassForm = bindChangePassForm;
window.bindChangeNameForm = bindChangeNameForm;
