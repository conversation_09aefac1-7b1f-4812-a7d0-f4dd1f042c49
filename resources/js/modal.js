function openModal(modal, isSticky = true, modalId = "", hidden = false, reload = "", onClose = () => {}) {
    $("#generic-modal").html(modal);
    $('#chat-widget-container').removeClass('has-modal')

    if (reload) {
        $("#generic-modal").data('reload', reload);
    }
    const listModal = ['bank-list-modal-container'];
    listModal.forEach((e) => {
        if (!!$(e)) {
            $('body').addClass('no-scroll');
            $('body').addClass('block-scroll');
        } else {
            $('body').removeClass('overflow-hidden');
            $('body').removeClass('no-scroll');
            $('body').removeClass('block-scroll');
        }
    })
    if (hidden) {
        $("#generic-modal").addClass("hidden");
        $("#generic-modal").attr("type", "hidden");
    }

    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            if ($(e.target).closest($(`#${modalId}-container`)).length === 0) {
                closeModal();
            }
        });
    }
    if (onClose) {
        $('.js-close-modal').on('click', (event) => {
            event.preventDefault();
            closeModal();
            onClose();
        });
    } else {
        closeModal()
    }
}

function toggleHiddenModal() {
    $("#generic-modal").toggleClass("hidden");
}

function checkSignupModal() {
    if ($('#signup-modal').is(':visible')) {
        $('body').addClass('no-scroll');
        $('body').addClass('block-scroll');
    }
}

/**
 * checkOnActiveModal
 * 
 * Check if any modal is open (has child elements).
 * If so, prevent page scrolling.
 */
const checkOnActiveModal  = ()=>{
    const notiModal = $('#noti-modal')?.children();
    const authModal = $('#auth-modal')?.children();
    const genericModal = $('#generic-modal')?.children();
    if(notiModal?.length || authModal?.length || genericModal?.length){
        $('body').addClass('overflow-hidden');
        $('body').addClass('no-scroll');
        $('body').addClass('block-scroll');
    }
}

function closeModal() {
    const reload = $(`#generic-modal`).data("reload");
    $('body').removeClass('overflow-hidden');
    $('body').removeClass('no-scroll');
    $('body').removeClass('block-scroll');
    checkSignupModal();
    
    $(`#generic-modal`).html("");
    checkOnActiveModal();
    if (reload) {
        window.location.href = reload;
    }
}

function openAuthModal(modal, isSticky = true, modalId = "") {
    $("#auth-modal").empty().html(modal);
    $('#chat-widget-container').removeClass('has-modal')
    $("body").addClass("no-scroll");
    $('body').removeClass('overflow-hidden');
    $('body').addClass('block-scroll');
    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            if ($(e.target).closest($(`#${modalId}-container`)).length === 0) {
                closeAuthModal();
            }
        });
    }
}

function closeAuthModal() {
    $(`#auth-modal`).html("");
    $("body").removeClass("no-scroll");
    $('body').removeClass('block-scroll');
    $('body').removeClass('overflow-hidden');

    if ($('#generic-modal').children().length > 0) {
        $(".main-body").addClass("no-scroll");
        $('.main-body').addClass('block-scroll');

        //check show event
         $('.js-event-modal').removeClass('!z-0 !hidden');
        $("body").addClass("no-scroll");
        $('body').addClass('block-scroll');
    }
}

function openNotificationModal(modal, isSticky = true, modalId = "", hidden = false, reload = "", onClose = () => {}) {
    $("#noti-modal").html(modal);

    if (reload) {
        $("#noti-modal").data('reload', reload);
    }
    const listModal = ['bank-list-modal-container'];
    listModal.forEach((e) => {
        if (!!$(e)) {
            $('body').addClass('no-scroll');
            $('body').addClass('block-scroll');
        } else {
            $('body').removeClass('overflow-hidden');
            $('body').removeClass('no-scroll');
            $('body').removeClass('block-scroll');
        }
    })
    if (hidden) {
        $("#noti-modal").addClass("hidden");
        $("#noti-modal").attr("type", "hidden");
    }

    if (!isSticky) {
        $(`#${modalId}`).on("click", async function (e) {
            if ($(e.target).closest($(`#${modalId}-container`)).length === 0) {
                closeNotificationModal();
            }
        });
    }
    if (onClose) {
        $('.js-close-modal').on('click', (event) => {
            event.preventDefault();
            closeNotificationModal();
            onClose();
        });
    } else {
        closeNotificationModal()
    }
}

function closeNotificationModal() {
    const reload = $(`#noti-modal`).data("reload");
    $('body').removeClass('overflow-hidden');
    $('body').removeClass('no-scroll');
    $('body').removeClass('block-scroll');
    $(`#noti-modal`).html("");
    checkOnActiveModal();
    if (reload) {
        window.location.href = reload;
    }
}

window.openModal = openModal;
window.closeModal = closeModal;
window.openAuthModal = openAuthModal;
window.closeAuthModal = closeAuthModal;
window.toggleHiddenModal = toggleHiddenModal;
window.openNotificationModal = openNotificationModal;
window.closeNotificationModal = closeNotificationModal;

document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        if ($("#generic-modal").html().trim() !== "") {
            if ($("#noti-modal").html().trim() === "") {
                closeModal();
            }
        }
        if ($("#auth-modal").html().trim() !== "") {
            if ($("#noti-modal").html().trim() === "") {
                closeAuthModal();
            }
        }
        if ($("#noti-modal").html().trim() !== "") {
            closeNotificationModal();
        }
    }
});
