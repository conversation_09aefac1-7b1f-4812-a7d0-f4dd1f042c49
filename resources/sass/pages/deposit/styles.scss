@import "./ewallet.scss";

.deposit-tab-item {

    &.disable {
        @apply text-neutral-600 ;

        img {
            filter: brightness(0) saturate(100%) invert(93%) sepia(12%) saturate(113%) hue-rotate(190deg) brightness(89%) contrast(92%);
        }
    }

    @apply h-11 relative font-medium text-sm capitalize whitespace-nowrap text-neutral-800 flex items-center min-w-max px-3 xl:px-4;

    .hint {
        @apply absolute top-0 right-1 h-3 px-1.5 flex items-center rounded-[100px];
        & > span {
            @apply text-[8px] leading-3 font-medium uppercase mt-0.5;
        }
        &.status-new {
            @apply bg-success-700 [&>span]:text-neutral;
        }
        &.status-maintain {
            @apply bg-gray-400 [&>span]:text-neutral;
        }
        &.status-suggest {
            @apply bg-warning-700 [&>span]:text-neutral;
        }
    }
    &.active {
        
        &::after {
            @apply absolute bottom-0 w-[45px] mx-auto left-0 right-0 bg-secondary-500 h-1 rounded-t-full;
            content: "";
        }
        @apply text-secondary-500 bg-gradient-to-t bg-blue-3-gradient to-transparent;

        img {
            filter: brightness(0) saturate(100%) invert(18%) sepia(59%)
                saturate(3270%) hue-rotate(199deg) brightness(94%)
                contrast(102%);
        }
    }
}

.custom-amount-btn {
    @apply overflow-hidden flex h-[38px] xl:h-[42px] border-[1px] flex-col rounded-lg 
    text-xs xl:text-sm font-medium items-center justify-center px-0 border-solid bg-card-amount
    border-neutral-150 xl:hover:text-primary-700 transition-colors text-neutral-1000;

    &.active {
        @apply  text-primary-700 border-primary-400 bg-card-amount-active;
    }
}


.promotions-group {
    & .promotions-item {
        background-image: url(../../public/asset/images/account/deposit/promo-card.avif);
        background-size: cover;

        @apply flex items-center justify-center h-[3.625rem] rounded-[4px] overflow-hidden 
        px-[10px] xl:px-4 py-3 cursor-pointer border-none;
        &:hover {
            background-image: url(../../public/asset/images/account/deposit/promo-card-active.avif);
        }

        &:has(input:checked) {
            background-image: url(../../public/asset/images/account/deposit/promo-card-active.avif);
            
            &>.checked-icon {
                @apply opacity-100 #{!important};
            }

            .promotions-label {
                @apply text-neutral-1000 #{!important};
            }
        }
    }
}

.amount-input-wrapper {
    .input-right-text {
        @apply italic text-xs font-normal #{!important};
    }
}
    
.copy-icon {
    &.active {
        filter: brightness(0) saturate(100%) invert(40%) sepia(53%) saturate(3213%) hue-rotate(357deg) brightness(102%) contrast(101%);
    }
}

.copy-button {
    &.active {
        img {
            filter: brightness(0) saturate(100%) invert(56%) sepia(75%) saturate(5497%) hue-rotate(360deg) brightness(103%) contrast(104%);
        }
    }
}

.swiper-button-disabled {
    @apply text-neutral-500 border-neutral-50 #{!important}
}

.codepay-qr-ticket {
    .ticket-mb-bg {
        @apply xl:hidden mx-auto rounded-2xl flex relative flex-col pb-3 max-w-[440px];
        background: linear-gradient(223.92deg, #C9C9C9 -9.02%, #F6F6F6 67.85%);
    }
}
.ewallet-content {
    .ticket-mb-bg {
        @apply xl:hidden mx-auto rounded-2xl flex relative flex-col pb-[17px] xl:pb-3 max-w-[440px];
        background: linear-gradient(225.86deg, #C9C9C9 -9.33%, #F6F6F6 70.2%);

    }
    .line-qr {
        @apply h-[36px] xl:h-[25.008px];
    }
    .line-left,
    .line-right {
        @apply w-[36px];
    }
}