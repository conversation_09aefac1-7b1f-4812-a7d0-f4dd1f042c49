server {  
  listen 80;
  index index.php index.html;
  server_name localhost;  
  root /var/www/public;  
  charset utf-8;  
  location / {  
    try_files $uri $uri/ /index.php?$query_string;  
  }  
  location ~ \.php$ {
    fastcgi_pass app:9000;
    fastcgi_index index.php;
    fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
    include fastcgi_params;
    fastcgi_read_timeout 600;
  }

  access_log off;  
  error_log /var/log/nginx/error.log error;  

  sendfile off;  

  client_max_body_size 100m;  
  location ~ /.ht {  
    deny all;  
  }     
}
# server {  
#   listen 5173;
#   server_name localhost;  
#   location /@vite {

        
#     proxy_pass http://app:5173;
#     proxy_http_version 1.1;
#     proxy_set_header Upgrade $http_upgrade;
#     proxy_set_header Connection 'upgrade';
#     proxy_set_header Host $host;
#     proxy_cache_bypass $http_upgrade;    
#     # Allow CORS
#     add_header 'Access-Control-Allow-Origin' '*'; 
#     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
#     add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization' always; 
#   }
 
# }