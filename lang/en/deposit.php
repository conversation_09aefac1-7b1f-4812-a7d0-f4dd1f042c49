<?php

return [
    'codepay' => [
        'instruction' => [
            'title' => 'Deposit Instructions',
            'label1' => 'Note',
            'items1' => [
                'Deposit/Withdraw using your own account.',
                'QR code can only be used once.',
                'Support interbank transfers.',
                'Keep the receipt for verification when needed.',
            ],
            'label2' => 'QR Code',
            'items2' => [
                'The code can only be used once, please enter the exact transfer content, if incorrect you will not receive the money.',
            ],
            'label3' => 'Account',
            'items3' => [
                'Bank accounts will always be continuously updated, please do not save this information when making transactions.',
            ],
        ],
    ],
    'crypto' => [
        'instruction' => [
            'label' => 'Deposit Instructions',
            'items' => [
                'You need to transfer at least 50K.',
                'The code can only be used once, please enter the correct transfer content, if incorrect you will not receive the money.',
                'After 1 minute if you haven\'t received the money, please contact SUPPORT 24/7.',
            ],
        ],
        'instructionUSDT' => [
            ['label' => 'Binance', 'icon' => 'asset/icons/account/deposit/crypto/binance.avif'],
            ['label' => 'Coin12', 'icon' => 'asset/icons/account/deposit/crypto/coin12.avif'],
            ['label' => 'Huobi', 'icon' => 'asset/icons/account/deposit/crypto/huobi.avif'],
            ['label' => 'Remitano', 'icon' => 'asset/icons/account/deposit/crypto/remitano.avif'],
        ],
    ],
];
