<?php

/**
 * <PERSON><PERSON><PERSON> để dịch các text tiế<PERSON> sang tiếng Anh
 */

class VietnameseToEnglishTranslator
{
    private $translations = [];

    public function __construct()
    {
        $this->initializeTranslations();
    }

    private function initializeTranslations()
    {
        // Từ điển dịch cơ bản từ tiếng Vi<PERSON> sang tiếng Anh
        $this->translations = [
            // Auth
            __('common.Dang_nhap_1') => 'Login',
            __('common.Dang_ky_1') => 'Sign Up',
            __('common.Dang_xuat_3') => 'Logout',
            __('common.ten_dang_nhap_1') => 'Username',
            __('common.mat_khau_1') => 'Password',
            __('auth.mat_khau_moi_1') => 'New Password',
            __('auth.xac_nhan_mat_khau_1') => 'Confirm Password',
            __('common.so_dien_thoai_1') => 'Phone Number',
            'Email' => 'Email',
            __('auth.nhap_dia_chi_email_cua_ban') => 'Enter your email address',
            __('auth.tai_khoan_bi_khoa') => 'Account is locked',
            __('auth.loi_dang_nhap') => 'Login error',
            __('auth.ten_hien_thi') => 'Display Name',
            __('auth.nhap_it_nhat_6_ky_tu') => 'Enter at least 6 characters',
            __('account.nhap_ma_otp_1') => 'Enter OTP code',

            // Common
            __('common.trang_chu_9') => 'Home',
            __('common.tin_tuc_4') => 'News',
            __('common.the_thao_2') => 'Sports',
            'Casino' => 'Casino',
            __('common.tro_choi_1') => 'Games',
            __('config.cong_game_2') => 'Game Portal',
            __('common.khuyen_mai_1') => 'Promotions',
            __('common.ho_tro_2') => 'Support',
            __('common.tai_khoan_2') => 'Account',
            __('common.nap_tien_2') => 'Deposit',
            __('common.rut_tien_2') => 'Withdraw',
            __('common.lich_su_1') => 'History',
            __('common.thong_tin_1') => 'Information',
            __('common.cai_dat_1') => 'Settings',
            __('common.xac_nhan_1') => 'Confirm',
            __('common.huy_1') => 'Cancel',
            __('common.luu_1') => 'Save',
            __('common.tim_kiem_4') => 'Search',
            __('home.xem_them_1') => 'View More',
            __('account.tat_ca_3') => 'All',

            // Casino
            __('common.song_bai_1') => 'Casino',
            __('common.yeu_thich_1') => 'Favorites',
            __('common.tai_xiu_1') => 'Sic Bo',
            __('common.xoc_dia_1') => 'Xoc Dia',
            __('common.rong_ho_1') => 'Dragon Tiger',
            __('common.khac_1') => 'Others',

            // Games
            __('account.quay_so_1') => 'Number Games',
            __('account.no_hu_1') => 'Jackpot',
            __('account.game_bai') => 'Card Games',
            __('account.ban_ca_1') => 'Fish Shooting',
            __('games.games_khac') => 'Other Games',
            __('account.lo_de') => 'Lottery',
            __('account.Da_ga') => 'Cockfighting',

            // Account
            __('account.thuc_nhan_1') => 'Net Amount',
            __('account.so_vong_cuoc_1') => 'Betting Rounds',
            __('account.25_vong_1') => '25 Rounds',
            __('account.tien_cuoc_yeu_cau_1') => 'Required Bet Amount',
            __('account.giao_dich_p2p_3') => 'P2P Transaction',
            __('account.moi_5') => 'New',
            __('account.de_xuat_1') => 'Recommended',
            __('account.tien_ao_7') => 'Cryptocurrency',
            __('account.vi_dien_tu_3') => 'E-Wallet',
            __('account.the_cao_11') => 'Scratch Card',
            __('account.ngan_hang_9') => 'Bank',
            __('account.lich_su_cuoc_1') => 'Betting History',
            __('account.lich_su_giao_dich_1') => 'Transaction History',
            __('account.ma_giao_dich_3') => 'Transaction ID',
            __('account.thoi_gian_3') => 'Time',
            'Game' => 'Game',
            __('account.so_tien_3') => 'Amount',
            'Thắng/Thua' => 'Win/Loss',
            'Turnover' => 'Turnover',
            __('account.trang_thai_3') => 'Status',

            // Policy & Terms
            __('common.ve_chung_toi_1') => 'About Us',
            __('config.mien_trach_nhiem') => 'Disclaimer',
            __('config.cau_hoi_thuong_gap') => 'FAQ',
            __('config.chinh_sach_bao_mat') => 'Privacy Policy',
            __('config.dieu_khoan_Dieu_kien') => 'Terms & Conditions',
            __('config.huong_dan') => 'Guide',
            __('config.chinh_sach_bao_mat_3') => 'Privacy Policy',
            __('config.Dieu_khoan_dieu_kien') => 'Terms & Conditions',
            __('config.Dieu_khoan_Dieu_kien') => 'Terms & Conditions',
            __('config.tro_giup_7') => 'Help',
            __('config.mien_trach_nhiem_2') => 'Disclaimer',

            // Maintenance
            __('config.Dang_bao_tri') => 'Under Maintenance',
            __('config.website_dang_duoc_bao_tri_chung_toi_se_som_tro_lai') => 'Website is under maintenance, we will be back soon. Please come back later.',
            __('config.lien_he_ho_tro') => 'Contact Support',

            // Sports
            __('sports.the_thao_chau_A') => 'Asian Sports',
            __('sports.the_thao_chau_Au') => 'European Sports',
            __('sports.the_thao_latinh') => 'Latin Sports',
            __('sports.the_thao_Ao') => 'Virtual Sports',
            __('sports.ti_le_cuoc_hap_dan') => 'Attractive Odds',
            __('sports.Da_dang_the_loai') => 'Diverse Categories',
            __('sports.mo_phong_nhieu_the_loai') => 'Multi-category Simulation',
            __('sports.trai_nghiem_cong_nghe_moi') => 'New Technology Experience',

            // Home
            __('config.game_moi_3') => 'New Games',
            __('config.vua_choi_3') => 'Recently Played',
            __('config.lo_De_3_mien') => '3-Region Lottery',
            __('config.lo_De_sieu_toc') => 'Super Fast Lottery',
            __('config.sieu_toc_md5_1') => 'MD5 Super Fast',
            __('config.Da_ga_ga28') => 'GA28 Cockfighting',
            __('config.Da_ga_ws168') => 'WS168 Cockfighting',
            __('config.kenh_thong_tin_km') => 'Promotion Info Channel',
            __('home.tro_choi_noi_bat') => 'Featured Games',

            // Common UI
            __('sports.cac_tran_Dau') => 'Matches',
            __('sports.noi_bat') => 'Featured',
            __('sports.Ao') => 'Virtual',
            __('sports.soi_Dong') => 'Exciting',
            __('games.game_khong_choi_duoc') => 'Game unavailable',
            __('games.ban_dang_tham_gia_khuyen_mai_khong_duoc_phep_choi_') => 'You are participating in a promotion that does not allow playing this game',
            __('games.xem_khuyen_mai') => 'View Promotions',
            __('common.nha_cung_cap_1') => 'Provider',
            __('header.vui_long_nhap_ten_dang_nhap_va_mat_khau') => 'Please enter username and password',
            __('common.tim_kiem_3') => 'Search',
            __('account.bao_tri_2') => 'Maintenance',

            // Promotions
            __('account.khuyen_mai_100_nap_lan_dau_1') => '100% First Deposit Bonus',
            __('account.nap_lan_dau') => 'First Deposit',
            __('account.nhan_ngay_phan_thuong_Dung_bo_lo_co_hoi_vang_nay_7') => 'Get rewards now. Don\'t miss this golden opportunity!',
            __('account.hoan_tra_the_thao_len_den_16_1') => 'Sports Cashback up to 1.6%',
            __('account.hoan_tra_slot_hang_ngay_3') => 'Daily Slot Cashback',
            __('account.hoan_tra_10_cho_moi_lan_quay_1') => '10% cashback for each spin.',
            __('account.hoan_tra_hang_tuan_khi_choi_live_casino') => 'Weekly cashback when playing Live Casino',
            __('account.hoan_tra_vo_tan') => 'Unlimited Cashback',
            __('account.hoan_tra_16') => '1.6% Cashback',
            __('account.nap_tien_lan_dau') => 'First Deposit',
            __('account.khuyen_mai_100') => '100% Bonus',

            // Account Management
            __('account.tim_kiem_ngan_hang_1') => 'Search Bank',
            __('account.xac_minh_email') => 'Verify Email',
            __('account.nhap_email_cua_ban') => 'Enter your Email',
            __('account.ma_otp_1') => 'OTP Code',
            __('account.xac_minh_telegram') => 'Verify Telegram',
            __('account.nhap_so_tien_nap') => 'Enter deposit amount',
            __('account.so_tien_nap') => 'Deposit Amount',
            __('account.huy_giao_dich') => 'Cancel transaction?',
            __('account.tu_choi') => 'Decline',
            __('account.so_serial_1') => 'Serial Number',
            __('account.nhap_so_serial_1') => 'Enter serial number',
            __('account.ma_the_pin') => 'Card PIN',
            __('account.nhap_ma_the_pin') => 'Enter card PIN',
            __('account.gan_day_1') => 'Recent',
            __('account.chon_ngan_hang_cua_ban_1') => 'Choose your bank',
            __('account.chu_tai_khoan_2') => 'Account Holder',
            __('account.nhap_ten_tai_khoan_3') => 'Enter account name',
            __('account.so_tai_khoan_2') => 'Account Number',
            __('account.nhap_so_tai_khoan_2') => 'Enter account number',
            __('account.huong_dan_mua_usdt') => 'USDT Purchase Guide',
            __('account.ten_tai_khoan') => 'Account Name',
            __('account.so_tien_rut_1') => 'Withdrawal Amount',
            __('account.nhap_so_tien_rut_1') => 'Enter withdrawal amount',
            __('account.nhap_5_so_cuoi_dien_thoai_2') => 'Enter last 5 digits of phone',
            __('account.so_luong_the') => 'Number of Cards',
            __('account.nhap_dia_chi_vi') => 'Enter wallet address',
            __('account.Dia_chi_vi') => 'Wallet Address',

            // Error Messages
            __('config.oop_khong_tim_thay_trang') => 'Oops! Page Not Found',
            __('config.xin_loi_trang_ban_dang_tim_kiem_khong_duoc_tim_tha') => 'Sorry, the page you are looking for could not be found. Please try another page.',
            __('config.quay_ve_trang_chu') => 'Back to Home',
            __('news.khong_co_du_lieu') => 'No data available',
            __('common.khong_tim_thay_nguoi_dung_1') => 'User not found',

            // Address
            __('common.123_Duong_nguyen_hue_1') => '123 Nguyen Hue Street',
            __('common.quan_1_1') => 'District 1',
            __('common.tp_ho_chi_minh_1') => 'Ho Chi Minh City',

            // Brand
            __('common.nha_cai_truc_tuyen_hang_dau_1') => 'Leading Online Betting House',
            __('sports.cuong_nhiet') => 'Passionate',

            // Logout
            __('common.Dang_xuat_tai_khoan_2') => 'Logout account',
            __('common.ban_muon_thoat_tai_khoan_2') => 'Do you want to logout?',
            __('common.bo_qua_2') => 'Skip',

            // Quay so
            __('config.quay_so_1_1') => 'Number Game 1',
            __('config.quay_so_2_1') => 'Number Game 2',
            __('config.quay_so_5_1') => 'Number Game 5',

            // Generic
            __('common.Dot_2') => 'Round',
            __('config.lien_he') => 'Contact',
            __('config.giup_do') => 'Help',
        ];
    }

    public function translateText($vietnameseText)
    {
        // Kiểm tra trong từ điển trước
        if (isset($this->translations[$vietnameseText])) {
            return $this->translations[$vietnameseText];
        }

        // Nếu không có trong từ điển, thử dịch một số pattern cơ bản
        $text = $vietnameseText;

        // Thay thế một số từ phổ biến
        $commonWords = [
            __('common.cua') => 'of',
            __('common.va') => 'and',
            __('common.voi') => 'with',
            'trong' => 'in',
            __('common.tren') => 'on',
            'cho' => 'for',
            __('common.tu') => 'from',
            __('config.den') => 'to',
            'khi' => 'when',
            __('common.neu') => 'if',
            __('common.hoac') => 'or',
            __('common.nhung') => 'but',
            __('common.vi') => 'because',
            __('common.de') => 'to',
            __('common.co') => 'have',
            __('common.la') => 'is',
            __('common.duoc') => 'be',
            __('common.se') => 'will',
            __('common.da') => 'has',
            __('common.dang') => 'is',
            __('common.ban') => 'you',
            __('common.chung_toi') => 'we',
            __('common.ho') => 'they',
            __('common.toi') => 'I',
            __('common.nay') => 'this',
            __('common.do') => 'that',
            __('common.nhung_1') => 'the',
            __('common.cac') => 'the',
            __('common.mot') => 'a',
            __('common.khong') => 'not',
            __('common.rat') => 'very',
            __('common.nhieu') => 'many',
            __('common.it') => 'few',
            __('common.lon') => 'big',
            __('common.nho') => 'small',
            __('account.moi_5') => 'new',
            __('common.cu') => 'old',
            __('common.tot') => 'good',
            __('common.xau') => 'bad',
            'nhanh' => 'fast',
            __('common.cham') => 'slow',
        ];

        // Nếu text quá dài hoặc phức tạp, giữ nguyên
        if (mb_strlen($text) > 100) {
            return $vietnameseText;
        }

        // Thay thế một số từ cơ bản
        foreach ($commonWords as $vi => $en) {
            $text = str_ireplace($vi, $en, $text);
        }

        // Nếu sau khi thay thế vẫn giống text gốc, trả về text gốc
        if ($text === $vietnameseText) {
            return $vietnameseText;
        }

        return $text;
    }

    public function translateFile($filePath)
    {
        if (!file_exists($filePath)) {
            echo "File không tồn tại: $filePath\n";
            return false;
        }

        $content = file_get_contents($filePath);
        
        // Parse PHP array
        $array = include $filePath;
        if (!is_array($array)) {
            echo "File không phải là PHP array: $filePath\n";
            return false;
        }

        $translatedArray = $this->translateArray($array);
        
        // Tạo nội dung file mới
        $newContent = "<?php\n\nreturn [\n";
        $newContent .= $this->arrayToString($translatedArray, 1);
        $newContent .= "];\n";

        file_put_contents($filePath, $newContent);
        return true;
    }

    private function translateArray($array)
    {
        $result = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->translateArray($value);
            } else {
                $result[$key] = $this->translateText($value);
            }
        }
        return $result;
    }

    private function arrayToString($array, $indent = 1)
    {
        $result = '';
        $indentStr = str_repeat('    ', $indent);

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result .= "{$indentStr}'{$key}' => [\n";
                $result .= $this->arrayToString($value, $indent + 1);
                $result .= "{$indentStr}],\n";
            } else {
                $escapedValue = addslashes($value);
                $result .= "{$indentStr}'{$key}' => '{$escapedValue}',\n";
            }
        }

        return $result;
    }

    public function translateAllEnglishFiles()
    {
        $langEnDir = 'lang/en';
        if (!is_dir($langEnDir)) {
            echo "Thư mục lang/en không tồn tại\n";
            return;
        }

        $files = glob($langEnDir . '/*.php');
        foreach ($files as $file) {
            echo "Đang dịch file: $file\n";
            if ($this->translateFile($file)) {
                echo "Đã dịch xong: $file\n";
            } else {
                echo "Lỗi khi dịch: $file\n";
            }
        }
    }
}

// Chạy script
$translator = new VietnameseToEnglishTranslator();
echo __('common.dIch_sang_tiEng_anh_nn');
$translator->translateAllEnglishFiles();
echo __('common.nhoan_thanh_dich_sang_tieng_anhn');
