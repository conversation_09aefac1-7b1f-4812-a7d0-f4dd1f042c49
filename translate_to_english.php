<?php

/**
 * <PERSON><PERSON><PERSON> để dịch các text tiế<PERSON> Việ<PERSON> sang tiếng Anh
 */

class VietnameseToEnglishTranslator
{
    private $translations = [];

    public function __construct()
    {
        $this->initializeTranslations();
    }

    private function initializeTranslations()
    {
        // Từ điển dịch cơ bản từ tiếng Việt sang tiếng Anh
        $this->translations = [
            // Auth
            'Đăng nhập' => 'Login',
            'Đăng ký' => 'Sign Up',
            'Đăng xuất' => 'Logout',
            'Tên đăng nhập' => 'Username',
            'Mật khẩu' => 'Password',
            'Mật khẩu mới' => 'New Password',
            'Xác nhận mật khẩu' => 'Confirm Password',
            'Số điện thoại' => 'Phone Number',
            'Email' => 'Email',
            'Nhập địa chỉ email của bạn' => 'Enter your email address',
            'T<PERSON><PERSON> khoản bị khoá' => 'Account is locked',
            'lỗi đăng nhập' => 'Login error',
            'Tên hiển thị' => 'Display Name',
            'Nhập ít nhất 6 ký tự' => 'Enter at least 6 characters',
            'Nhập mã OTP' => 'Enter OTP code',

            // Common
            'Trang chủ' => 'Home',
            'Tin tức' => 'News',
            'Thể thao' => 'Sports',
            'Casino' => 'Casino',
            'Trò chơi' => 'Games',
            'Cổng game' => 'Game Portal',
            'Khuyến mãi' => 'Promotions',
            'Hỗ trợ' => 'Support',
            'Tài khoản' => 'Account',
            'Nạp tiền' => 'Deposit',
            'Rút tiền' => 'Withdraw',
            'Lịch sử' => 'History',
            'Thông tin' => 'Information',
            'Cài đặt' => 'Settings',
            'Xác nhận' => 'Confirm',
            'Hủy' => 'Cancel',
            'Lưu' => 'Save',
            'Tìm kiếm' => 'Search',
            'Xem thêm' => 'View More',
            'Tất cả' => 'All',

            // Casino
            'Sòng bài' => 'Casino',
            'Yêu thích' => 'Favorites',
            'Tài xỉu' => 'Sic Bo',
            'Xóc đĩa' => 'Xoc Dia',
            'Rồng hổ' => 'Dragon Tiger',
            'Khác' => 'Others',

            // Games
            'Quay số' => 'Number Games',
            'Nổ hũ' => 'Jackpot',
            'Game bài' => 'Card Games',
            'Bắn cá' => 'Fish Shooting',
            'Games khác' => 'Other Games',
            'Lô đề' => 'Lottery',
            'Đá gà' => 'Cockfighting',

            // Account
            'Thực nhận' => 'Net Amount',
            'Số vòng cược' => 'Betting Rounds',
            '25 Vòng' => '25 Rounds',
            'Tiền cược yêu cầu' => 'Required Bet Amount',
            'giao dịch P2P' => 'P2P Transaction',
            'mới' => 'New',
            'đề xuất' => 'Recommended',
            'tiền ảo' => 'Cryptocurrency',
            'Ví điện tử' => 'E-Wallet',
            'Thẻ cào' => 'Scratch Card',
            'Ngân hàng' => 'Bank',
            'Lịch Sử Cược' => 'Betting History',
            'Lịch Sử Giao Dịch' => 'Transaction History',
            'Mã giao dịch' => 'Transaction ID',
            'Thời gian' => 'Time',
            'Game' => 'Game',
            'Số tiền' => 'Amount',
            'Thắng/Thua' => 'Win/Loss',
            'Turnover' => 'Turnover',
            'Trạng thái' => 'Status',

            // Policy & Terms
            'Về chúng tôi' => 'About Us',
            'miễn trách nhiệm' => 'Disclaimer',
            'câu hỏi thường gặp' => 'FAQ',
            'chính sách bảo mật' => 'Privacy Policy',
            'điều khoản & Điều kiện' => 'Terms & Conditions',
            'hướng dẫn' => 'Guide',
            'Chính sách bảo mật' => 'Privacy Policy',
            'Điều khoản & điều kiện' => 'Terms & Conditions',
            'Điều khoản & Điều kiện' => 'Terms & Conditions',
            'Trợ giúp' => 'Help',
            'Miễn trách nhiệm' => 'Disclaimer',

            // Maintenance
            'Đang bảo trì' => 'Under Maintenance',
            'Website đang được bảo trì, chúng tôi sẽ sớm trở lại. Quý khách vui lòng quay lại sau.' => 'Website is under maintenance, we will be back soon. Please come back later.',
            'Liên hệ hỗ trợ' => 'Contact Support',

            // Sports
            'Thể thao châu Á' => 'Asian Sports',
            'Thể thao châu Âu' => 'European Sports',
            'Thể thao Latinh' => 'Latin Sports',
            'Thể thao Ảo' => 'Virtual Sports',
            'Tỉ lệ cược hấp dẫn' => 'Attractive Odds',
            'Đa dạng thể loại' => 'Diverse Categories',
            'Mô phỏng nhiều thể loại' => 'Multi-category Simulation',
            'Trải nghiệm công nghệ mới' => 'New Technology Experience',

            // Home
            'Game mới' => 'New Games',
            'Vừa chơi' => 'Recently Played',
            'Lô Đề 3 Miền' => '3-Region Lottery',
            'Lô Đề Siêu Tốc' => 'Super Fast Lottery',
            'Siêu tốc MD5' => 'MD5 Super Fast',
            'Đá Gà GA28' => 'GA28 Cockfighting',
            'Đá Gà WS168' => 'WS168 Cockfighting',
            'Kênh Thông Tin KM' => 'Promotion Info Channel',
            'Trò chơi nổi bật' => 'Featured Games',

            // Common UI
            'Các Trận Đấu' => 'Matches',
            'Nổi Bật' => 'Featured',
            'Ảo' => 'Virtual',
            'Sôi Động' => 'Exciting',
            'Game không chơi được' => 'Game unavailable',
            'Bạn đang tham gia khuyến mãi không được phép chơi trò chơi này' => 'You are participating in a promotion that does not allow playing this game',
            'Xem khuyến mãi' => 'View Promotions',
            'Nhà Cung Cấp' => 'Provider',
            'Vui lòng nhập tên đăng nhập và mật khẩu' => 'Please enter username and password',
            'Tìm Kiếm' => 'Search',
            'Bảo trì' => 'Maintenance',

            // Promotions
            'Khuyến mãi 100% nạp lần đầu' => '100% First Deposit Bonus',
            'Nạp lần đầu' => 'First Deposit',
            'Nhận ngay phần thưởng. Đừng bỏ lỡ cơ hội vàng này!' => 'Get rewards now. Don\'t miss this golden opportunity!',
            'Hoàn trả thể thao lên đến 1.6%' => 'Sports Cashback up to 1.6%',
            'Hoàn trả slot hằng ngày' => 'Daily Slot Cashback',
            'Hoàn trả 10% cho mỗi lần quay.' => '10% cashback for each spin.',
            'Hoàn trả hằng tuần khi chơi Live Casino' => 'Weekly cashback when playing Live Casino',
            'Hoàn trả vô tận' => 'Unlimited Cashback',
            'Hoàn trả 1.6%' => '1.6% Cashback',
            'Nạp tiền lần đầu' => 'First Deposit',
            'Khuyến mãi 100%' => '100% Bonus',

            // Account Management
            'Tìm kiếm ngân hàng' => 'Search Bank',
            'Xác minh Email' => 'Verify Email',
            'Nhập Email của bạn' => 'Enter your Email',
            'Mã OTP' => 'OTP Code',
            'Xác minh telegram' => 'Verify Telegram',
            'Nhập số tiền nạp' => 'Enter deposit amount',
            'Số tiền nạp' => 'Deposit Amount',
            'Huỷ giao dịch?' => 'Cancel transaction?',
            'Từ chối' => 'Decline',
            'Số serial' => 'Serial Number',
            'Nhập số serial' => 'Enter serial number',
            'Mã thẻ (PIN)' => 'Card PIN',
            'Nhập mã thẻ (PIN)' => 'Enter card PIN',
            'Gần đây' => 'Recent',
            'Chọn ngân hàng của bạn' => 'Choose your bank',
            'Chủ tài khoản' => 'Account Holder',
            'Nhập tên tài khoản' => 'Enter account name',
            'Số tài khoản' => 'Account Number',
            'Nhập số tài khoản' => 'Enter account number',
            'Hướng dẫn mua USDT' => 'USDT Purchase Guide',
            'Tên tài khoản' => 'Account Name',
            'Số tiền rút' => 'Withdrawal Amount',
            'Nhập số tiền rút' => 'Enter withdrawal amount',
            'Nhập 5 số cuối điện thoại' => 'Enter last 5 digits of phone',
            'Số lượng thẻ' => 'Number of Cards',
            'Nhập địa chỉ ví' => 'Enter wallet address',
            'Địa chỉ ví' => 'Wallet Address',

            // Error Messages
            'Oop! Không Tìm Thấy Trang' => 'Oops! Page Not Found',
            'Xin lỗi, trang bạn đang tìm kiếm không được tìm thấy. Vui lòng truy cập khác.' => 'Sorry, the page you are looking for could not be found. Please try another page.',
            'Quay về trang chủ' => 'Back to Home',
            'Không có dữ liệu' => 'No data available',
            'Không tìm thấy người dùng' => 'User not found',

            // Address
            '123 Đường Nguyễn Huệ' => '123 Nguyen Hue Street',
            'Quận 1' => 'District 1',
            'TP. Hồ Chí Minh' => 'Ho Chi Minh City',

            // Brand
            'Nhà cái trực tuyến hàng đầu' => 'Leading Online Betting House',
            'Cuồng nhiệt' => 'Passionate',

            // Logout
            'Đăng xuất tài khoản' => 'Logout account',
            'Bạn muốn thoát tài khoản?' => 'Do you want to logout?',
            'Bỏ qua' => 'Skip',

            // Quay so
            'quay số 1' => 'Number Game 1',
            'quay số 2' => 'Number Game 2',
            'quay số 5' => 'Number Game 5',

            // Generic
            'Đợt' => 'Round',
            'Liên Hệ' => 'Contact',
            'Giúp đỡ' => 'Help',
        ];
    }

    public function translateText($vietnameseText)
    {
        // Kiểm tra trong từ điển trước
        if (isset($this->translations[$vietnameseText])) {
            return $this->translations[$vietnameseText];
        }

        // Nếu không có trong từ điển, thử dịch một số pattern cơ bản
        $text = $vietnameseText;

        // Thay thế một số từ phổ biến
        $commonWords = [
            'của' => 'of',
            'và' => 'and',
            'với' => 'with',
            'trong' => 'in',
            'trên' => 'on',
            'cho' => 'for',
            'từ' => 'from',
            'đến' => 'to',
            'khi' => 'when',
            'nếu' => 'if',
            'hoặc' => 'or',
            'nhưng' => 'but',
            'vì' => 'because',
            'để' => 'to',
            'có' => 'have',
            'là' => 'is',
            'được' => 'be',
            'sẽ' => 'will',
            'đã' => 'has',
            'đang' => 'is',
            'bạn' => 'you',
            'chúng tôi' => 'we',
            'họ' => 'they',
            'tôi' => 'I',
            'này' => 'this',
            'đó' => 'that',
            'những' => 'the',
            'các' => 'the',
            'một' => 'a',
            'không' => 'not',
            'rất' => 'very',
            'nhiều' => 'many',
            'ít' => 'few',
            'lớn' => 'big',
            'nhỏ' => 'small',
            'mới' => 'new',
            'cũ' => 'old',
            'tốt' => 'good',
            'xấu' => 'bad',
            'nhanh' => 'fast',
            'chậm' => 'slow',
        ];

        // Nếu text quá dài hoặc phức tạp, giữ nguyên
        if (mb_strlen($text) > 100) {
            return $vietnameseText;
        }

        // Thay thế một số từ cơ bản
        foreach ($commonWords as $vi => $en) {
            $text = str_ireplace($vi, $en, $text);
        }

        // Nếu sau khi thay thế vẫn giống text gốc, trả về text gốc
        if ($text === $vietnameseText) {
            return $vietnameseText;
        }

        return $text;
    }

    public function translateFile($filePath)
    {
        if (!file_exists($filePath)) {
            echo "File không tồn tại: $filePath\n";
            return false;
        }

        $content = file_get_contents($filePath);
        
        // Parse PHP array
        $array = include $filePath;
        if (!is_array($array)) {
            echo "File không phải là PHP array: $filePath\n";
            return false;
        }

        $translatedArray = $this->translateArray($array);
        
        // Tạo nội dung file mới
        $newContent = "<?php\n\nreturn [\n";
        $newContent .= $this->arrayToString($translatedArray, 1);
        $newContent .= "];\n";

        file_put_contents($filePath, $newContent);
        return true;
    }

    private function translateArray($array)
    {
        $result = [];
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->translateArray($value);
            } else {
                $result[$key] = $this->translateText($value);
            }
        }
        return $result;
    }

    private function arrayToString($array, $indent = 1)
    {
        $result = '';
        $indentStr = str_repeat('    ', $indent);

        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result .= "{$indentStr}'{$key}' => [\n";
                $result .= $this->arrayToString($value, $indent + 1);
                $result .= "{$indentStr}],\n";
            } else {
                $escapedValue = addslashes($value);
                $result .= "{$indentStr}'{$key}' => '{$escapedValue}',\n";
            }
        }

        return $result;
    }

    public function translateAllEnglishFiles()
    {
        $langEnDir = 'lang/en';
        if (!is_dir($langEnDir)) {
            echo "Thư mục lang/en không tồn tại\n";
            return;
        }

        $files = glob($langEnDir . '/*.php');
        foreach ($files as $file) {
            echo "Đang dịch file: $file\n";
            if ($this->translateFile($file)) {
                echo "Đã dịch xong: $file\n";
            } else {
                echo "Lỗi khi dịch: $file\n";
            }
        }
    }
}

// Chạy script
$translator = new VietnameseToEnglishTranslator();
echo "=== DỊCH SANG TIẾNG ANH ===\n\n";
$translator->translateAllEnglishFiles();
echo "\nHoàn thành dịch sang tiếng Anh!\n";
