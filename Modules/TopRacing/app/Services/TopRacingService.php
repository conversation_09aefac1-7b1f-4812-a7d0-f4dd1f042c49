<?php

namespace Modules\TopRacing\Services;

use App\Services\GatewayApi;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\App;
use App\Enums\GatewayEndpoint;
use Exception;

class TopRacingService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getRankData($startTime, $endTime)
    {
        $response = $this->gatewayApi->getPromotion(GatewayEndpoint::TOP_RACING_EVENT_LIST->value, ['startTime' => $startTime, 'endTime' => $endTime]);

        if (isset($response->status) && $response->status === 'OK') {
            $rankData = $response -> data;
            return $rankData;
        }

        return [];
    }
}
