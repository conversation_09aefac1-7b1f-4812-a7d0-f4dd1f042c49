@props(['show', 'type', 'countdown'])

<div class="rank-empty-container flex-col justify-center items-center gap-[10px] w-full h-full pt-[39px] text-[12px] leading-[18px] bg-neutral rounded-t-[4px] xl:text-[14px] xl:leading-[20px] xl:rounded-t-[8px] {{ $show ? "flex" : "hidden"  }} {{ $type === "before" ? "active-before" : "active-empty"}}">
    <img src="{{ Module::asset('topracing:images/rank/cup.avif') }}" alt="cup" class="w-20 h-20 xl:w-[100px] xl:h-[100px]" loading="lazy"/>
    <p class="hidden text-neutral-850 [.active-empty_&]:block">BXH đang cập nhật</p>
    <div class="hidden flex-col items-center [.active-before_&]:flex">
        <p class="text-neutral-850">B<PERSON><PERSON> đầu sau</p>
        <p class="rank-countdown text-neutral-1000">{{ $countdown['days'] ?? 0 }} ngày {{ $countdown['hours'] ?? 0 }} giờ {{ $countdown['minutes'] ?? 0 }} phút {{ $countdown['seconds'] ?? 0 }} giây</p>
    </div>
</div>
