
@php
    $validEvent = request()->get('validTopRacingEvent') ?? false;
    $beforeEvent = request()->get('beforeTopRacingEvent') ?? false;
@endphp

<div class="relative flex justify-center items-center w-full aspect-[390/300] xl:max-h-[490px] xl:aspect-[1920/490]">
    <img
        class="absolute top-0 left-0 z-0 w-full h-full hidden object-cover xl:block"
        src="{{ Module::asset('topracing:images/banner/event-banner-bg.avif') }}"
    />
    <div class="container relative z-[1] hidden w-full h-full xl:block">
        @if ($validEvent || $beforeEvent)
            <img src="{{ Module::asset('topracing:images/banner/top-racing-banner-valid-pc.avif') }}" class="w-full h-full" />
        @else
            <img src="{{ Module::asset('topracing:images/banner/top-racing-banner-expire-pc.avif') }}" class="w-full h-full" />
        @endif
    </div>
    @if ($validEvent || $beforeEvent)
        <img src="{{ Module::asset('topracing:images/banner/top-racing-banner-valid-mb.avif') }}" class="block w-full h-full xl:hidden" />
    @else
        <img src="{{ Module::asset('topracing:images/banner/top-racing-banner-expire-mb.avif') }}" class="block w-full h-full xl:hidden" />
    @endif
</div>
