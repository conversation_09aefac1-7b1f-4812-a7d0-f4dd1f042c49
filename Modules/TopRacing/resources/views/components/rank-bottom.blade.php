@props(['data'])

@php
    $validEvent = request()->get('validTopRacingEvent') ?? false;
    $beforeEvent = request()->get('beforeTopRacingEvent') ?? false;

    $rankPosition = $data['rankPosition'] ?? null;

    $type = null;

    if (!$validEvent && !$beforeEvent) {
        $type = 'expire';
    } else {
        if ($beforeEvent) {
            $type = 'before';
        } else {
            if (Auth::check()) {
                $type = 'auth';
            } else {
                $type = 'login';
            }
        }
    }

    $formatIndex = function ($value) {
        if ($value) {
            return $value > 500 ? '500+' : $value;
        } else {
            return '--';
        }
    }
@endphp

<div data-status="{{ $type }}" class="rank-bottom-container relative z-[1] w-full h-[45px] border-t-[1px] border-neutral bg-rank-list-bottom shadow-rank-bottom xl:h-[57px]">
    <div class="rank-bottom-expire justify-between items-center w-full h-full px-[11px] xl:px-[19px] {{ $type === "expire" ? "flex" : "hidden" }}">
        <p class="text-[10px] leading-[14px] italic text-secondary-600 xl:text-[14px] xl:leading-[20px]">Sự kiện đã kết thúc</p>
        @if (!Auth::check())
            <button class="w-full max-w-[108px] xl:max-w-[153px] xl:hover:opacity-80 brightness-110 transition-all duration-200" onclick="openLogin()">
                <picture>
                    <source 
                        srcset="{{ Module::asset('topracing:images/rank/login-mb.avif') }}" 
                        type="image/avif"
                        media="(max-width: 991px)"
                    >
                    <img 
                        src="{{ Module::asset('topracing:images/rank/login-pc.avif') }}" 
                        alt="event"   
                        class="w-full aspect-[108/28] md2:aspect-[153/40]"
                    >
                </picture>
            </button>
        @endif
    </div>
    <div class="rank-bottom-before justify-between items-center w-full h-full px-[11px] xl:px-[19px] {{ $type === "before" ? "flex" : "hidden" }}">
        <p class="text-[10px] leading-[14px] italic text-secondary-600 xl:text-[14px] xl:leading-[20px]">Sự kiện sắp diễn ra</p>
        @if (!Auth::check())
            <button class="w-full max-w-[108px] xl:max-w-[153px] xl:hover:opacity-80 brightness-110 transition-all duration-200" onclick="openLogin()">
                <picture>
                    <source 
                        srcset="{{ Module::asset('topracing:images/rank/login-mb.avif') }}" 
                        type="image/avif"
                        media="(max-width: 991px)"
                    >
                    <img 
                        src="{{ Module::asset('topracing:images/rank/login-pc.avif') }}" 
                        alt="event"   
                        class="w-full aspect-[108/28] md2:aspect-[153/40]"
                    >
                </picture>
            </button>
        @endif
    </div>
    <div class="rank-bottom-auth w-full h-full overflow-hidden {{ $type === "auth" ? "flex" : "hidden" }}">
        <div class="flex items-center w-[18.75%] h-full md:w-[18.2%] md1:w-[18.1%] xl:w-[23.505%]">
            <div class="relative flex justify-center items-center w-full max-w-[60px] h-full xl:max-w-[169px]">
                <picture>
                    <source 
                        srcset="{{ Module::asset('topracing:images/rank/rank-index-bottom-mb.avif') }}" 
                        type="image/avif"
                        media="(max-width: 1199px)"
                    >
                    <img 
                        src="{{ Module::asset('topracing:images/rank/rank-index-bottom-pc.avif') }}" 
                        alt="rank"   
                        class="absolute top-0 left-0 z-0 w-full h-full"
                    >
                </picture>
                <p class="rank-bottom-index top-racing-index relative z-[1] text-[18px] leading-[45px] font-extrabold xl:text-[30px]">{{ isset($rankPosition -> index) ? $formatIndex($rankPosition -> index) : "--" }}</p>
            </div>
        </div>
        <div class="flex items-center w-[23.37%] h-full md:w-[23.5%] md1:w-[23.3%] lg:w-[23.5%] xl:w-[20.887%]">
            <p class="rank-bottom-name text-[10px] leading-[18px] font-medium text-secondary-600 truncate xxs:text-[12px] xl:text-[14px] xl:leading-[20px]">{{ $rankPosition -> username ?? "--" }}</p>
        </div>
        <div class="flex justify-end items-center w-[22.284%] h-full md:w-[23%] lg:w-[23.2%] xl:w-[20.967%]">
            <p class="rank-bottom-amount text-[10px] leading-[18px] font-medium text-secondary-600 xxs:text-[12px] xl:text-[14px] xl:leading-[20px]">{{ isset($rankPosition -> amount) ? number_format((int)$rankPosition->amount ?? 0, 0, '.', ',') . ' K' : "--" }}</p>
        </div>
        <div class="flex justify-end items-center grow h-full border-box pr-[21px] xl:pr-[83px]">
            <p class="rank-bottom-turnover text-[10px] leading-[18px] font-medium text-secondary-600 xxs:text-[12px] xl:text-[14px] xl:leading-[20px]">{{ isset($rankPosition -> turnover) ? number_format((int)$rankPosition->turnover ?? 0, 0, '.', ',') . ' VND' : "--" }}</p>
        </div>
    </div>
    <div class="rank-bottom-login justify-between items-center w-full h-full px-[11px] xl:px-[19px] {{ $type === "login" ? "flex" : "hidden" }}">
        <p class="w-max max-w-[188px] text-[10px] leading-[14px] italic text-secondary-600 xl:max-w-max xl:text-[14px] xl:leading-[20px]">Tham gia ngay để có thể thu về bạc tỷ từ chương trình Đua Top Tích Lũy</p>
        <button class="w-full max-w-[108px] xl:max-w-[153px] xl:hover:opacity-80 brightness-110 transition-all duration-200" onclick="openLogin()">
            <picture>
                <source 
                    srcset="{{ Module::asset('topracing:images/rank/login-mb.avif') }}" 
                    type="image/avif"
                    media="(max-width: 991px)"
                >
                <img 
                    src="{{ Module::asset('topracing:images/rank/login-pc.avif') }}" 
                    alt="event"   
                    class="w-full aspect-[108/28] md2:aspect-[153/40]"
                >
            </picture>
        </button>
    </div>
</div>
