@props([
    'key' => 0, 
    'data' => null,
    'username' => '', 
    'amount' => '', 
    'turnover' => '',
    'activeClass' => ''
])

<div class="flex w-full h-[38px] bg-neutral rounded-[4px] overflow-hidden xl:h-[50px] xl:rounded-[8px] {{ $activeClass }}">
    <div class="flex items-center w-[17.515%] h-full xl:w-[22.667%]">
        <div class="relative flex justify-center items-center w-full max-w-[49px] h-full xl:max-w-[129.5px]">
            <picture>
                <source 
                    srcset="{{ Module::asset('topracing:images/rank/rank-index-mb.avif') }}" 
                    type="image/avif"
                    media="(max-width: 1199px)"
                >
                <img 
                    src="{{ Module::asset('topracing:images/rank/rank-index-pc.avif') }}" 
                    alt="rank"   
                    class="absolute top-0 left-0 z-0 w-full h-full"
                >
            </picture>
            <p class="top-racing-index relative z-[1] text-[18px] leading-[45px] font-extrabold xl:text-[30px]">{{ $key }}</p>
        </div>
    </div>
    <div class="flex items-center w-[24.295%] h-full xl:w-[21.583%]">
        <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 truncate xxs:text-[12px] xl:text-[14px] xl:leading-[20px] [.active_&]:text-secondary-600">{{ $username }}</p>
    </div>
    <div class="flex justify-end items-center w-[23.165%] h-full xl:w-[21.667%]">
        <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 xxs:text-[12px] xl:text-[14px] xl:leading-[20px] [.active_&]:text-secondary-600">{{ isset($data->amount) ? number_format((int)$data->amount ?? 0, 0, '.', ',') : $amount }} K</p>
    </div>
    <div class="flex justify-end items-center grow h-full pr-[14px] xl:pr-[63px]">
        <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 xxs:text-[12px] xl:text-[14px] xl:leading-[20px] [.active_&]:text-secondary-600">{{ isset($data->turnover) ? number_format((int)$data->turnover ?? 0, 0, '.', ',') : $turnover }} VND</p>
    </div>
</div>
