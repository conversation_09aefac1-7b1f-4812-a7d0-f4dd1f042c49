@props(['data'])

@php
    $indexTime = $data['indexTime'];
    $status = $data['status'];
    $defaultTime = $data['defaultTime'];
    $currentTime = $data['currentTime'];
    $listTime = $data['listTime'] ?? [];
    $topRankList =  $data['topRankList'] ?? [];

    $rank1 = $topRankList[0] ?? null;
    $rank2 = $topRankList[1] ?? null;
    $rank3 = $topRankList[2] ?? null;

    $format = function ($value) {
        return $value ? number_format((int)$value ?? 0, 0, '.', ',') : "";
    };
@endphp

<div 
    class="rank-container flex flex-col items-center gap-3 xl:gap-[27px]"
    data-active="{{ $indexTime }}"
    data-status="{{ $status }}"
    data-start="{{ $defaultTime['start'] ?? "" }}"
    data-current="{{ $currentTime->format('d/m/Y H:i:s') }}"
>
    <p class="text-[20px] leading-[24px] text-neutral-950 uppercase font-redzone xl:text-[32px] xl:leading-[38px]">
        Bảng xếp hạng
    </p>
    <div class="relative block w-full pt-[163px] xl:hidden">
        <div class="absolute top-0 left-1/2 translate-x-[-50%] z-0 flex justify-center w-full max-w-[370px] h-[195px]">
            <div class="absolute bottom-0 left-0 z-0 w-[33.245%] aspect-[123/188]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-2-mb.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute top-[20.75%] left-[6.5%] z-[1] flex flex-col items-center gap-[6px] w-[84.55%]">
                    <p class="top-rank-content top-rank-name-2 w-full text-[12px] leading-[18px] text-neutral-1900 font-medium truncate text-center">{{ isset($rank2 -> username) ? $rank2 -> username : '--' }}</p>
                    <div class="flex flex-col gap-2 w-full">
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tổng cược</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="top-rank-content top-rank-amount-2 text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">{{ isset($rank2 -> amount) ? $format($rank2 -> amount) . ' K' : '--' }}</p>
                            </div>
                        </div>
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tiền thưởng</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">37,500,000 VND</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative z-[1] w-[35.138%] aspect-[130/195]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-1-mb.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute top-[22.05%] left-1/2 translate-x-[-50%] z-[1] flex flex-col items-center gap-[6px] w-[87.69%]">
                    <p class="top-rank-content top-rank-name-1 w-full text-[12px] leading-[18px] text-quaternary-1100 font-medium truncate text-center">{{ isset($rank1 -> username) ? $rank1 -> username : '--' }}</p>
                    <div class="flex flex-col gap-2 w-full">
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tổng cược</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="top-rank-content top-rank-amount-1 text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">{{ isset($rank1 -> amount) ? $format($rank1 -> amount) . ' K' : '--' }}</p>
                            </div>
                        </div>
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tiền thưởng</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">60,000,000 VND</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="absolute bottom-0 right-0 z-0 w-[33.245%] aspect-[123/188]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-3-mb.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute top-[20.75%] right-[6.5%] z-[1] flex flex-col items-center gap-[6px] w-[84.55%]">
                    <p class="top-rank-content top-rank-name-3 w-full text-[12px] leading-[18px] text-quaternary-1000 font-medium truncate text-center">{{ isset($rank3 -> username) ? $rank3 -> username : '--' }}</p>
                    <div class="flex flex-col gap-2 w-full">
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tổng cược</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="top-rank-content top-rank-amount-3 text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">{{ isset($rank3 -> amount) ? $format($rank3 -> amount) . ' K' : '--' }}</p>
                            </div>
                        </div>
                        <div class="flex flex-col items-center w-full">
                            <div class="py-[2px] px-1 bg-white-50-2 rounded-t-[4px]">
                                <p class="text-[8px] leading-[12px] font-bold text-neutral-1000 uppercase">tiền thưởng</p>
                            </div>
                            <div class="flex justify-center w-full py-[1.5px] px-[2px] bg-white-30 border-[0.5px] border-white-50-2 rounded-[4px]">
                                <p class="text-[10px] leading-[18px] font-medium text-neutral-1000 uppercase xxs:text-[12px]">15,000,000 VND</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="relative z-[1] w-full bg-rank-list rounded-[12px] border border-neutral overflow-hidden">
            <div class="relative z-0 flex flex-col gap-2 pt-[7px] px-[7px] [&_.dropdown-list-wrap]:h-max [&_.dropdown-list-wrap]:max-h-[264px] [&_.dropdown-item_button]:px-[7px]">
                <x-kit.dropdown-new 
                    placeholderIcon="" 
                    id="filter" 
                    :options="$listTime" 
                    :defaultValue="$defaultTime"
                    dropdownContainerClass="rank-filter-mb"
                />
                <x-topracing::rank-list :data="$data"/>
            </div>
            <x-topracing::rank-bottom :data="$data"/>
        </div>
    </div>
    <div class="relative hidden w-full aspect-[1240/592] xl:block">
        <img src="{{ Module::asset('topracing:images/rank/rank-bg.avif') }}" alt="rank bg" class="absolute top-0 left-0 z-[1] w-full h-full" />
        <div class="absolute top-[11.486%] right-[1.612%] z-10 w-[15%] [&_.dropdown-button]:w-max [&_.dropdown-button]:py-[7px] [&_.dropdown-button]:px-2 [&_.dropdown-button]:h-9 [&_.dropdown-button]:gap-1 [&_.dropdown-button]:border-0 [&_.dropdown-item_button]:px-[7px] [&_.dropdown-icon-base]:hidden [&_.dropdown-list-wrap]:h-max [&_.dropdown-list-wrap]:max-h-[264px]">
            <x-kit.dropdown-new 
                placeholderIcon="" 
                id="filter" 
                :options="$listTime" 
                :defaultValue="$defaultTime"
                dropdownContainerClass="rank-filter-pc"
            />
        </div>
        <div class="absolute bottom-[80.74%] left-0 z-0 w-[92.177%] aspect-[1143/124]">
            <div class="absolute left-0 bottom-0 z-0 w-[32.108%] aspect-[367/101]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-2-pc.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute bottom-[23.762%] left-[29.7%] flex flex-col gap-2 w-full max-w-[58.583%]">
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tổng cược</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="top-rank-content top-rank-amount-2 text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">{{ isset($rank2 -> amount) ? $format($rank2 -> amount) . ' K' : '--' }}</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tiền thưởng</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">37,500,000 VND</p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-[-15.84%] left-[29.7%] z-[1] flex justify-center w-full max-w-[58.583%] line-clamp-1">
                    <p class="top-rank-content top-rank-name-2 max-w-full text-[18px] leading-[26px] font-medium text-gray-400 text-center truncate">{{ isset($rank2 -> username) ? $rank2 -> username : '--' }}</p>
                </div>
            </div>
            <div class="absolute left-[28.346%] bottom-0 z-[1] w-[34.033%] aspect-[389/124]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-1-pc.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute bottom-[19.354%] left-[35.48%] flex flex-col gap-2 w-full max-w-[55.27%]">
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tổng cược</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="top-rank-content top-rank-amount-1 text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">{{ isset($rank1 -> amount) ? $format($rank1 -> amount) . ' K' : '--' }}</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tiền thưởng</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">60,000,000 VND</p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-[8.87%] left-[35.48%] z-[1] flex justify-center w-full max-w-[55.27%] px-[13px] line-clamp-1">
                    <p class="top-rank-content top-rank-name-1 max-w-full text-[18px] leading-[26px] font-medium text-quaternary-1100 text-center truncate">{{ isset($rank1 -> username) ? $rank1 -> username : '--' }}</p>
                </div>
            </div>
            <div class="absolute right-0 bottom-0 z-[2] w-[39.37%] aspect-[450/99]">
                <img src="{{ Module::asset('topracing:images/rank/rank-type-3-pc.avif') }}" alt="rank" class="relative top-0 left-0 z-0 w-full h-full" />
                <div class="absolute bottom-[24.242%] left-[21.555%] z-[1] flex flex-col gap-2 w-full max-w-[47.777%]">
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tổng cược</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="top-rank-content top-rank-amount-3 text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">{{ isset($rank3 -> amount) ? $format($rank3 -> amount) . ' K' : '--' }}</p>
                        </div>
                    </div>
                    <div class="grid grid-cols-[79fr_134fr] h-6 rounded-[4px] border-[0.5px] border-white-50-2-2">
                        <div class="flex items-center h-full pl-[3.5px] bg-white-50-2">
                            <p class="text-[10px] leading-[14px] font-bold uppercase text-neutral-1000">tiền thưởng</p>
                        </div>
                        <div class="flex justify-end items-center h-full pr-[7.5px] bg-white-30">
                            <p class="text-[14px] leading-[20px] font-medium uppercase text-neutral-1000">15,000,000 VND</p>
                        </div>
                    </div>
                </div>
                <div class="absolute top-[-20.202%] left-[21.555%] z-[1] flex justify-center w-full max-w-[47.777%] line-clamp-1">
                    <p class="top-rank-content top-rank-name-3 max-w-full text-[18px] leading-[26px] font-medium text-quaternary-1200 text-center truncate">{{ isset($rank3 -> username) ? $rank3 -> username : '--' }}</p>
                </div>
            </div>
        </div>
        <div class="absolute bottom-[58px] top-[20%] left-[1.612%] right-[1.612%] z-[2]">
            <x-topracing::rank-list :data="$data"/>
        </div>
        <div class="absolute bottom-[1px] left-[1px] right-[1px] z-[2] w-[calc(100%-2px)] rounded-b-[16px] shadow-rank-bottom overflow-hidden">
            <x-topracing::rank-bottom :data="$data"/>
        </div>
    </div>
</div>