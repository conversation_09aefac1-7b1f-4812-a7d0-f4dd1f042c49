@pushOnce('css')
    <link rel="stylesheet" href="{{ Module::asset('topracing:css/app.css') }}">
@endPushOnce

@props(['data'])

<x-layout>
    <div class="flex flex-col gap-6 pb-[84px] bg-neutral-50 xl:gap-[40px] xl:pb-[80px]">
        <x-topracing::banner />
        <x-topracing::list-gift />
        <div class="container flex flex-col gap-6 xl:gap-[40px]">
            <x-topracing::rank :data="$data"/>
            <x-topracing::type-involvement />
            <x-topracing::applicable-games />
            <x-topracing::term />
        </div>
    </div>
</x-layout>

<script src="{{ Module::asset('topracing:js/utils.js') }}"></script>
