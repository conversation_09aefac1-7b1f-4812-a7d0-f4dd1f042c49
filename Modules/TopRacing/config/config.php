<?php
use App\Enums\UrlPathEnum;

$brand = config('app.brand_name');

return [
    'name' => 'TopRacing',
    'startTopRacingEvent' => env('START_TOP_RACING_EVENT_DAY', ''),
    'endTopRacingEvent' => env('END_TOP_RACING_EVENT_DAY', ''),
    'daysPerBatch' => env('DAYS_PER_BATCH_TOP_RACING_EVENT', 7),
    'listGift' => [
        'topracing:images/listGift/1.avif',
        'topracing:images/listGift/2.avif',
        'topracing:images/listGift/3.avif',
        'topracing:images/listGift/4.avif',
        'topracing:images/listGift/5.avif',
        'topracing:images/listGift/6.avif',
        'topracing:images/listGift/7.avif',
        'topracing:images/listGift/8.avif',
        'topracing:images/listGift/9.avif',
    ],
    'listTerm' => [
        [
            'type' => 'text',
            'content' => 'Giải thưởng sẽ được cập nhật trong vòng 12 giờ kể từ lúc tổng kết giải thưởng mỗi tuần.'
        ],
        [
            'type' => 'list',
        ],
        [
            'type' => 'text',
            'content' => 'Đối tượng áp dụng: Chương trình dành cho tất cả thành viên trên ' .  $brand . ' đang hưởng khuyến mãi hoàn trả.'
        ],
        [
            'type' => 'text',
            'content' => 'Thành viên hưởng khuyến mãi 100%, trong thời gian diễn ra sự kiện hoàn thành khuyến mãi 100% thì vẫn có cơ hội tham gia sự kiện và nhận giải thưởng nếu đạt đủ các điều kiện.'
        ],
        [
            'type' => 'text',
            'content' => 'Các vé cân kèo ở tất cả game có cược 2 bên, các vé hoà tiền, các vé bị huỷ cược và các vé thể thao có tỷ lệ cược dưới 1.5 (DEC), -200 (US), -2 (INDO), 0.5 (MY), 0.5 (HK) sẽ không được tính vào doanh thu cược của thành viên.'
        ],
        [
            'type' => 'text',
            'content' => 'Đối với các thành viên có hành vi gian lận, ' . $brand . ' có quyền huỷ tư cách nhận giải thưởng.'
        ],
        [
            'type' => 'text',
            'content' => $brand . ' có quyền thay đổi, chỉnh sửa hoặc huỷ chương trình này, hoặc một phần của chương trình vào bất cứ thời điểm và thời gian nào mà không cần thông báo trước.'
        ],
        [
            'type' => 'text',
            'content' => 'Điều khoản và Điều kiện chung của ' .  $brand . ' vẫn được áp dụng cho chương trình này.'
        ],
    ],
    'listTypeInvolvement' => [
        [
            'link' => 'topracing:images/type-involvement/type-1.avif',
            'content' => 'Thành viên không cần đăng ký tham gia chương trình.',
            'class' => 'left-5'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-2.avif',
            'content' => 'Top 500 thành viên có tổng tiền đặt cược cao nhất ở các vòng sẽ nhận ngay tiền thưởng từ ' . $brand . '.',
            'class' => 'left-[19px]'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-3.avif',
            'content' => 'Dựa trên tổng tiền đặt cược thành công của người chơi, càng nhiều càng sẽ có cơ hội đạt thứ hạng cao hơn.',
            'class' => 'left-5'
        ],
        [
            'link' => 'topracing:images/type-involvement/type-4.avif',
            'content' => 'Tổng giải thưởng của mỗi vòng đua top sẽ được tích lũy dựa trên tổng cược của tất cả người chơi tham gia đua top.',
            'class' => 'left-[30px]'
        ],
    ],
    'listGame' => [
        [
            'type' => 'link',
            'link' => UrlPathEnum::SLOTS,
            'img' => 'topracing:images/applicable-games/1.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::SPORTS,
            'img' => 'topracing:images/applicable-games/2.avif'
        ],
                [
            'type' => 'link',
            'link' => UrlPathEnum::GAME_OTHER,
            'img' => 'topracing:images/applicable-games/3.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::CASINO,
            'img' => 'topracing:images/applicable-games/4.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::GAME_CARD,
            'img' => 'topracing:images/applicable-games/5.avif'
        ],
        [
            'type' => 'link',
            'link' => UrlPathEnum::LOTTERY,
            'img' => 'topracing:images/applicable-games/6.avif'
        ],
        [
            'type' => 'game',
            'link' => UrlPathEnum::E_SPORTS,
            'img' => 'topracing:images/applicable-games/7.avif'
        ],
    ],
    'listTime' => [
        [
            'key' => '1',
            'name' => 'Đợt 1: 04/08 → 10/08',
            'value' => '04/08/2025.10/08/2025',
            'label' => 'Đợt 1: 04/08 → 10/08',
            'icon' => '',
            'start' => '04/08/2025',
            'end' => '10/08/2025',
        ],
        [
            'key' => '2',
            'name' => 'Đợt 2: 11/08 → 17/08',
            'value' => '11/08/2025.17/08/2025',
            'label' => 'Đợt 2: 11/08 → 17/08',
            'icon' => '',
            'start' => '11/08/2025',
            'end' => '17/08/2025',
        ],
        [
            'key' => '3',
            'name' => 'Đợt 3: 18/08 → 24/08',
            'value' => '18/08/2025.24/08/2025',
            'label' => 'Đợt 3: 18/08 → 24/08',
            'icon' => '',
            'start' => '18/08/2025',
            'end' => '24/08/2025',
        ],
        [
            'key' => '4',
            'name' => 'Đợt 4: 25/08 → 31/08',
            'value' => '25/08/2025.31/08/2025',
            'label' => 'Đợt 4: 25/08 → 31/08',
            'icon' => '',
            'start' => '25/08/2025',
            'end' => '31/08/2025',
        ],
        [
            'key' => '5',
            'name' => 'Đợt 5: 01/09 → 07/09',
            'value' => '01/09/2025.07/09/2025',
            'label' => 'Đợt 5: 01/09 → 07/09',
            'icon' => '',
            'start' => '01/09/2025',
            'end' => '07/09/2025',
        ],
        [
            'key' => '6',
            'name' => 'Đợt 6: 08/09 → 14/09',
            'value' => '08/09/2025.14/09/2025',
            'label' => 'Đợt 6: 08/09 → 14/09',
            'icon' => '',
            'start' => '08/09/2025',
            'end' => '14/09/2025',
        ],
        [
            'key' => '7',
            'name' => 'Đợt 7: 15/09 → 21/09',
            'value' => '15/09/2025.21/09/2025',
            'label' => 'Đợt 7: 15/09 → 21/09',
            'icon' => '',
            'start' => '15/09/2025',
            'end' => '21/09/2025',
        ],
        [
            'key' => '8',
            'name' => 'Đợt 8: 22/09 → 28/09',
            'value' => '22/09/2025.28/09/2025',
            'label' => 'Đợt 8: 22/09 → 28/09',
            'icon' => '',
            'start' => '22/09/2025',
            'end' => '28/09/2025',
        ],

          [
            'key' => '9',
            'name' => 'Đợt 9: 23/06 → 29/06',
            'value' => '23/06/2025.29/06/2025',
            'label' => 'Đợt 9: 23/06 → 29/06',
            'icon' => '',
            'start' => '23/06/2025',
            'end' => '29/06/2025',
        ],
    ],
];
