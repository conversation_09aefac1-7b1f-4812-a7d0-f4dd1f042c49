document.addEventListener("DOMContentLoaded", async () => {
    // Change tab
    const btnConditions = document.getElementById("btn-conditions");
    const btnRules = document.getElementById("btn-rules");
    const tabConditions = document.getElementById("tab-conditions");
    const tabRules = document.getElementById("tab-rules");

    btnConditions.addEventListener("click", () => {
        tabConditions.classList.remove("hidden");
        tabRules.classList.add("hidden");

        btnConditions.classList.add(
            "text-white",
            "bg-[linear-gradient(173.69deg,_#51C6FE_4.62%,_#3D98F1_40.12%,_#2273D7_94.72%)]"
        );
        btnConditions.classList.remove("bg-[#DEF2FF]", "text-[#27272A]");
        btnRules.classList.remove(
            "text-white",
            "bg-[linear-gradient(173.69deg,_#51C6FE_4.62%,_#3D98F1_40.12%,_#2273D7_94.72%)]"
        );
        btnRules.classList.add("bg-[#DEF2FF]", "text-[#27272A]");
    });

    btnRules.addEventListener("click", () => {
        tabConditions.classList.add("hidden");
        tabRules.classList.remove("hidden");

        btnRules.classList.add(
            "text-white",
            "bg-[linear-gradient(173.69deg,_#51C6FE_4.62%,_#3D98F1_40.12%,_#2273D7_94.72%)]"
        );
        btnRules.classList.remove("bg-[#DEF2FF]", "text-[#27272A]");
        btnConditions.classList.remove(
            "text-white",
            "bg-[linear-gradient(173.69deg,_#51C6FE_4.62%,_#3D98F1_40.12%,_#2273D7_94.72%)]"
        );
        btnConditions.classList.add("bg-[#DEF2FF]", "text-[#27272A]");
    });
});
