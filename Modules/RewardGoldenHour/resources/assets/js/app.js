const checkRewardGoldenHour = async () => {
    try {
        const { status, data } = await fetchData('/api-promotion/v1/lixi/newyear');
        if (status === 'OK') {
            return data.check;
        }
        return false;
    } catch (error) {
        return false
    }

}
const claimRewardGoldenHour = async () => {
    openModal(rewardGoldenHourResultModal, true, 'reward-golden-hour-result-modal');

    const modalElement = document.querySelector('.js-golden-hour-award-modal');

    const awardContent = modalElement.querySelector('.js-golden-hour-award-content');
    const awardTitle = modalElement.querySelector('.js-golden-hour-award-title');
    const awardDescription = modalElement.querySelector('.js-golden-hour-award-description');
    const awardBtnText = modalElement.querySelector('.js-golden-hour-award-btn-text');
    const awardBtn = modalElement.querySelector('.js-golden-hour-award-btn');

    let isClaimSuccess = false;

    try {
        const { status, data } = await submitData('/api-promotion/v1/lixi/newyear');
        if (status === 'OK') {
            awardContent.classList.add('mt-[71px]');
            awardContent.classList.add('xl:mt-[101px]');
            awardTitle.classList.add('mb-4');
            awardTitle.classList.add('xl:mb-5');

            awardTitle.innerHTML = 'CHÚC MỪNG';
            awardDescription.innerHTML = `
                Quý Khách đã nhận được <span class="text-[#EA4D4D] font-bold">${data.amount_txt} VND</span> từ chương trình <span class="font-bold">SĂN THƯỞNG GIỜ VÀNG</span> mỗi ngày
            `;
            awardBtnText.innerHTML = 'CƯỢC NGAY';

            awardBtn.addEventListener('click', () => {
                window.closeModal();
                window.location.href = '/cong-game';
            });
            return;
        }
        isClaimSuccess = false;
    } catch (error) {
        console.error(error);
        isClaimSuccess = false;
    }
    if (!isClaimSuccess) {
        awardContent.classList.remove('mt-[71px]');
        awardContent.classList.remove('xl:mt-[101px]');
        awardTitle.classList.remove('mb-4');
        awardTitle.classList.remove('xl:mb-5');

        awardContent.classList.add('mt-[106px]');
        awardContent.classList.add('xl:mt-[144px]');
        awardTitle.classList.add('mb-[6px]');
        awardTitle.classList.add('xl:mb-2');


        awardTitle.innerHTML = 'ĐÃ TRAO HẾT GIẢI';
        awardDescription.innerHTML = `Chúc Quý Khách may mắn lần sau!`;
        awardBtnText.innerHTML = 'ĐÓNG';
        awardBtn.addEventListener('click', () => {
            window.closeModal();
        });
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    const check = await checkRewardGoldenHour();
    if (check) {
        window.claimRewardGoldenHour = claimRewardGoldenHour;
        openModal(rewardGoldenHourGiftModal, true, 'reward-golden-hour-gift-modal');
    }
});
