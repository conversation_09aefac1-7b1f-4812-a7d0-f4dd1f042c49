@php
    use Carbon\Carbon;  

    $startEventTime = env('START_REWARD_EVENT_TIME');
    $endEventHourTime = env('END_REWARD_EVENT_TIME');
    $startEventDay = env('START_REWARD_EVENT_DAY');
    $endEventDay = env('END_REWARD_EVENT_DAY');

    $startEventArr = explode(":", $startEventTime);
    $endEventArr = explode(":", $endEventHourTime);

    $timeServer = Carbon::now('Asia/Ho_Chi_Minh');

    $isLoggedIn = Auth::check() ?? false;
    $depositButtonBg = Module::asset('rewardgoldenhour:images/deposit-button-bg.png');
    $eventStatus = config('rewardgoldenhour.event_status');
    $userTurnover = $userTurnover ?? null;
    $currentHour = (int) date('H');
    $currentMinute = (int) date('i');

    $validEvent = request()->get('validRewardEvent');
    $beforeEvent = request()->get('beforeRewardEvent');

    $currentTime = $timeServer;
    $startHour = $timeServer->copy()->startOfDay()->addHours((int)$startEventArr[0] ?? 0)->addMinutes((int)$startEventArr[1] ?? 0);
    $endHour = $timeServer->copy()->startOfDay()->addHours((int)$endEventArr[0] ?? 0)->addMinutes((int)$endEventArr[1] ?? 0);

    $checkEventStatus = '';
    $progress = 0;
    
    if ($userTurnover) {
        $checkEventStatus = $eventStatus['claimed'];
        $userTurnover = $userTurnover[0];
        $progress = $userTurnover->turnover / $userTurnover->rolling_main * 100;
    } else {
        if ($currentTime < $startHour) {
            // Before 12:00 - Event not started
            $checkEventStatus = $eventStatus['not_started'];
        } elseif ($currentTime >= $endHour) {
            // After 13:00 - Event ended
            $checkEventStatus = $eventStatus['ended'];
        } else {
            // Between 12:00 and 13:00 - Event ongoing
            $checkEventStatus = $eventStatus['valid'];
        }
    }
@endphp

@if ($beforeEvent)
    <div class="flex flex-col text-[12px] leading-[18px] text-neutral-1000 xl:flex-row xl:items-center xl:gap-[2px] xl:text-[14px] xl:leading-[20px]">
        <p>Sự kiện sẽ diễn ra vào 00:00, {{ $startEventDay }} → 23:59, {{ $endEventDay }}.</p>
    </div>
@else
    @if (!$validEvent)
        <div class="flex flex-col text-[12px] leading-[18px] text-neutral-1000 xl:flex-row xl:items-center xl:gap-[2px] xl:text-[14px] xl:leading-[20px]">
            <p>Sự kiện đã kết thúc</p>
        </div>
    @else
        {{-- chưa diễn ra --}}
        @if ($checkEventStatus === $eventStatus['not_started'])
            <p class="text-[12px] leading-[18px] xl:text-[14px] xl:leading-[20px] text-neutral-1000">
                Sự kiện Nạp Giờ Vàng mở thưởng vào {{ $startEventTime }} - {{ $endEventHourTime }}
            </p>
        @endif

        {{-- Đã kết thúc --}}
        @if ($checkEventStatus === $eventStatus['ended'])
            <div class="flex flex-col text-[12px] leading-[18px] text-neutral-1000 xl:flex-row xl:items-center xl:gap-[2px] xl:text-[14px] xl:leading-[20px]">
                <p>Sự kiện hôm nay đã kết thúc.</p>
                <p>Quý khách vui lòng quay lại vào {{ $startEventTime }} - {{ $endEventHourTime }} ngày mai</p>
            </div>
        @endif

        {{-- Đang diễn ra --}}
        @if ($checkEventStatus === $eventStatus['valid'])
            <div class="flex justify-between items-center gap-[7px] w-full">
                <p class="flex items-center h-full text-[12px] leading-[18px] text-neutral-1000 xl:text-[14px] xl:leading-[20px]">
                    Nạp ngay để nhận thưởng Nạp Giờ Vàng
                </p>
                <button class="js-reward-deposit-now">
                    <img 
                        src="{{ Module::asset('rewardgoldenhour:images/deposit-button.webp') }}" 
                        alt="deposit button"
                        class="w-[111px] h-[30px] xl:w-[151px] xl:h-[40px]"
                    >
                </button>
            </div>
        @endif

        {{-- Đã nhận thưởng --}}
        @if ($checkEventStatus === $eventStatus['claimed'])
            <div class="flex justify-between items-center gap-[7px] w-full text-[12px] leading-[18px] xl:text-[14px] xl:leading-[20px]">
                <div class="flex flex-col gap-1 w-full min-w-[229px] {{ $progress === 100 ? "max-w-full xl:max-w-full" : "max-w-max xl:max-w-[437px]" }}">
                    <div class="flex justify-between whitespace-nowrap">
                        <p class="text-neutral-1000">Hoàn thành vòng cược</p>
                        <p class="text-secondary-500 font-bold xl:font-semibold">{{ number_format((int)$userTurnover->turnover / 1000, 0, '.', ',') }} K/{{ number_format((int)$userTurnover->rolling_main / 1000, 0, '.', ',') }} K</p>
                    </div>
                    <div class="relative w-full h-[10px] overflow-hidden rounded-full">
                        <div 
                            style="width: {{ $progress }}%"
                            class="absolute rounded-full top-0 left-0 h-full bg-event-progress"
                        >
                        </div>
                        <div class="w-full h-full bg-neutral"></div>
                    </div>
                </div>

                @if ($progress < 100)
                    <button class="js-reward-deposit-now">
                        <img 
                            src="{{ Module::asset('rewardgoldenhour:images/deposit-button.webp') }}" 
                            alt="deposit button"
                            class="w-[111px] h-[30px] xl:w-[151px] xl:h-[40px]"
                        >
                    </button>
                @endif
            </div>
        @endif
    @endif
@endif
