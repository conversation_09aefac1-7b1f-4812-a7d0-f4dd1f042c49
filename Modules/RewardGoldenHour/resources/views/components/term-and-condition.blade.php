@php
    $rules = config('rewardgoldenhour.rules');
    $conditions = config('rewardgoldenhour.conditions');
@endphp

<section class="hidden grid-cols-2 gap-10 xl:grid">
    <div class="h-full pl-4">
        <div class="flex flex-col gap-[18px] pt-[19px] pb-[23px] rounded-[8px] border-[1px] border-neutral bg-event-rule-bg">
            <div class="relative flex justify-center items-center pb-[2px] w-full h-[60px]">
                <p class="relative z-[1] text-[32px] leading-[38px] text-neutral-950 font-utm capitalize">Thể lệ tham gia</p>
                <span class="absolute top-0 right-0 z-0 w-[calc(100%+16px)] h-full bg-event-rule-line"></span>
                <span class="absolute bottom-0 left-0 w-0 h-0 border-t-[16px] border-l-[16px] border-l-transparent border-danger-1000 translate-x-[-100%] translate-y-[100%]"></span>
            </div>
            <div class="flex flex-col gap-3 h-[260px] px-[21px]">
                @foreach ($rules as $rule)
                    <div class="flex gap-1">
                        <img 
                            src="{{ Module::asset('rewardgoldenhour:images/list-item-image.avif') }}"
                            class="size-5" alt="rules-icon"
                        >
                        <p class="text-[14px] leading-[20px] text-black-900">{!! $rule !!}</p>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="h-full pl-4">
        <div class="flex flex-col gap-[18px] pt-[19px] pb-[23px] rounded-[8px] border-[1px] border-neutral bg-event-rule-bg">
            <div class="relative flex justify-center items-center pb-[2px] w-full h-[60px]">
                <p class="relative z-[1] text-[32px] leading-[38px] text-neutral-950 font-utm capitalize">Điều kiện & Điều khoản</p>
                <span class="absolute top-0 right-0 z-0 w-[calc(100%+16px)] h-full bg-event-rule-line"></span>
                <span class="absolute bottom-0 left-0 w-0 h-0 border-t-[16px] border-l-[16px] border-l-transparent border-danger-1000 translate-x-[-100%] translate-y-[100%]"></span>
            </div>
            <div class="h-[260px] pl-[21px] pr-[7px]">
                <div class="scrollbar-without-bg h-full overflow-auto">
                    <div class="flex flex-col gap-3 pr-2">
                        @foreach ($conditions as $condition)
                            <div class="flex gap-1">
                                <img src="{{ Module::asset('rewardgoldenhour:images/list-item-image.avif') }}"
                                    class="h-[20px] w-[20px]" alt="rules-icon">
                                <div class="flex flex-col gap-1 text-[14px] leading-[20px] text-black-900">
                                    <p>{!! $condition['title'] !!}</p>
                                    @if (isset($condition['list']))
                                        <ul class="flex flex-col gap-1">
                                            @foreach($condition['list'] as $item)
                                                <li>
                                                    <div class="flex gap-[6px]">
                                                        <span class="relative translate-x-[-1px] w-[4px]">•</span>
                                                        <p>{!! $item !!}</p>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section class="block pl-[6px] xl:hidden">
    <div class="flex flex-col gap-[17px] pt-[14px] pb-[15px] rounded-[8px] border-[1px] border-neutral bg-event-rule-bg">
        <div class="relative translate-x-[1px] flex justify-end items-start h-[43px]">
            <span class="absolute bottom-0 left-[-8px] w-0 h-0 border-t-[6px] border-l-[6px] border-l-transparent border-danger-1000 translate-y-[100%]"></span>
            <button data-type="rule" class="js-reward-policy-button active absolute top-[5px] left-[-8px] z-0 flex justify-center items-center w-[calc(50%+7px)] h-[38px] pl-[7px] bg-event-rule-tag [&.active]:bg-event-rule-tag-active [&.active]:z-[1]">
                <p class="tex-[18px] leading-[22px] font-utm text-secondary-1100 capitalize pointer-events-none [.active_&]:text-neutral-950">Thể lệ</p>
            </button>
            <button data-type="term" class="js-reward-policy-button relative z-0 flex justify-center items-center w-[calc(50%+7px)] h-[38px] pl-[7px] bg-event-rule-tag [&.active]:bg-event-rule-tag-active [&.active]:z-[1]">
                <span class="absolute top-0 left-0 block w-0 h-0 border-b-[6px] border-r-[6px] border-r-transparent border-secondary-1100 [.active_&]:hidden"></span>
                <span class="absolute bottom-0 left-0 hidden w-0 h-0 border-t-[6px] border-l-[6px] border-l-transparent border-secondary-1100 translate-y-[100%] [.active_&]:block"></span>
                <p class="tex-[18px] leading-[22px] font-utm text-secondary-1100 capitalize pointer-events-none [.active_&]:text-neutral-950">Điều khoản</p>
            </button>
        </div>
        <div class="js-reward-policy-term hidden h-[380px] pl-[7px] pr-[1px]">
            <div class="js-reward-policy-term-scroll scrollbar-without-bg h-full pr-[6px] overflow-auto">
                <div class="flex flex-col gap-2">
                    @foreach ($conditions as $condition)
                        <div class="flex gap-1">
                            <img 
                                src="{{ Module::asset('rewardgoldenhour:images/list-item-image.avif') }}"
                                class="h-[20px] w-[20px]"
                                alt="rules-icon"
                            >
                            <div class="flex flex-col gap-1 text-[14px] leading-[20px] text-black-900">
                                <p>{!! $condition['title'] !!}</p>
                                @if (isset($condition['list']))
                                    <ul class="flex flex-col gap-1">
                                        @foreach($condition['list'] as $item)
                                            <li>
                                                <div class="flex gap-[6px]">
                                                    <span class="relative translate-x-[-1px] w-[4px]">•</span>
                                                    <p>{!! $item !!}</p>
                                                </div>
                                            </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        <div class="js-reward-policy-rule flex flex-col gap-2 h-[380px] px-[7px]">
            @foreach ($rules as $rule)
                <div class="flex gap-1">
                    <img 
                        src="{{ Module::asset('rewardgoldenhour:images/list-item-image.avif') }}"
                        class="size-5" alt="rules-icon"
                    >
                    <p class="text-[14px] leading-[20px] text-black-900">{!! $rule !!}</p>
                </div>
            @endforeach
        </div>
    </div>
</section>
