@props(['rewardList', 'userTurnover'])
@php
    $list = $rewardList['reward_list'] ?? [];
    $total = $rewardList['reward_total'] ?? '0/0';
    $remain = $rewardList['rewardRemain'] ?? 0;
    $currentDate = now()->timezone('Asia/Bangkok')->format('d/m');
@endphp
<div class="flex flex-col items-center gap-3 xl:gap-4">
    <div class="px-4 pb-1 bg-event xl:px-7 xl:pb-[10px]">
        <p class="text-[16px] leading-[20px] text-neutral-960 font-utm capitalize xl:text-[32px] xl:leading-[38px]">nhận thưởng hằng ngày</p>
    </div>
    <section class="grid grid-cols-1 w-full xl:grid-cols-[578fr_642fr] xl:gap-5">
        <img 
            src="{{ Module::asset('rewardgoldenhour:images/macost.avif') }}" 
            alt="icon"
            class="hidden h-full aspect-[578/585] xl:block"
        >
        <div class="w-full pl-[6px] xl:pl-[7px]">
            <div class="relative flex flex-col justify-between w-full h-full rounded-t-[8px] rounded-b-[10px]">
                <div class="relative z-[1] pt-3 pb-[11px] pl-2 pr-[9px] xl:pt-4 xl:pb-[19px] xl:pl-[19px]">
                    <p class="text-[14px] leading-[18px] font-utm text-neutral xl:text-[22px] xl:leading-[26px]">DANH SÁCH TRÚNG THƯỞNG - {{ $currentDate }}</p>
                </div>
                <div class="relative z-[1] w-full h-[22px] pl-2 mb-3 xl:h-8 xl:pl-3 xl:mb-4">
                    <div class="relative z-[1] flex items-center gap-[6px] w-full h-full">
                        <p class="text-[12px] leading-[18px] font-medium text-neutral-1000 xl:text-[14px] xl:leading-[20px] xl:font-semibold">
                            Đã trao: 
                        </p>
                        <p class="text-[12px] leading-[18px] font-medium text-brand-bg-secondary-brand xl:text-[14px] xl:leading-[20px] xl:font-semibold">
                            {{ $total }} giải
                        </p>
                        <span class="w-1 h-1 rounded-full bg-neutral-1000 bg-opacity-50"></span>
                        <p class="text-[12px] leading-[18px] font-medium text-neutral-1000 xl:text-[14px] xl:leading-[20px] xl:font-semibold">
                            Còn lại: 
                        </p>
                        <p class="text-[12px] leading-[18px] font-medium text-brand-bg-secondary-brand xl:text-[14px] xl:leading-[20px] xl:font-semibold">
                            {{ $remain }} giải
                        </p>
                    </div>
                    <span class="absolute top-0 right-0 z-0 w-[calc(100%+6px)] h-full bg-event-gift-line xl:w-[calc(100%+7px)]"></span>
                    <span class="absolute bottom-0 left-0 w-0 h-0 border-t-[6px] border-l-[6px] border-l-transparent border-danger-1000 translate-x-[-100%] translate-y-[100%] xl:border-t-[7px] xl:border-l-[7px]"></span>
                    <picture>
                        <source 
                            srcset="{{ Module::asset('rewardgoldenhour:images/gift-icon-mb.avif') }}" 
                            type="image/avif"
                            media="(max-width: 1199px)"
                        >
                        <img 
                            src="{{ Module::asset('rewardgoldenhour:images/gift-icon-pc.avif') }}" 
                            alt="event"   
                            class="absolute bottom-0 right-0 w-[30.21%] max-w-[110px] aspect-[110/42] xl:right-3 xl:w-[186px] xl:max-w-max xl:aspect-[186/74]"
                        >
                    </picture>
                </div>
                <div class="relative z-[1] flex flex-col grow w-full pl-2 pr-[9px] pb-2 xl:pb-4 xl:px-3">
                    <div class="flex justify-between items-center w-full h-9 pr-[10px] bg-event-table-header xl:justify-start xl:h-11">
                        <div class="flex justify-start items-center w-[58px] h-full pl-[18px] mr-[29px] xl:mr-[119px]">
                            <p class="text-[14px] leading-[20px] text-neutral">STT</p>
                        </div>
                        <div class="flex justify-start items-center w-full max-w-[120px] h-full xl:mr-[116px]">
                            <p class="text-[14px] leading-[20px] text-neutral">Người chơi</p>
                        </div>
                        <div class="flex justify-start items-center w-full max-w-[130px] h-full">
                            <p class="text-[14px] leading-[20px] text-neutral">Nhận thưởng</p>
                        </div>
                    </div>
                    <div class="w-full h-[360px] bg-event-list overflow-auto no-scrollbar">
                        @if (count($list) > 0)
                            <div class="flex flex-col h-max">
                                @foreach($list as $key => $item)
                                    <div class="flex justify-between items-center w-full h-9 pr-[10px] border-b-[1px] border-warning-1000 xl:justify-start">
                                        <div class="relative flex justify-start items-start w-[58px] h-full mr-[29px] pt-1 pl-2 pr-[11px] xl:mr-[119px]">
                                            <img 
                                                src="{{ Module::asset('rewardgoldenhour:images/index.avif') }}"
                                                class="absolute top-0 left-0 z-0 w-full h-full"
                                            />
                                            <p class="reward-index relative z-[1] w-full text-center text-[20px] leading-[24px] font-utm">{{ $key + 1 }}</p>
                                        </div>
                                        <div class="flex justify-start items-center w-full max-w-[120px] h-full xl:mr-[116px]">
                                            <p class="text-[14px] leading-[20px] text-neutral-1000 italic">{{ $item -> username}}</p>
                                        </div>
                                        <div class="flex justify-start items-center gap-1 w-full max-w-[130px] h-full">
                                            <img src="{{ Module::asset('rewardgoldenhour:images/coin.avif') }}"  class="size-[14px]" alt="coin"/>
                                            <p class="text-[14px] leading-[20px] text-neutral-1000 italic">{{ $item -> amount_txt }} VND</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                         @else
                            <div class="flex flex-col justify-center items-center w-full h-full gap-4">
                                <img 
                                    src="{{ Module::asset('rewardgoldenhour:images/empty-icon.avif') }}" 
                                    alt="event"    
                                    class="size-[100px]"
                                />
                                <p class="text-[14px] leading-[20px] text-neutral-800">Chưa có người chơi trúng thưởng</p>
                            </div>
                         @endif
                    </div>
                </div>
                <div class="relative z-[1] flex justify-between items-center gap-[7px] h-[56px] pl-2 pr-[9px] bg-event-deposit-section rounded-b-[8px] xl:px-3">
                    <x-rewardgoldenhour::reward-list-addon :userTurnover="$userTurnover" />
                </div>

                <picture>
                    <source 
                        srcset="{{ Module::asset('rewardgoldenhour:images/event-gift-list-bg-mb.avif') }}" 
                        type="image/avif"
                        media="(max-width: 1199px)"
                    >
                    <img 
                        src="{{ Module::asset('rewardgoldenhour:images/event-gift-list-bg.avif') }}" 
                        alt="icon"
                        class="absolute top-0 left-0 z-0 w-full h-full rounded-t-[8px] rounded-b-[10px]"
                    >
                </picture>
            </div>
        </div>
    </section>
</div>
