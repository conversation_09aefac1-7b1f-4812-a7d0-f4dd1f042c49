
@php
    $startEventDay = env('START_REWARD_EVENT_DAY');
    $endEventDay = env('END_REWARD_EVENT_DAY');

    $validEvent = request()->get('validRewardEvent');
    $beforeEvent = request()->get('beforeRewardEvent');
@endphp

<div class="relative flex justify-center items-center w-full aspect-[390/300] xl:max-h-[490px] xl:aspect-[1920/490]">
    <img
        class="absolute top-0 left-0 z-0 w-full h-full hidden object-cover xl:block"
        src="{{ Module::asset('rewardgoldenhour:images/event-banner-bg.avif') }}"
    />
    <div class="container relative z-[1] hidden justify-between h-full xl:flex">
        <div class="flex items-center justify-center w-[42.2%] h-full pt-5">
            <div class="relative">
                @if ($beforeEvent || $validEvent) 
                    <div class="absolute bottom-full left-[5px] flex items-center w-[269px] h-[37px] pt-[5px] pl-[38px]">
                        <img src="{{ Module::asset('rewardgoldenhour:images/reward-day-bg-pc.avif') }}" class="absolute top-0 left-0 z-0 w-full h-full"/>
                        <p class="relative z-[1] text-[16px] leading-[16px] text-neutral-1000 font-semibold italic">{{ $startEventDay }} → {{ $endEventDay }}</p>
                    </div>
                @else
                    <img src="{{ Module::asset('rewardgoldenhour:images/reward-end-pc.avif') }}" class="absolute bottom-full left-[5px] w-[174px] h-7"/>
                @endif
                <img src="{{ Module::asset('rewardgoldenhour:images/reward-content-bg-pc.avif') }}" class="w-full aspect-[524/250]"/>
            </div>
        </div>
        <div class="relative translate-x-[19px] w-[53.87%] h-full">
            <img src="{{ Module::asset('rewardgoldenhour:images/reward-bg-pc.avif') }}" class="w-full h-full"/>
        </div>
    </div>
    <div class="relative flex justify-center w-full h-full pt-[11px] xl:hidden">
        <div class="flex flex-col items-start w-[78.46%]">
            <img src="{{ Module::asset('rewardgoldenhour:images/reward-content-bg-mb.avif') }}" class="relative z-[1] w-full aspect-[307/102]"/>
            @if ($beforeEvent || $validEvent) 
                <div class="relative z-[1] flex items-center w-[186px] h-[22px] pt-[4px] pl-[29px] mt-1 ml-[18px]">
                    <img src="{{ Module::asset('rewardgoldenhour:images/reward-day-bg-mb.avif') }}" class="absolute top-0 left-0 z-0 w-full h-full"/>
                    <p class="relative z-[1] text-[12px] leading-[12px] text-neutral-1000 font-semibold italic tracking-[-0.5px]">{{ $startEventDay }} → {{ $endEventDay }}</p>
                </div>
            @else
                <img src="{{ Module::asset('rewardgoldenhour:images/reward-end-mb.avif') }}" class="relative z-[1] w-[101px] h-4 mt-[7px] ml-[13px]"/>
            @endif
        </div>
   
        <img src="{{ Module::asset('rewardgoldenhour:images/reward-bg-mb.avif') }}" class="absolute top-0 left-0 z-0 w-full h-full"/>
    </div>
</div>