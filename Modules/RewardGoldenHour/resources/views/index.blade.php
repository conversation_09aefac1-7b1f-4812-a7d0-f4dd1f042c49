@pushOnce('css')
    <link rel="stylesheet" href="{{ Module::asset('rewardgoldenhour:css/app.css') }}">
@endPushOnce
<x-layout>
    <div class="flex flex-col gap-6 pb-[84px] bg-neutral-50 xl:gap-[60px] xl:pb-[80px]">
        <x-rewardgoldenhour::banner />
        <div class="container flex flex-col gap-6 xl:gap-[60px]">
            <x-rewardgoldenhour::info />
            <x-rewardgoldenhour::reward-list :rewardList="$rewardList" :userTurnover="$userTurnover" />
            <x-rewardgoldenhour::term-and-condition />
        </div>
    </div>
</x-layout>

<script src="{{ Module::asset('rewardgoldenhour:js/utils.js') }}"></script>
