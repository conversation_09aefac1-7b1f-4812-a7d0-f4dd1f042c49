<?php

namespace Modules\RewardGoldenHour\Services;

use App\Services\GatewayApi;
use App\Services\FakerApi;
use Symfony\Component\HttpFoundation\Response;
use App\Services\AccountService;
use Illuminate\Support\Facades\App;
use App\Enums\GatewayEndpoint;
use Exception;

class RewardGoldenHourService
{
    private GatewayApi $gatewayApi;
    private FakerApi $fakerApi;
    private AccountService $accountService;

    public function __construct(GatewayApi $gatewayApi, FakerApi $fakerApi, AccountService $accountService)
    {
        $this->gatewayApi = $gatewayApi;
        $this->fakerApi = $fakerApi;
        $this->accountService = $accountService;
    }

    public function getRewardList()
    {
        if (App::environment('local') && env('FAKER_BASE_URL')) {
            $response = $this->fakerApi->getPromotion(GatewayEndpoint::REWARD_EVENT_LIST->value);
        } else {
            $response = $this->gatewayApi->getPromotion(GatewayEndpoint::REWARD_EVENT_LIST->value);
        }

        if (isset($response->code) && $response->code === Response::HTTP_OK) {
            $rewardList = $response->data;
            $rewardTotal = $response->total_txt ?? '0/0';
            $rewardTotalArr = explode('/', $rewardTotal);
            $rewardRemain = (int)$rewardTotalArr[1] - ($response->total ?? 0);
            return [
                'reward_list' => $rewardList,
                'reward_total' => $rewardTotal,
                'rewardRemain' => $rewardRemain,
            ];
        }

        return [];
    }
    public function getUserTurnover()
    {
        try {
            $eventKey = config('rewardgoldenhour.key');

            if (App::environment('local') && env('FAKER_BASE_URL')) {
                $response = $this->fakerApi->get(GatewayEndpoint::ACCOUNT_INFO->value);
            } else {
                $response = $this->gatewayApi->get(GatewayEndpoint::ACCOUNT_INFO->value);
            }
            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                $userTurnover = $response->data;
                if (isset($userTurnover->rolling_info) && $userTurnover->rolling_info) {
                    $rollingInfo = array_filter($userTurnover->rolling_info, function ($item) use ($eventKey) {
                        return $item->event_id === $eventKey;
                    });
                    $rollingInfo = array_map(function ($item) use ($userTurnover) {
                        $item->turnover = $userTurnover->turnover;
                        $item->rolling_main = $userTurnover->rolling;
                        return $item;
                    }, $rollingInfo);
                    return $rollingInfo;
                }
                return null;
            }
        } catch (Exception $e) {
            return null;
        }
    }

}
