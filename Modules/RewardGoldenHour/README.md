# Săn <PERSON>o <PERSON> Xì

## 1. <PERSON><PERSON><PERSON> tra điều kiện nhận lì xì
**GET** `api-promotion/v1/lixi/newyear`

### Response:
```json
{
  "status": "OK",
  "code": 200,
  "data": {
    "check": false
  }
}
```
## 2. <PERSON><PERSON><PERSON><PERSON> lì xì
**POST** `api-promotion/v1/lixi/newyear`

### Error Response:
```json
{
  "code": 400,
  "status": "ERROR",
  "error": true,
  "amount": 0,
  "message": "Phần thưởng hôm nay đã hết"
}
```

### Success Response:
```json
{
    "code": 200,
    "status": "OK",
    "error": false,
    "amount": 50000,
    "amount_txt": "50,000"
}
```
## 2. <PERSON>h sách trúng thưởng
**GET** `api-promotion/v1/lixi/list`

### Response:
```json
{
    "code": 200,
    "status": "OK",
    "data": [
        {
            "username": "abcd****",
            "amount": 50000,
            "amount_txt": "50,000",
            "created_time": "2024-12-09T09: 19: 34.008Z"
        },
        {
            "username": "abcd****",
            "amount": 50000,
            "amount_txt": "50,000",
            "created_time": "2024-12-09T09: 19: 34.008Z"
        }
    ],
    "total": 2,
    "total_txt": "2/200",
}
```
## 2. Vòng cược
**GET** `api/v1/account/info`

Tiền cược theo event là rolling trong array. Số tiền đã nạp là amount trong array

### Response:
```json
{
  "code": 200,
  "status": "OK",
  "data": {
    "name": "Hoàn trả vô tận",
    "type": "COMMISSION",
    "deposit_amount": 0,
    "deposit_amount_txt": 0,
    "promotion_amount": 50000,
    "promotion_amount_txt": "50",
    "multiplier": 0,
    "rolling": 50000,
    "rolling_txt": "50",
    "package_id": 1,
    "turnover": 0,
    "turnover_txt": 0,
    "created_time": "2024-12-11T10:51:22.644Z",
    "description": "Hoàn trả vô tận",
    "rolling_time": "2024-12-11T10:51:22.645Z",
    "rolling_info": [
      {
        "amount": 50000,
        "amount_txt": "50,000",
        "date": "2024-12-11T10:51:22.645Z",
        "event_id": "li_xi_2025",
        "invoice_id": 2814,
        "rolling": 50000,
        "rolling_txt": "50,000"
      }
    ]
  }
}
```


















