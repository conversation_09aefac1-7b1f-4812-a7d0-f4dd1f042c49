<?php

$brandName = config('app.brand_name', 'Z27');
$startDay = env('START_REWARD_EVENT_DAY');
$endDay = env('END_REWARD_EVENT_DAY');
$startHour = env('START_REWARD_EVENT_TIME');
$endHour = env('END_REWARD_EVENT_TIME');

return [
    'key' => 'li_xi_2025',
    'name' => 'RewardGoldenHour',
    'startRewardEvent' =>  $startDay,
    'endRewardEvent' =>  $endDay,
    'info' => [
        [
            'icon' => 'time',
            'title' => 'Từ <span class="font-semibold text-secondary-800">' . $startHour . ' -> ' . $endHour .'</span><br>hằng ngày',
        ],
        [
            'icon' => 'money',
            'title' => 'Nạp từ <span class="font-semibold text-secondary-800">500 K</span><br> trở lên',
        ],
        [
            'icon' => 'gift',
            'title' => '<span class="font-semibold text-secondary-800">Chỉ 200 giải/ngày</span><br> nhận thưởng liền tay',
        ],
        [
            'icon' => 'product',
            'title' => 'Áp dụng <br><span class="font-semibold text-secondary-800">đa dạng games</span>',
        ],
    ],
    'rules' => [
        'Thời gian diễn ra chương trình: 00:00, ' . $startDay . ' → 23:59, ' . $endDay . '.',
        'Người dùng nạp tiền từ <span class="font-semibold">' . $startHour . ' đến ' . $endHour . '</span> hàng ngày với số tiền từ <span class="font-semibold">500 K</span> trở lên sẽ có cơ hội nhận được hộp quà với phần thưởng <span class="font-semibold">50,000 VND</span>.',
        'Sau khi nạp tiền thành công, hộp quà sẽ xuất hiện dưới dạng pop-up hoặc người dùng có thể truy cập vào trang sự kiện để nhận thưởng.',
        'Phần thưởng sẽ được trao cho <span class="font-semibold">200 người dùng đầu tiên</span> thoả mãn điều kiện nạp tiền và nhấn vào hộp quà.',
        '<span class="font-semibold">Mỗi tài khoản chỉ được nhận một hộp quà mỗi ngày.</span>',
        'Người dùng phải đạt doanh thu cược hợp lệ <span class="font-semibold">50 K</span> trở lên sau khi nhận hộp quà để rút tiền.'
    ],
    'conditions' => [
        [
            'title' => 'Doanh thu cược hợp lệ được áp dụng với tất cả các sản phẩm trên ' . $brandName . '.'
        ],
        [
            'title' => 'Không áp dụng cho hình thức nạp P2P.'
        ],
        [
            'title' => 'Không áp dụng chung với gói khuyến mãi thưởng nạp lần đầu.'
        ],
        [
            'title' => 'Khi tham gia chương trình này, Quý Khách không thể chơi các thể loại game:',
            'list' => [
                'Game Bài.',
                'Table Games.',
                'Sòng Bài Trực Tuyến (Live Casino).',
                'Tất cả game trên menu Mini Game (Tài Xỉu, Roulette MD5, Xóc Đĩa MD5, Mr.Bean Go Home MD5, Tom & Jerry MD5, Vé Số Cào, Mini Poker, Trên Dưới, Super Wheel…).',
                "Game của các nhà cung cấp: Pragmatic Play, TomHorn, QTech, PGSoft, Microgaming, Askmebet, Jili, Play'n'Go, Evoplay, Fachai, CQ9, Onegame, Blueprint Gaming, QuickSpin, Relax Gaming, ThunderKick, DragonSoft, Yggdrasil, Spribe, Red Tiger, NetEnt và Habanero.",
                'Một số game của TechPlay bao gồm: Đua Ngựa Máy Xèng, Đua Ngựa 3D, Keno Lộc Phát.',
                'Một số game nổ hũ của TechPlay bao gồm: DragonBall, Minion, Tron Legacy, Huyền Thoại Muay Thái, Giáng Sinh Rinh Quà.',
            ]
        ],
        [
            'title' => $brandName . ' có quyền chấm dứt, tạm dừng hoặc hủy bỏ chương trình khuyến mãi này, khấu trừ bất kỳ khoản tiền thưởng nào đã được ghi có, thu hồi các khoản thắng cược có liên quan do vi phạm các điều khoản và điều kiện của chương trình khuyến mãi, hoặc do hành vi cá cược cố ý gian lận và không phù hợp.'
        ],
        [
            'title' => 'Các vé cược cân kèo, có kết quả hoà, bị huỷ cược và có tỷ lệ cược dưới 1.5 (DEC), -200 (US), -2 (INDO), 0.5 (MY), 0.5 (HK) sẽ không được tính vào số vé hợp lệ.'
        ],
        [
            'title' => 'Điều khoản và Điều kiện chung của ' . $brandName . ' vẫn được áp dụng cho chương trình này.'
        ],
    ],
    'event_status' => [
        'not_started' => 'not_started',
        'ended' => 'ended',
        'valid' => 'valid',
        'claimed' => 'claimed',
    ],
    'promotion_item' => [
        'id' => 'reward_golden_hour',
        'title' => $brandName . ' - Săn Thưởng Giờ Vàng',
        'imgUrl' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour.webp'),
        'imgUrlMb' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour-mb.webp'),
        'actionUrl' => '/san-thuong-gio-vang',
    ],
    'hero_banner' => [
        'id' => 'reward_golden_hour',
        'title' => 'Săn Thưởng Giờ Vàng',
        'imgSrc' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour.avif'),
        'imgSrcMobile' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour-mb.avif'),
        'url' => '/san-thuong-gio-vang',
        'order' => 1,
        'type' => 'component',
    ],
];

