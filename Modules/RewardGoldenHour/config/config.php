<?php

$brandName = config('app.brand_name', 'Z27');
$startDay = env('START_REWARD_EVENT_DAY');
$endDay = env('END_REWARD_EVENT_DAY');
$startHour = env('START_REWARD_EVENT_TIME');
$endHour = env('END_REWARD_EVENT_TIME');

return [
    'key' => 'li_xi_2025',
    'name' => 'RewardGoldenHour',
    'startRewardEvent' =>  $startDay,
    'endRewardEvent' =>  $endDay,
    'info' => [
        [
            'icon' => 'time',
            'title' => __('config.tu_span_classfontsemibold_textsecondary800') . $startHour . ' -> ' . $endHour .'</span><br>hằng ngày',
        ],
        [
            'icon' => 'money',
            'title' => 'Nạp từ <span class="font-semibold text-secondary-800">500 K</span><br> trở lên',
        ],
        [
            'icon' => 'gift',
            'title' => '<span class="font-semibold text-secondary-800">Chỉ 200 giải/ngày</span><br> nhận thưởng liền tay',
        ],
        [
            'icon' => 'product',
            'title' => 'Áp dụng <br><span class="font-semibold text-secondary-800">đa dạng games</span>',
        ],
    ],
    'rules' => [
        'Thời gian diễn ra chương trình: 00:00, ' . $startDay . ' → 23:59, ' . $endDay . '.',
        __('config.nguoi_dung_nap_tien_tu_span_classfontsemibold') . $startHour . ' đến ' . $endHour . '</span> hàng ngày với số tiền từ <span class="font-semibold">500 K</span> trở lên sẽ có cơ hội nhận được hộp quà với phần thưởng <span class="font-semibold">50,000 VND</span>.',
        __('config.sau_khi_nap_tien_thanh_cong_hop_qua_se_xuat_hien_d'),
        'Phần thưởng sẽ được trao cho <span class="font-semibold">200 người dùng đầu tiên</span> thoả mãn điều kiện nạp tiền và nhấn vào hộp quà.',
        '<span class="font-semibold">Mỗi tài khoản chỉ được nhận một hộp quà mỗi ngày.</span>',
        'Người dùng phải đạt doanh thu cược hợp lệ <span class="font-semibold">50 K</span> trở lên sau khi nhận hộp quà để rút tiền.'
    ],
    'conditions' => [
        [
            'title' => 'Doanh thu cược hợp lệ được áp dụng với tất cả các sản phẩm trên ' . $brandName . '.'
        ],
        [
            'title' => __('config.khong_ap_dung_cho_hinh_thuc_nap_p2p')
        ],
        [
            'title' => __('config.khong_ap_dung_chung_voi_goi_khuyen_mai_thuong_nap_')
        ],
        [
            'title' => __('config.khi_tham_gia_chuong_trinh_nay_quy_khach_khong_the__1'),
            'list' => [
                __('config.game_bai_4'),
                'Table Games.',
                __('config.song_bai_truc_tuyen_live_casino_1'),
                __('config.tat_ca_game_tren_menu_mini_game_tai_xiu_roulette_m_1'),
                __('config.game_cua_cac_nha_cung_cap_pragmatic_play_tomhorn_q_1'),
                __('config.mot_so_game_cua_techplay_bao_gom_Dua_ngua_may_xeng_1'),
                __('config.mot_so_game_no_hu_cua_techplay_bao_gom_dragonball__1'),
            ]
        ],
        [
            'title' => $brandName . ' có quyền chấm dứt, tạm dừng hoặc hủy bỏ chương trình khuyến mãi này, khấu trừ bất kỳ khoản tiền thưởng nào đã được ghi có, thu hồi các khoản thắng cược có liên quan do vi phạm các điều khoản và điều kiện của chương trình khuyến mãi, hoặc do hành vi cá cược cố ý gian lận và không phù hợp.'
        ],
        [
            'title' => __('config.cac_ve_cuoc_can_keo_co_ket_qua_hoa_bi_huy_cuoc_va_')
        ],
        [
            'title' => 'Điều khoản và Điều kiện chung của ' . $brandName . ' vẫn được áp dụng cho chương trình này.'
        ],
    ],
    'event_status' => [
        'not_started' => 'not_started',
        'ended' => 'ended',
        'valid' => 'valid',
        'claimed' => 'claimed',
    ],
    'promotion_item' => [
        'id' => 'reward_golden_hour',
        'title' => $brandName . ' - Săn Thưởng Giờ Vàng',
        'imgUrl' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour.webp'),
        'imgUrlMb' => Module::asset('rewardgoldenhour:images/promotion-reward-golden-hour-mb.webp'),
        'actionUrl' => '/san-thuong-gio-vang',
    ],
    'hero_banner' => [
        'id' => 'reward_golden_hour',
        'title' => __('config.san_thuong_gio_vang_1'),
        'imgSrc' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour.avif'),
        'imgSrcMobile' => Module::asset('rewardgoldenhour:images/banner-reward-golden-hour-mb.avif'),
        'url' => '/san-thuong-gio-vang',
        'order' => 1,
        'type' => 'component',
    ],
];

