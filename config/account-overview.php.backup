<?php

use App\Enums\UrlPathEnum;
use App\Enums\GatewayEndpoint;

return [
    'game_swiper_config' => [
        'slidesPerView' => 1.2411,
        'slidesPerGroup' => 1,
        'spaceBetween' => 4,
        'pagination' => [
            'el' => '.overview-games-swiper-pagination',
            'clickable' => true,
            'type' => 'bullets',
        ],
        'autoplay' => [
            'delay' => 2000,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
        'breakpoints' => [
            '1024' => [
                'slidesPerView' => 1.216,
                'spaceBetween' => 4,
            ],
        ],
    ],
    'category_swiper_config' => [
        'slidesPerView' => 2.78,
        'slidesPerGroup' => 1,
        'spaceBetween' => 12,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
        'autoplay' => [
            'delay' => 2500,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
    ],
    'category_mb_swiper_config' => [
        'slidesPerView' => 3.272,
        'slidesPerGroup' => 1,
        'spaceBetween' => 8,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
        'autoplay' => [
            'delay' => 2500,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
    ],
    'category_list' => [
        [
            'name' => 'K-sports',
            'image' => 'k-sports',
            'link' => UrlPathEnum::K_SPORTS,
            'isSport' => true,
            'url' => UrlPathEnum::K_SPORTS->value,
            'apiUrl' => GatewayEndpoint::K_SPORTS->value,
        ],
        [
            'name' => 'Game bài',
            'image' => 'game-bai',
            'link' => UrlPathEnum::GAME_CARD,
        ],
        [
            'name' => 'Sòng bài',
            'image' => 'song-bai',
            'link' => UrlPathEnum::CASINO,
        ],
        [
            'name' => 'Lô đề',
            'image' => 'lo-de',
            'link' => UrlPathEnum::GAME_OTHER,
            'mb-link' => UrlPathEnum::LODE,
        ],
        [
            'name' => 'Quay số',
            'image' => 'quay-so',
            'link' => UrlPathEnum::LOTTERY,
            'mb-link' => UrlPathEnum::QUAYSO,
        ],
        [
            'name' => 'Bắn cá',
            'image' => 'ban-ca',
            'link' => UrlPathEnum::FISHING,
        ],
        [
            'name' => 'IM-sports',
            'image' => 'im-sports',
            'link' =>  UrlPathEnum::IM_NORMAL_SPORTS,
            'isSport' => true,
            'url' => UrlPathEnum::IM_NORMAL_SPORTS->value,
            'apiUrl' => GatewayEndpoint::IM_NORMAL_SPORTS->value,
        ],
        [
            'name' => 'Đá gà',
            'image' => 'da-ga',
            'link' => UrlPathEnum::GAME_OTHER,
            'mb-link' => UrlPathEnum::FIGHTCOCK,
        ],
        [
            'name' => 'Slots',
            'image' => 'slots',
            'link' => UrlPathEnum::SLOTS,
        ],
        [
            'name' => 'Nổ hũ',
            'image' => 'no-hu',
            'link' => UrlPathEnum::NOHU,
        ],
    ],
    'history' => [
        'typeTransaction' => [
            'WITHDRAW' => 'Rút tiền',
            'DEPOSIT' => 'Nạp tiền',
        ],
        'methodTransaction' => [
            'bank_account' => 'Chuyển khoản',
            'nicepay' => 'nicepay',
            'phone_card' => 'Thẻ cào',
            'daily_cashback_slot' => 'Hoàn trả Slots',
            'cancel_promotion' => 'Cancel Promotion',
            'crypto' => 'Crypto',
        ],
        'statusTransaction' => [
            'CANCEL' => 'Thất bại',
            'DRAFT' => 'Đang xử lý',
            'FINISHED' => 'Hoàn thành',
            'PENDING' => 'Đang xử lý',
            'WAITING' => 'Đang xử lý',
            'PROCESSING' => 'Đang xử lý',
            'APPROVED' => 'Đang xử lý',
            'PHONE_CARD_PROCESSING' => 'Đang xử lý',
            'PHONE_CARD_PENDING' => 'Đang xử lý',
            'PHONE_CARD_FINISHED' => 'Hoàn thành',
            'PHONE_CARD_CANCEL' => 'Thất bại',
            'PHONE_CARD_DRAFT' => 'Đang xử lý',
        ],
    ],
];
