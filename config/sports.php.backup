<?php

use App\Enums\GatewayEndpoint;
use App\Enums\UrlPathEnum;

return [
    'listSports'  => [
        [
            'key' => 'k',
            'name' => 'K - Sports',
            'img' => '/asset/images/sports/k-sport.avif',
            'imghover' => '/asset/images/sports/k-sport-hover.avif',
            'text' => '/asset/icons/sports/k-sport-text.svg',
            'texthover' => '/asset/icons/sports/k-sport-text-hover.svg',
            'imgmb' => '/asset/images/sports/k-sport-mb.avif',
            'url' => UrlPathEnum::K_SPORTS->value,
            'apiUrl' => GatewayEndpoint::K_SPORTS->value,
            'isLogin' => false,
            'description' => 'Thể thao châu Á',
        ],
        [
            'key' => 'saba',
            'name' => 'Saba - Sports',
            'img' => '/asset/images/sports/saba-sport.avif',
            'imghover' => '/asset/images/sports/saba-sport-hover.avif',
            'text' => '/asset/icons/sports/saba-sport-text.svg',
            'texthover' => '/asset/icons/sports/saba-sport-text-hover.svg',
            'imgmb' => '/asset/images/sports/saba-sport-mb.avif',
            'url' => UrlPathEnum::SABA_SPORTS->value,
            'apiUrl' => GatewayEndpoint::SABA_SPORTS->value,
            'isLogin' => false,
            'description' => 'Thể thao châu Âu',
        ],
        [
            'key' => 'bti',
            'name' => 'BTI - Sports',
            'img' => '/asset/images/sports/bti-sport.avif',
            'imghover' => '/asset/images/sports/bti-sport-hover.avif',
            'text' => '/asset/icons/sports/bti-sport-text.svg',
            'texthover' => '/asset/icons/sports/bti-sport-text-hover.svg',
            'imgmb' => '/asset/images/sports/bti-sport-mb.avif',
            'url' => UrlPathEnum::BTI_SPORTS->value,
            'apiUrl' => GatewayEndpoint::BTI_SPORTS->value,
            'isLogin' => false,
            'description' => 'Thể thao Latinh',
        ],
        [
            'key' => 'im',
            'name' => 'IM - Sports',
            'img' => '/asset/images/sports/im-sport.avif',
            'imghover' => '/asset/images/sports/im-sport-hover.avif',
            'text' => '/asset/icons/sports/im-sport-text.svg',
            'texthover' => '/asset/icons/sports/im-sport-text-hover.svg',
            'imgmb' => '/asset/images/sports/im-sport-mb.avif',
            'url' => UrlPathEnum::IM_NORMAL_SPORTS->value,
            'apiUrl' => GatewayEndpoint::IM_NORMAL_SPORTS->value,
            'isLogin' => false,
            'description' => 'Thể thao Ảo',
            'loginRequired' => true,
        ],
    ],
    'vitualSports'  => [
        [
            'key' => 'im',
            'name' => 'IM - Sports',
            'img' => '/asset/images/sports/im-sport-virtual.avif',
            'text' => '/asset/icons/sports/im-sport-virtual-text.svg',
            'imgmb' => '/asset/images/sports/im-sport-virtual-mb.avif',
            'imghover' => '/asset/images/sports/im-sport-virtual-hover.avif',
            'url' => UrlPathEnum::IM_SPORTS->value,
            'apiUrl' => GatewayEndpoint::IM_SPORTS->value,
            'loginRequired' => true,
            'description' => 'Tỉ lệ cược hấp dẫn',
        ],
        [
            'key' => 'k',
            'name' => 'K - Sports',
            'img' => '/asset/images/sports/k-sport-vitual.avif',
            'text' => '/asset/icons/sports/k-sport-vitual-text.svg',
            'imgmb' => '/asset/images/sports/k-sport-vitual-mb.avif',
            'imghover' => '/asset/images/sports/k-sport-virtual-hover.avif',
            'url' => UrlPathEnum::K_SPORTS_VITUAL->value,
            'apiUrl' => GatewayEndpoint::K_SPORTS_VITUAL->value,
            'loginRequired' => true,
            'description' => 'Đa dạng thể loại',
        ],
        [
            'key' => 'saba',
            'name' => 'Saba - Sports',
            'img' => '/asset/images/sports/saba-sport-vitual.avif',
            'text' => '/asset/icons/sports/saba-sport-vitual-text.svg',
            'imgmb' => '/asset/images/sports/saba-sport-vitual-mb.avif',
            'imghover' => '/asset/images/sports/saba-sport-virtual-hover.avif',
            'url' => UrlPathEnum::SABA_SPORTS_VITUAL->value,
            'apiUrl' => GatewayEndpoint::SABA_SPORTS_VITUAL->value,
            'loginRequired' => true,
            'description' => 'Mô phỏng nhiều thể loại',
        ],
        [
            'key' => 'pp',
            'name' => 'PP - Sports',
            'img' => '/asset/images/sports/pp-sport.avif',
            'text' => '/asset/icons/sports/pp-sport-text.svg',
            'imgmb' => '/asset/images/sports/pp-sport-mb.avif',
            'imghover' => '/asset/images/sports/pp-sport-virtual-hover.avif',
            'url' => UrlPathEnum::PP_SPORTS->value,
            'apiUrl' => GatewayEndpoint::PP_SPORTS->value,
            'loginRequired' => true,
            'description' => 'Trải nghiệm công nghệ mới',
        ],
    ],
    'hotMatchSwiper' => [
        'slidesPerView' => 1.03,
        'spaceBetween' => 8,
        'autoplay' => [
            'delay' => 3000,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => false,
        'breakpoints' => [
            '1200' => [
                'slidesPerView' => 4,
                'spaceBetween' => 12,
            ],
            '768' => [
                'slidesPerView' => 2,
                'spaceBetween' => 8,
            ],
        ],
        'navigation' => [
            'prevEl' => '.sports-prev',
            'nextEl' => '.sports-next',
        ],
    ],
];
