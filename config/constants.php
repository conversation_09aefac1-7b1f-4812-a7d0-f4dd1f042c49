<?php

return [
    'gameFilter' => [
        'sortOptions' => [
            [
                'id' => 'all',
                'key' => 'all',
                'value' => 'all',
                'i18_key' => 'filters.sort.all',
                'label' => 'All',
                'name' => 'All',
                'icon' => 'all-board',
            ],
            [
                'id' => 'hot',
                'key' => 'hot',
                'value' => 'hot',
                'i18_key' => 'filters.sort.hot',
                'label' => 'Game hot',
                'name' => 'Game hot',
                'icon' => 'effect-fire',
            ],
            [
                'id' => 'new',
                'key' => 'new',
                'value' => 'new',
                'i18_key' => 'filters.sort.new',
                'label' => 'Game mới',
                'name' => 'Game mới',
                'icon' => 'news',
            ],
            [
                'id' => 'recent',
                'key' => 'recent',
                'value' => 'recent',
                'i18_key' => 'filters.sort.recent',
                'label' => 'Vừa chơi',
                'name' => 'Vừa chơi',
                'icon' => 'recent',
                'checkAuth' => true,
            ],
        ],
        'typeOptions' => [
            [
                'id' => 'slot',
                'key' => 'slot',
                'value' => 'slot',
                'i18_key' => 'filters.type.slot',
                'name' => 'Slots',
                'link' => '/games/quay-slots',
            ],
            [
                'id' => 'game_cards',
                'key' => 'game_cards',
                'value' => 'game_cards',
                'i18_key' => 'filters.type.game_cards',
                'name' => 'Game Cards',
                'link' => '/games/game-bai',
            ],
            [
                'id' => 'lode',
                'key' => 'lode',
                'value' => 'lode',
                'i18_key' => 'filters.type.lode',
                'name' => 'Lode',
                'link' => '/games/lode',
            ],
            [
                'id' => 'fishing',
                'key' => 'fishing',
                'value' => 'fishing',
                'i18_key' => 'filters.type.fishing',
                'name' => 'Fishing',
                'link' => '/games/ban-ca',
            ],
            [
                'id' => 'tables',
                'key' => 'tables',
                'value' => 'tables',
                'i18_key' => 'filters.type.tables',
                'name' => 'Tables',
                'link' => '/games/table-games',
            ],
            [
                'id' => 'instant',
                'key' => 'instant',
                'value' => 'instant',
                'i18_key' => 'filters.type.instant',
                'name' => 'Instant',
                'link' => '/games/game-nhanh',
            ],
        ],
        'providerOptions' => [],
        'page' => 1,
        'limit' => 20,
        'keyword' => [],
    ],
    'casinoFilter' => [
        'sortOptions' => [
            [
                'id' => 'all',
                'key' => 'all',
                'value' => 'all',
                'i18_key' => 'filters.sort.all',
                'name' => 'All',
                'label' => 'All',
                'icon' => 'all-board',
                'active' => true,
            ],
            [
                'id' => 'hot',
                'key' => 'hot',
                'value' => 'hot',
                'i18_key' => 'filters.sort.hot',
                'name' => 'Game hot',
                'label' => 'Game hot',
                'icon' => 'effect-fire',
            ],
            [
                'id' => 'new',
                'key' => 'new',
                'value' => 'new',
                'i18_key' => 'filters.sort.new',
                'name' => 'Game mới',
                'label' => 'Game mới',
                'icon' => 'news',
            ],
            [
                'id' => 'recent',
                'key' => 'recent',
                'value' => 'recent',
                'i18_key' => 'filters.sort.recent',
                'name' => 'Vừa chơi',
                'label' => 'Vừa chơi',
                'icon' => 'recent',
                'checkAuth' => true,
            ],
        ],
        'typeOptions' => [
            [
                'id' => 'baccarat',
                'key' => 'baccarat',
                'value' => 'baccarat',
                'i18_key' => 'filters.type.baccarat',
                'name' => 'Baccarat',
            ],
            [
                'id' => 'sicbo',
                'key' => 'sicbo',
                'value' => 'sicbo',
                'i18_key' => 'filters.type.sicbo',
                'name' => 'Sicbo',
            ],
            [
                'id' => 'blackjack',
                'key' => 'blackjack',
                'value' => 'blackjack',
                'i18_key' => 'filters.type.blackjack',
                'name' => 'Blackjack',
            ],
            [
                'id' => 'roulette',
                'key' => 'roulette',
                'value' => 'roulette',
                'i18_key' => 'filters.type.roulette',
                'name' => 'Roulette',
            ],
            [
                'id' => 'poker',
                'key' => 'poker',
                'value' => 'poker',
                'i18_key' => 'filters.type.poker',
                'name' => 'poker',
            ],
            [
                'id' => 'dragontiger',
                'key' => 'dragontiger',
                'value' => 'dragontiger',
                'i18_key' => 'filters.type.dragontiger',
                'name' => 'Dragon Tiger',
            ],
            [
                'id' => 'other',
                'key' => 'other',
                'value' => 'other',
                'i18_key' => 'filters.type.other',
                'name' => 'Other Games',
            ],

        ],
        'providerOptions' => [],
        'page' => 1,
        'limit' => 20,
        'keyword' => [],
    ],
    'homepage' => [
        // Option 1 : Change image when switching language. The image will be displayed by suffix. Example: '_en' or '_th' --
        // Option 2: Change the content by language
        'highlightsOption' => 1,
        'highlights' => [
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'lobby',
                'type' => '.avif',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'casino',
                'type' => '.avif',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'games',
                'type' => '.avif',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'lobby',
                'type' => '.avif',
            ],
        ],
        'highlights2' => [
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'lobby',
                'type' => '.avif',
                'i18_key' => 'common.casino',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'casino',
                'type' => '.avif',
                'i18_key' => 'common.casino',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'games',
                'type' => '.avif',
                'i18_key' => 'common.casino',
            ],
            [
                'imageDir' => 'asset/images/home/<USER>',
                'href' => 'lobby',
                'type' => '.avif',
                'i18_key' => 'common.casino',
            ],
        ],
    ],
    'accountSection' => [
        'navItemsDesktop' => [
            [
                'id' => 'deposit',
                'label' => 'Deposit',
                'href' => 'account/deposit',
            ],
            [
                'id' => 'withdraw',
                'label' => 'Withdraw',
                'href' => 'account/withdraw',
            ],
            [
                'id' => 'transactionHistory',
                'label' => 'Transaction History',
                'href' => 'account/transaction-history',
            ],
            [
                'id' => 'betHistory',
                'label' => 'Bet History',
                'href' => 'account/bet-history',
            ],
            [
                'id' => 'bankAccount',
                'label' => 'Bank Account',
                'href' => 'account/bank-account',
            ],
            [
                'id' => 'accountInformation',
                'label' => 'Account Information',
                'href' => 'account/profile',
            ],
            [
                'id' => 'password',
                'label' => 'Password',
                'href' => 'account/password',
            ],
            [
                'id' => 'promotion',
                'label' => 'Promotion',
                'href' => 'account/promotion',
            ],
            [
                'id' => 'referral',
                'label' => 'Referral',
                'href' => 'account/referral',
            ],
        ],
        'navItemsMobile' => [
            [
                'id' => 'promotions',
                'label' => 'Promotions',
                'items' => [
                    [
                        'id' => 'promotion',
                        'label' => 'Promotion',
                        'href' => 'account/promotion',
                        'icon' => 'vendor/accounts/images/account/promotion.svg',
                    ],
                    [
                        'id' => 'referral',
                        'label' => 'Referral',
                        'href' => 'account/referral',
                        'icon' => 'vendor/accounts/images/account/referral.svg',
                    ],
                ],
            ],
            [
                'id' => 'profile',
                'label' => 'Profile',
                'items' => [
                    [
                        'id' => 'bankAccount',
                        'label' => 'Bank Account',
                        'href' => 'account/bank-account',
                        'icon' => 'vendor/accounts/images/account/bank-account.svg',
                    ],
                    [
                        'id' => 'password',
                        'label' => 'Change Password',
                        'href' => 'account/password',
                        'icon' => 'vendor/accounts/images/account/change-password.svg',
                    ],
                    [
                        'id' => 'accountInformation',
                        'label' => 'Account Information',
                        'href' => 'account',
                        'icon' => 'vendor/accounts/images/account/information.svg',
                    ],
                ],
            ],
            [
                'id' => 'history',
                'label' => 'History',
                'items' => [
                    [
                        'id' => 'betHistory',
                        'label' => 'Bet History',
                        'href' => 'account/bet-history',
                        'icon' => 'vendor/accounts/images/account/bet-history.svg',
                    ],
                    [
                        'id' => 'transactionHistory',
                        'label' => 'Transaction History',
                        'href' => 'account/transaction-history',
                        'icon' => 'vendor/accounts/images/account/transaction-history.svg',
                    ],
                ],
            ],
            [
                'id' => 'others',
                'label' => 'Others',
                'items' => [
                    [
                        'id' => 'language',
                        'label' => 'Language',
                        'href' => '',
                        'icon' => 'vendor/accounts/images/account/language.svg',
                    ],
                ],
            ],
            [
                'id' => 'logoutItem',
                'label' => '',
                'items' => [
                    [
                        'id' => 'logout',
                        'label' => 'Log out',
                        'href' => '',
                        'icon' => 'vendor/accounts/images/account/logout.svg',
                    ],
                ],
            ],
        ],
        'betHistory' => [
            'filters' => [
                [
                    'id' => 'all',
                    'label' => 'All',
                    'value' => 'all',
                ],
                [
                    'id' => 'win',
                    'label' => 'Win',
                    'value' => 'win',
                ],
                [
                    'id' => 'lose',
                    'label' => 'Lose',
                    'value' => 'lose',
                ],
            ],
            'tableItems' => [
                [
                    'id' => 'idTime',
                    'label' => 'ID/Time',
                ],
                [
                    'id' => 'game',
                    'label' => 'Game',
                ],
                [
                    'id' => 'amount',
                    'label' => 'Amount',
                ],
                [
                    'id' => 'winLoss',
                    'label' => 'Win-Loss',
                ],
                [
                    'id' => 'turnover',
                    'label' => 'Turnover',
                ],
                [
                    'id' => 'reward',
                    'label' => 'Reward',
                ],
                [
                    'id' => 'status',
                    'label' => 'Status',
                ],
            ],
            'mockupData' => [
                [
                    'id' => '#123',
                    'time' => '15/11/2024',
                    'game' => 'LuckyLuke:Lucky Luke',
                    'amount' => '1.5 THB',
                    'winLoss' => '7 THB',
                    'turnOver' => '1.5 THB',
                    'reward' => '0.02 THB',
                    'status' => 'win',
                ],
                [
                    'id' => '#234',
                    'time' => '15/11/2024',
                    'game' => 'LuckyLuke:Lucky Luke',
                    'amount' => '1.5 THB',
                    'winLoss' => '6 THB',
                    'turnOver' => '1.5 THB',
                    'reward' => '0.02 THB',
                    'status' => 'win',
                ],
                [
                    'id' => '#345',
                    'time' => '15/11/2024',
                    'game' => 'LuckyLuke:Lucky Luke',
                    'amount' => '1.5 THB',
                    'winLoss' => '-1.5 THB',
                    'turnOver' => '1.5 THB',
                    'reward' => '0.02 THB',
                    'status' => 'lose',
                ],
            ],
        ],
        'transactionHistory' => [
            'filters' => [
                [
                    'id' => 'all',
                    'label' => 'All',
                    'value' => 'all',
                ],
                [
                    'id' => 'success',
                    'label' => 'Successful',
                    'value' => 'success',
                ],
                [
                    'id' => 'pending',
                    'label' => 'Pending',
                    'value' => 'pending',
                ],
                [
                    'id' => 'cancelled',
                    'label' => 'Cancel',
                    'value' => 'cancelled',
                ],
            ],
            'tableItems' => [
                [
                    'id' => 'idTime',
                    'label' => 'ID/Time',
                ],
                [
                    'id' => 'type',
                    'label' => 'Type',
                ],
                [
                    'id' => 'amount',
                    'label' => 'Amount',
                ],
                [
                    'id' => 'method',
                    'label' => 'Method',
                ],
                [
                    'id' => 'status',
                    'label' => 'Status',
                ],
                [
                    'id' => 'actions',
                    'label' => 'Actions',
                ],
            ],
            'mockupData' => [
                [
                    'id' => '#689182',
                    'time' => '16/11/2024 - 00:30',
                    'type' => 'Promotion',
                    'amount' => '1.5 THB',
                    'method' => '7 THB',
                    'status' => 'success',
                    'actions' => '',
                ],
                [
                    'id' => '#689183',
                    'time' => '16/11/2024 - 00:30',
                    'type' => 'Deposit',
                    'amount' => '500 THB',
                    'method' => 'thpay',
                    'status' => 'cancelled',
                    'actions' => '',
                ],
                [
                    'id' => '#689184',
                    'time' => '16/11/2024 - 00:30',
                    'type' => 'Deposit',
                    'amount' => '200 THB',
                    'method' => 'ibanking',
                    'status' => 'cancelled',
                    'actions' => '',
                ],
            ],
        ],
    ],
    'locales' => [
        'en' => [
            'id' => 'en',
            'image' => 'asset/images/en.avif',
            'label' => 'EN',
        ],
        'th' => [
            'id' => 'th',
            'image' => 'asset/images/th.avif',
            'label' => 'TH',
        ],
    ],
    'withdraw' => [
        'banking' => [
            'banks' => [
                [
                    'name' => 'Aeepay',
                    'key' => 'aeepay',
                    'is_maintain' => '0',
                    'defined_amount' => [
                        'deposit' => [
                            'min' => 100,
                            'max' => 200000,
                            'predefined_amounts' => [
                                100,
                                250,
                                500,
                                1000,
                                2000,
                                10000,
                                20000,
                                50000,
                            ],
                        ],
                        'withdraw' => [
                            'min' => 250,
                            'max' => 300000,
                            'predefined_amounts' => [
                                250,
                                3000,
                                5000,
                                10000,
                                50000,
                                100000,
                                200000,
                                300000,
                            ],
                        ],
                    ],
                    'bank_list' => [
                        ['code' => 'THB_UOB', 'name' => 'United Overseas Bank (Thai) Public Company Limited'],
                        ['code' => 'THB_HSBC', 'name' => 'The Hongkong and Shanghai Banking Corporation'],
                        ['code' => 'THB_SMBC', 'name' => 'Sumitomo Mitsui Banking Corporation'],
                        ['code' => 'THB_ICBC', 'name' => 'INDUSTRIAL AND COMMERCIAL BANK OF CHINA'],
                        ['code' => 'THB_TMB', 'name' => 'TMB Bank Public Company Limited'],
                        ['code' => 'THB_CIMB', 'name' => 'Commerce International Merchant Bank'],
                        ['code' => 'THB_LHBANK', 'name' => 'Land and Houses Bank'],
                        ['code' => 'THB_GSB', 'name' => 'Government Saving Bank'],
                        ['code' => 'THB_TCRB', 'name' => 'Thai Credit Retail Bank'],
                        ['code' => 'THB_KBANK', 'name' => 'Kasikorn Bank'],
                        ['code' => 'THB_SCBT', 'name' => 'Standard chartered Bank'],
                        ['code' => 'THB_TSCO', 'name' => 'TISCO Bank Public Company Limited'],
                        ['code' => 'THB_KK', 'name' => 'Kiatnakin Bank'],
                        ['code' => 'THB_TBANK', 'name' => 'Thanachartbank'],
                        ['code' => 'THB_DB', 'name' => 'Deutsche Bank AG'],
                        ['code' => 'THB_GHB', 'name' => 'Government Housing Bank'],
                        ['code' => 'THB_TTB', 'name' => 'TMBThanachat Bank'],
                        ['code' => 'THB_MHCB', 'name' => 'Mizuho Corporate Bank'],
                        ['code' => 'THB_BBL', 'name' => 'Bangkok Bank'],
                        ['code' => 'THB_ISBT', 'name' => 'Iowa State Bank and Trust Company'],
                        ['code' => 'THB_BAAC', 'name' => 'Bank for Agriculture and Agricultural Cooperatives'],
                        ['code' => 'THB_CITI', 'name' => 'Citibank'],
                        ['code' => 'THB_SCB', 'name' => 'Siam Commercial Bank'],
                        ['code' => 'THB_KTB', 'name' => 'Krung Thai Bank'],
                        ['code' => 'THB_BAY', 'name' => 'Bank of Ayudhya'],
                    ],
                ],
                [
                    'name' => 'Bigpayz',
                    'key' => 'bigpay',
                    'is_maintain' => '0',
                    'defined_amount' => [
                        'deposit' => [
                            'min' => 100,
                            'max' => 50000,
                            'predefined_amounts' => [
                                100,
                                250,
                                500,
                                1000,
                                2000,
                                10000,
                                20000,
                                50000,
                            ],
                        ],
                        'withdraw' => [
                            'min' => 500,
                            'max' => 100000,
                            'predefined_amounts' => [
                                500,
                                1000,
                                5000,
                                10000,
                                20000,
                                50000,
                                80000,
                                100000,
                            ],
                        ],
                    ],
                    'bank_list' => [
                        ['code' => 'THB_TSCO', 'name' => 'TISCO Bank'],
                        ['code' => 'THB_KK', 'name' => 'Kiatnakin Bank'],
                        ['code' => 'THB_SCBT', 'name' => 'Standard Chartered Bank (Thai)'],
                        ['code' => 'THB_BAY', 'name' => 'Bank of Ayudhya (Krungsri)'],
                        ['code' => 'THB_GSB', 'name' => 'Government Savings Bank'],
                        ['code' => 'THB_KTB', 'name' => 'Krung Thai Bank'],
                        ['code' => 'THB_UOB', 'name' => 'United Overseas Bank (Thai)'],
                        ['code' => 'THB_CIMB', 'name' => 'CIMB Thai Bank'],
                        ['code' => 'THB_SCB', 'name' => 'Siam Commercial Bank'],
                        ['code' => 'THB_KBANK', 'name' => 'Kasikorn Bank'],
                        ['code' => 'THB_TMB', 'name' => 'TMBThanachart Bank'],
                        ['code' => 'THB_BBL', 'name' => 'Bangkok Bank'],
                        ['code' => 'THB_BAAC', 'name' => 'Bank for Agriculture and Agricultural Cooperatives'],
                    ],
                ],
                [
                    'name' => 'Winpay',
                    'key' => 'winpay',
                    'is_maintain' => '0',
                    'defined_amount' => [
                        'deposit' => [
                            'min' => 100,
                            'max' => 49999,
                            'predefined_amounts' => [
                                100,
                                250,
                                500,
                                1000,
                                2000,
                                10000,
                                20000,
                                45000,
                            ],
                        ],
                        'withdraw' => [
                            'min' => 100,
                            'max' => 49999,
                            'predefined_amounts' => [
                                100,
                                1000,
                                2000,
                                5000,
                                10000,
                                20000,
                                30000,
                                45000,
                            ],
                        ],
                    ],
                    'bank_list' => [
                        ['code' => 'THB_GHB', 'name' => 'Government Housing Bank'],
                        ['code' => 'THB_TMB', 'name' => 'TMBThanachart Bank'],
                        ['code' => 'THB_CITI', 'name' => 'Citibank Thailand'],
                        ['code' => 'THB_SCBT', 'name' => 'Standard Chartered'],
                        ['code' => 'SMBT/SMBC', 'name' => 'Sumitomo Mitsui Trust Bank'],
                        ['code' => 'THB_TSCO', 'name' => 'TISCO Bank'],
                        ['code' => 'SME', 'name' => 'SME Development Bank'],
                        ['code' => 'THB_CIMB', 'name' => 'CIMB Thai Bank'],
                        ['code' => 'THB_UOB', 'name' => 'United Overseas Bank'],
                        ['code' => 'THB_BBL', 'name' => 'BANGKOK BANK'],
                        ['code' => 'ANZ', 'name' => 'Australia and New Zealand Banking Group Limited'],
                        ['code' => 'THB_KK', 'name' => 'KIATNAKIN BANK'],
                        ['code' => 'THB_SCB', 'name' => 'The Siam Commercial Bank'],
                        ['code' => 'THB_TCRB', 'name' => 'Thai Credit Retail Bank'],
                        ['code' => 'THB_GSB', 'name' => 'Government Savings Bank'],
                        ['code' => 'EXIM', 'name' => 'Export-Import Bank of Thailand'],
                        ['code' => 'BOC', 'name' => 'Bank of China (Thai)'],
                        ['code' => 'KBANK', 'name' => 'Kasikornbank'],
                        ['code' => 'THB_KTB', 'name' => 'Krung Thai Bank'],
                        ['code' => 'THB_BAY', 'name' => 'Bank of Ayudhya'],
                        ['code' => 'THB_ICBC', 'name' => 'ICBC Bank'],
                        ['code' => 'ISBT', 'name' => 'Islamic Bank of Thailand'],
                        ['code' => 'THB_LHBANK', 'name' => 'LH Bank'],
                        ['code' => 'THB_BAAC', 'name' => 'Bank for Agriculture and Agricultural Cooperatives'],
                    ],
                ],
            ],
            'userBanks' => [
                [
                    'is_disable' => false,
                    'bank_code' => 'THB_HSBC',
                    'bank_account_name' => 'KANAPTH ANTHA',
                    'bank_account_name_mask' => 'KANA****',
                    'bank_account_no' => '********',

                ],
            ],
        ],
        'crypto' => [
            [
                'currency' => 'USDT',
                'network' => [
                    'TRC20',
                ],
                'buyPrice' => 34.93,
                'sellPrice' => 34.93,
            ],
        ],
    ],
    'execeptContainer' => [
        '/games',
        '/casino',
    ],
    'swiperConfig' => [
        'loop' => false,
        'slidesPerView' => 'auto',
        'allowTouchMove' => true,
        'cssMode' => false,
        'persistent' => true,
        'navigation' => [
            'nextEl' => '.navigation__arrow--right',
            'prevEl' => '.navigation__arrow--left',
        ],
    ],
    'affiliate_tracking_params' => [
        'a',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'pxl',
        'zoneid',
        'aff_id',
        'querystring',
    ],
];
