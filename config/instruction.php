<?php

use App\Enums\UrlPathEnum;

return [
    'instructionItems' => [
        [
            'key' => 'p2p',
            'icon' => 'asset/icons/instruction/p2p.svg',
            'icon-active' => 'asset/icons/instruction/p2p-active.svg',
            'link' => UrlPathEnum::INSTRUCTION_OF_P2P_TRANSACTION,
        ],
        [
            'key' => 'register',
            'icon' => 'asset/icons/instruction/user.svg',
            'icon-active' => 'asset/icons/instruction/user-active.svg',
            'link' => UrlPathEnum::INSTRUCTION_OF_REGISTER,
        ],

        [
            'key' => 'deposit',
            'icon' => 'asset/icons/instruction/deposit.svg',
            'icon-active' => 'asset/icons/instruction/deposit-active.svg',
            'link' => UrlPathEnum::INSTRUCTION_OF_DEPOSIT,
        ],
        [
            'key' => 'withdrawal',
            'icon' => 'asset/icons/instruction/withdrawal.svg',
            'icon-active' => 'asset/icons/instruction/withdrawal-active.svg',
            'link' => UrlPathEnum::INSTRUCTION_OF_WITHDRAW,
        ],
    ],
];
