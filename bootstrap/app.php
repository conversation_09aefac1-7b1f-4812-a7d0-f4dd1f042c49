<?php

use App\Http\Middleware\BlockedCountryMiddleware;
use App\Http\Middleware\DetectDevice;
use App\Http\Middleware\DetectEvent;
use App\Http\Middleware\MinifyHtml;
use App\Http\Middleware\SeoMiddleware;
use App\Http\Middleware\SetLocale;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->encryptCookies(except: [
            'user',
            'lang',
            'a',
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
            'pxl',
            'zoneid',
            'aff_id',
            'querystring',
        ]);

        $middleware->append(DetectDevice::class);
        $middleware->append(BlockedCountryMiddleware::class);

        $middleware->web(prepend: [
            DetectEvent::class,
        ]);

        $middleware->web(append: [
            SetLocale::class,
            SeoMiddleware::class,
            MinifyHtml::class,
        ]);

        $middleware->validateCsrfTokens(except: ['*']);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })
    ->create();
