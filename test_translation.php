<?php

/**
 * <PERSON>ript test translation
 */

// Simulate <PERSON><PERSON>'s translation function
function __($key, $replace = [], $locale = null) {
    static $translations = null;
    
    if ($translations === null) {
        $translations = [
            'vi' => [],
            'en' => []
        ];
        
        // Load Vietnamese translations
        $viFiles = glob('lang/vi/*.php');
        foreach ($viFiles as $file) {
            $category = basename($file, '.php');
            $translations['vi'][$category] = include $file;
        }
        
        // Load English translations
        $enFiles = glob('lang/en/*.php');
        foreach ($enFiles as $file) {
            $category = basename($file, '.php');
            $translations['en'][$category] = include $file;
        }
    }
    
    $locale = $locale ?: 'vi'; // Default to Vietnamese
    
    if (strpos($key, '.') !== false) {
        list($category, $subKey) = explode('.', $key, 2);
        
        // Handle nested keys
        $keys = explode('.', $subKey);
        $value = $translations[$locale][$category] ?? [];
        
        foreach ($keys as $k) {
            if (is_array($value) && isset($value[$k])) {
                $value = $value[$k];
            } else {
                return $key; // Return key if not found
            }
        }
        
        return is_string($value) ? $value : $key;
    }
    
    return $translations[$locale]['common'][$key] ?? $key;
}

// Test some translations
echo "=== TEST TRANSLATION SYSTEM ===\n\n";

$testKeys = [
    'common.trang_chu_9',
    'common.ho_tro_2',
    'common.nap_tien_2',
    'common.rut_tien_2',
    'common.tai_khoan_2',
    'auth.ten_hien_thi',
    'auth.mat_khau_moi_1',
    'header.menus.vietnamese',
    'header.menus.english',
    'account.overview.wallet.amount_title',
    'account.menus.overview',
];

echo "TIẾNG VIỆT:\n";
echo str_repeat("-", 40) . "\n";
foreach ($testKeys as $key) {
    $translation = __($key, [], 'vi');
    echo "$key => $translation\n";
}

echo "\nTIẾNG ANH:\n";
echo str_repeat("-", 40) . "\n";
foreach ($testKeys as $key) {
    $translation = __($key, [], 'en');
    echo "$key => $translation\n";
}

echo "\n=== KIỂM TRA MỘT SỐ FILE BLADE ===\n\n";

// Test một file blade đã được thay thế
$testFile = 'resources/views/components/ui/bottom-bar.blade.php';
if (file_exists($testFile)) {
    $content = file_get_contents($testFile);
    
    echo "File: $testFile\n";
    echo "Có chứa __() functions: " . (strpos($content, "__('") !== false ? "CÓ" : "KHÔNG") . "\n";
    
    // Tìm các __() calls
    if (preg_match_all("/__\('([^']+)'\)/", $content, $matches)) {
        echo "Các translation keys tìm thấy:\n";
        foreach (array_unique($matches[1]) as $key) {
            echo "  - $key\n";
        }
    }
}

echo "\n=== HƯỚNG DẪN TIẾP THEO ===\n";
echo "1. Khởi động Laravel server: php artisan serve\n";
echo "2. Truy cập website và test chuyển đổi ngôn ngữ\n";
echo "3. Kiểm tra xem các text đã được dịch chưa\n";
echo "4. Nếu vẫn còn thiếu keys, chạy lại check_missing_translations.php\n";

echo "\nHoàn thành test!\n";
