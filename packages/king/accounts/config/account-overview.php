<?php

use App\Enums\UrlPathEnum;

return [
    'game_swiper_config' => [
        'slidesPerView' => 1.275,
        'spaceBetween' => 4,
        'pagination' => [
            'el' => '.overview-games-swiper-pagination',
            'clickable' => true,
            'type' => 'bullets',
        ],
        'autoplay' => [
            'delay' => 2500,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
        'breakpoints' => [
            '1024' => [
                'slidesPerView' => 1.15,
                'spaceBetween' => 4,
            ],
        ],
    ],
    'category_swiper_config' => [
        'slidesPerView' => 2.78,
        'spaceBetween' => 12,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
        'autoplay' => [
            'delay' => 3000,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
    ],
    'category_mb_swiper_config' => [
        'slidesPerView' => 3.2,
        'spaceBetween' => 8,
        'pagination' => [
            'el' => '.swiper-pagination',
            'clickable' => true,
        ],
        'autoplay' => [
            'delay' => 3000,
            'disableOnInteraction' => false,
        ],
        'speed' => 800,
        'loop' => true,
    ],
    'category_list' => [
        [
            'name' => 'K-sports',
            'image' => 'k-sports',
            'link' => UrlPathEnum::K_SPORTS,
        ],
        [
            'name' => __('account.quay_so_1'),
            'image' => 'quay-so',
            'link' => UrlPathEnum::LOTTERY,
        ],
        [
            'name' => __('account.no_hu_1'),
            'image' => 'no-hu',
            'link' => UrlPathEnum::NOHU,
        ],
        [
            'name' => 'iM-sports',
            'image' => 'im-sports',
            'link' =>  UrlPathEnum::IM_SPORTS,
        ],
        [
            'name' => __('common.song_bai_1'),
            'image' => 'song-bai',
            'link' => UrlPathEnum::CASINO,
        ],
        [
            'name' => __('account.ban_ca_1'),
            'image' => 'ban-ca',
            'link' => UrlPathEnum::FISHING,
        ],
        [
            'name' => 'Saba-sports',
            'image' => 'saba',
            'link' => UrlPathEnum::SABA_SPORTS,
        ],
        [
            'name' => 'Slots',
            'image' => 'slots',
            'link' => UrlPathEnum::SLOTS,
        ],
    ],
    'history' => [
        'typeTransaction' => [
            'WITHDRAW' => __('common.rut_tien_2'),
            'DEPOSIT' => __('common.nap_tien_2'),
        ],
        'methodTransaction' => [
            'bank_account' => __('account.chuyen_khoan_3'),
            'nicepay' => 'nicepay',
            'phone_card' => __('account.the_cao_11'),
            'daily_cashback_slot' => __('account.hoan_tra_slots_3'),
            'cancel_promotion' => 'Cancel Promotion',
            'crypto' => 'Crypto',
        ],
        'statusTransaction' => [
            'CANCEL' => __('account.that_bai_7'),
            'DRAFT' => __('account.Dang_xu_ly_31'),
            'FINISHED' => __('account.hoan_thanh_7'),
            'PENDING' => __('account.Dang_xu_ly_31'),
            'APPROVED' => __('account.Dang_xu_ly_31'),
            'PROCESSING' => __('account.Dang_xu_ly_31'),
            'WAITING' => __('account.Dang_xu_ly_31'),
            'PHONE_CARD_PROCESSING' => __('account.Dang_xu_ly_31'),
            'PHONE_CARD_PENDING' => __('account.Dang_xu_ly_31'),
            'PHONE_CARD_FINISHED' => __('account.hoan_thanh_7'),
            'PHONE_CARD_CANCEL' => __('account.that_bai_7'),
            'PHONE_CARD_DRAFT' => __('account.Dang_xu_ly_31'),
        ],
    ],
];
