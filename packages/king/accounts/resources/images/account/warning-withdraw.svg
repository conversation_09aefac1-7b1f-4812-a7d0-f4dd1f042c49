<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="43.5038" height="50.5882" rx="3.5443" transform="matrix(0.968477 0.249101 -0.249098 0.968478 32.1934 7.5)" fill="url(#paint0_linear_8140_10058)"/>
<g filter="url(#filter0_b_8140_10058)">
<rect x="19" y="22.5632" width="62.0249" height="54.9368" rx="3.5443" fill="#A8D4FF" fill-opacity="0.35"/>
<rect x="19.443" y="23.0063" width="61.1389" height="54.0507" rx="3.10126" stroke="url(#paint1_linear_8140_10058)" stroke-width="0.886074" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<path d="M64.1231 51.5362C64.9257 55.173 68.1266 57.7318 71.781 57.6652H88.1719C89.6924 57.6652 90.9257 56.4108 90.9257 54.8602V45.1809C90.9225 43.6336 89.6924 42.3759 88.1719 42.3726H71.4874C67.3305 42.3859 63.973 45.8231 63.9795 50.0588C63.9795 50.5546 64.0284 51.0504 64.1231 51.5362Z" fill="url(#paint2_linear_8140_10058)"/>
<g filter="url(#filter1_bd_8140_10058)">
<ellipse cx="71.6264" cy="50.0195" rx="3.27725" ry="3.27727" fill="url(#paint3_linear_8140_10058)"/>
<path d="M74.815 50.0195C74.815 51.7805 73.3874 53.2081 71.6264 53.2081C69.8653 53.2081 68.4377 51.7805 68.4377 50.0195C68.4377 48.2584 69.8653 46.8308 71.6264 46.8308C73.3874 46.8308 74.815 48.2584 74.815 50.0195Z" stroke="url(#paint4_linear_8140_10058)" stroke-opacity="0.5" stroke-width="0.177215"/>
</g>
<g filter="url(#filter2_bd_8140_10058)">
<path d="M54.4928 50.605L50.6043 54.4963C50.3355 54.7644 49.9003 54.7644 49.6322 54.4963C49.3634 54.2281 49.3634 53.7881 49.6322 53.52L52.4654 50.6875H45.1875C44.8073 50.6875 44.5 50.3781 44.5 50C44.5 49.6219 44.8073 49.3125 45.1875 49.3125H52.4654L49.6322 46.48C49.3634 46.2119 49.3634 45.7787 49.6322 45.5037C49.9003 45.2356 50.3355 45.2356 50.6043 45.5037L54.4928 49.395C54.6578 49.56 54.7094 49.7869 54.6716 50C54.7094 50.2131 54.6578 50.44 54.4928 50.605ZM50 39C43.9246 39 39 43.9225 39 50C39 56.0775 43.9246 61 50 61C56.0754 61 61 56.0775 61 50C61 43.9225 56.0754 39 50 39Z" fill="url(#paint5_linear_8140_10058)"/>
<path d="M45.1875 50.8049H52.1819L49.5493 53.4369C49.2345 53.7509 49.2345 54.2654 49.5493 54.5794C49.8633 54.8933 50.3727 54.8931 50.6872 54.5794L50.6874 54.5792L54.5758 50.688C54.7667 50.4972 54.8272 50.239 54.7905 50C54.8272 49.761 54.7667 49.5028 54.5758 49.312L50.6874 45.4208L50.6872 45.4206C50.3726 45.1069 49.8632 45.1067 49.5492 45.4207L49.5482 45.4217C49.2354 45.7417 49.2339 46.2486 49.5493 46.5631L52.1819 49.1951H45.1875C44.7422 49.1951 44.3826 49.5573 44.3826 50C44.3826 50.4427 44.7422 50.8049 45.1875 50.8049ZM39.1174 50C39.1174 43.9874 43.9894 39.1174 50 39.1174C56.0106 39.1174 60.8826 43.9874 60.8826 50C60.8826 56.0126 56.0106 60.8826 50 60.8826C43.9894 60.8826 39.1174 56.0126 39.1174 50Z" stroke="url(#paint6_linear_8140_10058)" stroke-opacity="0.5" stroke-width="0.234802"/>
</g>
<circle cx="19" cy="75" r="16" fill="url(#paint7_linear_8140_10058)"/>
<path d="M18.3857 66.0233C16.5026 66.4564 15.4574 68.1863 15.6646 70.1327L16.9215 76.6214C17.0196 77.0409 17.2278 77.4244 17.523 77.7296C17.8183 78.0347 18.1893 78.2499 18.5951 78.3513C19.8498 78.5678 20.8972 77.9183 21.1067 76.6214L22.3613 70.1327V68.8335C22.1337 67.9509 21.6073 67.1819 20.8778 66.6663C20.1483 66.1506 19.264 65.9225 18.3857 66.0233Z" fill="white"/>
<path d="M19 86C20.4851 86 21.6891 84.8004 21.6891 83.3206C21.6891 81.8408 20.4851 80.6411 19 80.6411C17.5149 80.6411 16.3109 81.8408 16.3109 83.3206C16.3109 84.8004 17.5149 86 19 86Z" fill="white"/>
<defs>
<filter id="filter0_b_8140_10058" x="-2.26577" y="1.29746" width="104.556" height="97.4683" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.6329"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_8140_10058"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_8140_10058" result="shape"/>
</filter>
<filter id="filter1_bd_8140_10058" x="55.058" y="33.4511" width="33.1369" height="33.1368" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="6.64555"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_8140_10058"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4.43037" dy="4.43037"/>
<feGaussianBlur stdDeviation="4.43037"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.321569 0 0 0 0 0.54902 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_8140_10058" result="effect2_dropShadow_8140_10058"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_8140_10058" result="shape"/>
</filter>
<filter id="filter2_bd_8140_10058" x="21.3898" y="21.3898" width="57.2203" height="57.2203" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="8.80508"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_8140_10058"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5.87005" dy="5.87005"/>
<feGaussianBlur stdDeviation="5.87005"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.85 0 0 0 0 0.85 0 0 0 0 0.85 0 0 0 0.5 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_8140_10058" result="effect2_dropShadow_8140_10058"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_8140_10058" result="shape"/>
</filter>
<linearGradient id="paint0_linear_8140_10058" x1="40.3921" y1="-1.77192e-05" x2="-9.41856" y2="29.5721" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint1_linear_8140_10058" x1="28.8734" y1="28.9634" x2="73.3551" y2="80.1223" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_8140_10058" x1="88.9984" y1="42.3726" x2="72.1696" y2="62.8423" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint3_linear_8140_10058" x1="75.4621" y1="50.0316" x2="68.3736" y2="50.0316" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint4_linear_8140_10058" x1="69.0552" y1="48.9409" x2="74.6209" y2="48.9716" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_8140_10058" x1="59.4402" y1="42.9728" x2="34.9343" y2="43.7718" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.2"/>
</linearGradient>
<linearGradient id="paint6_linear_8140_10058" x1="41.3699" y1="46.3799" x2="60.0509" y2="46.4831" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_8140_10058" x1="5.66667" y1="65" x2="29.3333" y2="88" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2B84B"/>
<stop offset="1" stop-color="#D06901"/>
</linearGradient>
</defs>
</svg>
