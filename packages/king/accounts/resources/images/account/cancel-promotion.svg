<svg width="96" height="96" viewBox="0 0 96 96" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5" filter="url(#filter0_f_7456_51161)">
<path d="M56.1211 61.9916C58.4704 63.0002 61.2022 62.074 62.4537 59.8446L69.2948 47.6575C70.5463 45.4281 69.9143 42.6137 67.8298 41.1334L46.5246 26.0039C44.1024 24.2839 40.7237 25.0426 39.2696 27.6331L29.7223 44.641C28.2681 47.2315 29.38 50.511 32.1098 51.6829L56.1211 61.9916Z" fill="#3766FF" fill-opacity="0.5"/>
</g>
<rect x="18" y="23" width="58" height="54" rx="6" fill="url(#paint0_linear_7456_51161)"/>
<g filter="url(#filter1_b_7456_51161)">
<path d="M21.979 77.0001C19.5736 76.582 17 74.0985 17 71.6004V40.3667H43V77.0001H21.979Z" fill="#A8D4FF" fill-opacity="0.35"/>
<path d="M17.443 71.6004V40.8097H42.557V76.5571H22.0183C20.9364 76.3587 19.7891 75.6941 18.9074 74.7737C18.0144 73.8416 17.443 72.7004 17.443 71.6004Z" stroke="url(#paint1_linear_7456_51161)" stroke-width="0.886074" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter2_b_7456_51161)">
<path d="M51 77.0001V40.3667H77V71.4634C77 71.8122 76.6952 72.9626 76.5548 73.3461C75.8508 75.2672 73.9498 76.5217 72.0228 77.0001H51.0018H51Z" fill="#A8D4FF" fill-opacity="0.35"/>
<path d="M51.443 40.8097V76.5571H71.9678C73.7884 76.0911 75.5083 74.9141 76.1387 73.1938L51.443 40.8097ZM51.443 40.8097H76.557V71.4634C76.557 71.5053 76.5454 71.6068 76.5148 71.7686C76.4861 71.9208 76.4456 72.1008 76.3999 72.2854C76.3076 72.659 76.2008 73.0243 76.1388 73.1937L51.443 40.8097Z" stroke="url(#paint2_linear_7456_51161)" stroke-width="0.886074" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter3_b_7456_51161)">
<path d="M43 23.0073V38.3174H17.3083C16.194 38.3174 14.6481 36.4529 14.3368 35.4193C13.9107 34.0041 13.9599 28.8125 14.1001 27.1708C14.2475 25.458 15.8608 23.0073 17.7162 23.0073H42.9982H43Z" fill="#A8D4FF" fill-opacity="0.35"/>
<path d="M42.557 37.8743V23.4504H17.7162C16.9844 23.4504 16.2313 23.9423 15.6152 24.7215C15.0049 25.4934 14.6065 26.454 14.5415 27.2087C14.4732 28.0089 14.4257 29.7008 14.4491 31.3738C14.4607 32.2087 14.4899 33.032 14.5421 33.7313C14.5952 34.4425 14.6699 34.9889 14.761 35.2916L42.557 37.8743ZM42.557 37.8743H17.3083C17.1465 37.8743 16.924 37.8034 16.652 37.6339C16.386 37.4682 16.1073 37.2298 15.8429 36.9511C15.3046 36.3836 14.8894 35.7179 14.761 35.2916L42.557 37.8743Z" stroke="url(#paint3_linear_7456_51161)" stroke-width="0.886074" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<g filter="url(#filter4_b_7456_51161)">
<path d="M51.0018 38.3174V23.0073H76.2892C77.6041 23.0073 79.1795 24.6453 79.5983 25.8396C80.101 27.2713 80.0391 32.4556 79.9061 34.152C79.7896 35.6385 79.1139 37.0336 77.8136 37.7951C77.6551 37.8882 76.7809 38.3155 76.6972 38.3155H51L51.0018 38.3174Z" fill="#A8D4FF" fill-opacity="0.35"/>
<path d="M51.4449 23.4504V23.0073H76.2892C77.6041 23.0073 79.1795 24.6453 79.5983 25.8396L79.4644 34.1174C79.4644 34.1174 79.4644 34.1174 79.4644 34.1174C79.3561 35.4994 78.7338 36.7428 77.5897 37.4128L77.5891 37.4132C77.5291 37.4484 77.2883 37.5706 77.0369 37.6902C76.9147 37.7484 76.7982 37.8021 76.7091 37.8405C76.6778 37.8541 76.6522 37.8647 76.6324 37.8725H51.4449M51.4449 23.4504H51.0018V37.8725H51.4449M51.4449 23.4504V37.8725M51.4449 23.4504V37.8725" stroke="url(#paint4_linear_7456_51161)" stroke-width="0.886074" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<path d="M23.2895 21.9568C22.2292 19.6193 22.2292 17.68 23.1265 15.3024C23.6704 13.8653 25.2214 10.2094 26.3019 9.31099C28.0434 7.86472 30.5723 7.70402 32.6489 8.45637C35.7583 10.1163 38.5491 12.3368 41.5193 14.2342L40.4133 15.7991L32.1123 10.4961C30.25 9.45342 26.0492 10.3701 27.1479 13.1239C27.7998 14.7583 30.3525 16.7633 32.1325 16.7633H39.8767C39.2523 18.4725 39.2871 20.2073 39.7394 21.9568H23.2895Z" fill="url(#paint5_linear_7456_51161)"/>
<path d="M70.7163 21.9568H54.2682C54.7224 20.2056 54.7553 18.4726 54.1309 16.7634H61.8751C63.6716 16.7634 66.2023 14.7711 66.8597 13.124C67.9658 10.3501 63.7778 9.44985 61.8953 10.4962L53.5943 15.7992L52.4883 14.2342C54.9934 12.7058 57.3282 10.7628 59.8461 9.27089C62.3128 7.81001 65.3343 7.34253 67.7039 9.31107C68.7788 10.204 69.9032 12.9761 70.4672 14.3456C71.5916 17.0775 72.0659 19.1282 70.7181 21.9568H70.7163Z" fill="url(#paint6_linear_7456_51161)"/>
<path d="M46.7694 14.0026C50.8479 13.8824 53.7382 18.2691 51.8561 21.9998C50.7037 21.985 42.7017 22 41.9901 22C41.8745 21.878 41.5963 21.0073 41.5529 20.7873C40.8864 17.3857 43.3538 14.1043 46.7694 14.0026Z" fill="url(#paint7_linear_7456_51161)"/>
<circle cx="76" cy="74.7848" r="16" fill="url(#paint8_linear_7456_51161)"/>
<path d="M73.6747 78.0536V77.7064C73.6747 76.7468 73.7537 75.982 73.9118 75.4118C74.0755 74.8417 74.3125 74.3845 74.623 74.0402C74.9335 73.6902 75.3117 73.3713 75.7576 73.0834C76.1132 72.8576 76.4293 72.629 76.7059 72.3976C76.9881 72.1605 77.2111 71.9065 77.3748 71.6355C77.5385 71.3646 77.6203 71.057 77.6203 70.7126C77.6203 70.3796 77.5413 70.0861 77.3833 69.8321C77.2252 69.5781 77.0079 69.3833 76.7313 69.2478C76.4603 69.1067 76.1584 69.0362 75.8253 69.0362C75.4923 69.0362 75.179 69.1124 74.8855 69.2648C74.5976 69.4172 74.3605 69.6345 74.1742 69.9167C73.9936 70.1933 73.9005 70.5292 73.8948 70.9243H70C70.0169 69.7389 70.2907 68.768 70.8213 68.0117C71.3575 67.2553 72.066 66.6964 72.9465 66.3352C73.8271 65.9683 74.798 65.7848 75.8592 65.7848C77.022 65.7848 78.055 65.9655 78.9581 66.3267C79.8669 66.688 80.581 67.2214 81.1003 67.927C81.6196 68.6326 81.8793 69.4962 81.8793 70.5179C81.8793 71.1896 81.7664 71.7823 81.5406 72.296C81.3148 72.8096 80.9987 73.264 80.5923 73.6592C80.1915 74.0486 79.7202 74.4043 79.1783 74.726C78.7549 74.9744 78.4021 75.234 78.1199 75.505C77.8433 75.7759 77.6344 76.0864 77.4933 76.4363C77.3522 76.7807 77.2817 77.204 77.2817 77.7064V78.0536H73.6747ZM75.5459 83.6079C74.9363 83.6079 74.4141 83.3934 73.9795 82.9644C73.5505 82.5354 73.3388 82.0161 73.3445 81.4065C73.3388 80.8082 73.5505 80.2973 73.9795 79.874C74.4141 79.445 74.9363 79.2305 75.5459 79.2305C76.1273 79.2305 76.6382 79.445 77.0784 79.874C77.5187 80.2973 77.7417 80.8082 77.7473 81.4065C77.7417 81.8129 77.6344 82.1826 77.4256 82.5157C77.2224 82.8487 76.9543 83.114 76.6212 83.3116C76.2938 83.5091 75.9354 83.6079 75.5459 83.6079Z" fill="white"/>
<defs>
<filter id="filter0_f_7456_51161" x="8.08057" y="4.07971" width="82.8545" height="79.3182" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="10.5" result="effect1_foregroundBlur_7456_51161"/>
</filter>
<filter id="filter1_b_7456_51161" x="-4.26577" y="19.1009" width="68.5315" height="79.165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.6329"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7456_51161"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7456_51161" result="shape"/>
</filter>
<filter id="filter2_b_7456_51161" x="29.7342" y="19.1009" width="68.5315" height="79.165" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.6329"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7456_51161"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7456_51161" result="shape"/>
</filter>
<filter id="filter3_b_7456_51161" x="-7.26577" y="1.74155" width="71.5315" height="57.8416" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.6329"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7456_51161"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7456_51161" result="shape"/>
</filter>
<filter id="filter4_b_7456_51161" x="29.7342" y="1.74155" width="71.5315" height="57.8416" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="10.6329"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7456_51161"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7456_51161" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7456_51161" x1="71.8515" y1="23" x2="13.8999" y2="65.9715" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint1_linear_7456_51161" x1="21.1388" y1="44.6345" x2="49.5817" y2="65.1984" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_7456_51161" x1="55.1388" y1="44.6345" x2="83.5817" y2="65.1984" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_7456_51161" x1="18.6163" y1="24.791" x2="28.8441" y2="44.526" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_7456_51161" x1="55.6163" y1="24.791" x2="65.8441" y2="44.526" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_7456_51161" x1="40.157" y1="8.01464" x2="24.5473" y2="22.7367" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint6_linear_7456_51161" x1="70.1766" y1="8" x2="54.5511" y2="22.7246" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint7_linear_7456_51161" x1="51.6096" y1="14" x2="42.4691" y2="22.4514" gradientUnits="userSpaceOnUse">
<stop stop-color="#39AFFD"/>
<stop offset="1" stop-color="#477FFF"/>
</linearGradient>
<linearGradient id="paint8_linear_7456_51161" x1="62.6667" y1="64.7848" x2="86.3333" y2="87.7848" gradientUnits="userSpaceOnUse">
<stop stop-color="#F2B84B"/>
<stop offset="1" stop-color="#D06901"/>
</linearGradient>
</defs>
</svg>
