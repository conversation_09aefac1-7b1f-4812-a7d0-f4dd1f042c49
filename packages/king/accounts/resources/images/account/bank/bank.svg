<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="40" height="40" rx="12" fill="#F8FAFB"/>
<g clip-path="url(#clip0_10885_26732)">
<path d="M13 8C11.3431 8 10 9.28942 10 10.88V25.28C10 26.8706 11.3431 28.16 13 28.16H15.5V16.64C15.5 15.5796 16.3954 14.72 17.5 14.72H25V12.3568C25 11.8705 24.8078 11.4023 24.4621 11.0467L22.093 8.60995C21.7148 8.22089 21.1853 8 20.6309 8H13Z" fill="#666D80"/>
<g filter="url(#filter0_biii_10885_26732)">
<path d="M12 11.8399C12 10.7795 12.8954 9.91992 14 9.91992H25.6309C26.1853 9.91992 26.7148 10.1408 27.093 10.5299L29.4621 12.9667C29.8078 13.3222 30 13.7904 30 14.2767V30.0799C30 31.1403 29.1046 31.9999 28 31.9999H14C12.8954 31.9999 12 31.1403 12 30.0799V11.8399Z" fill="#C1C7D0" fill-opacity="0.3"/>
<path d="M12 11.8399C12 10.7795 12.8954 9.91992 14 9.91992H25.6309C26.1853 9.91992 26.7148 10.1408 27.093 10.5299L29.4621 12.9667C29.8078 13.3222 30 13.7904 30 14.2767V30.0799C30 31.1403 29.1046 31.9999 28 31.9999H14C12.8954 31.9999 12 31.1403 12 30.0799V11.8399Z" fill="#C1C7D0" fill-opacity="0.3"/>
</g>
<path d="M15 23.8399C15 23.3097 15.4477 22.8799 16 22.8799H26C26.5523 22.8799 27 23.3097 27 23.8399C27 24.3701 26.5523 24.7999 26 24.7999H16C15.4477 24.7999 15 24.3701 15 23.8399Z" fill="white"/>
<path d="M15 27.1999C15 26.6697 15.4477 26.2399 16 26.2399H19C19.5523 26.2399 20 26.6697 20 27.1999C20 27.7301 19.5523 28.1599 19 28.1599H16C15.4477 28.1599 15 27.7301 15 27.1999Z" fill="white"/>
<path d="M26 26.2399C25.4477 26.2399 25 26.6697 25 27.1999C25 27.7301 25.4477 28.1599 26 28.1599C26.5523 28.1599 27 27.7301 27 27.1999C27 26.6697 26.5523 26.2399 26 26.2399Z" fill="white"/>
</g>
<defs>
<filter id="filter0_biii_10885_26732" x="5.28" y="3.19992" width="31.44" height="35.5201" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="3.36"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_10885_26732"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_10885_26732" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.44"/>
<feGaussianBlur stdDeviation="0.72"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_10885_26732"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.92"/>
<feGaussianBlur stdDeviation="0.96"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_10885_26732" result="effect3_innerShadow_10885_26732"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.48"/>
<feGaussianBlur stdDeviation="0.24"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="effect3_innerShadow_10885_26732" result="effect4_innerShadow_10885_26732"/>
</filter>
<clipPath id="clip0_10885_26732">
<rect width="20" height="24" fill="white" transform="translate(10 8)"/>
</clipPath>
</defs>
</svg>
