@props([
    'placeholder' => '',
    'options' => [],
    'subOptions' => [],
    'defaultValue' => null,
    'class' => '',
    'name' => 'bank',
    'label' => '',
    'isRequire' => false,
])

@php
    $defaultLabel = '';
    $defaultImage = 'vendor/accounts/images/account/bank/bank-circle.webp';

    if ($defaultValue && is_string($defaultValue)) {
        $newOptions = array_filter($options, function ($option) use ($defaultValue) {
            return $option -> value === $defaultValue;
        });

        $target = array_pop($newOptions);
        $defaultLabel = $target -> label ?? $defaultValue;
        $defaultImage = $target -> image ?? $defaultValue;
    } else {
        $defaultLabel = $placeholder;
    }

    $validOptions = array_filter(
                    $options,
                    function ($item) {
                        return $item -> bank_status !== 0;
                    }
                );
@endphp

<div 
    class="dropdown-bank-container flex flex-col gap-[8px] w-full [&.error-validate_.dropdown-bank-button]:border-danger-500 [&:has(.selected)_.dropdown-bank-icon-other]:bg-secondary-500 {{ $class }}" 
    data-placeholder="{{ $defaultLabel }}"
    data-icon="{{ asset($defaultImage) }}"
>
    @if ($label)
        <p class="text-[12px] leading-[18px] font-normal text-neutral-1000">
            {{ $label }}
        </p>
    @endif

    <div class="dropdown-bank-wrapper relative w-full">
        <button 
            type="button"
            class='dropdown-bank-button flex items-center justify-between gap-[8px] flex-1 w-full h-[40px] border border-neutral-150 p-[8px] [.active-drop_&]:border-info-500 rounded-[8px] bg-neutral xl:py-[10px] xl:px-[12px]'
        >
            <img src="{{ asset($defaultImage)}}" alt="bank" class="dropdown-bank-image w-[24px] h-[24px] rounded-full"/>
            <p class="dropdown-bank-text flex-1 text-[14px] leading-[20px] flex items-center gap-1 font-normal text-neutral-1000 text-left">
                {{ $defaultLabel }}
            </p>
            <i class="dropdown-bank-arrow icon-arrow-down text-[20px] text-neutral-600 [.active-drop_&]:rotate-180"></i>
            <input name="{{ $name }}" value="{{ $defaultValue }}" type="radio" class="peer absolute pointer-events-none opacity-0"/>
        </button>
        <div id="overlay-dropdown-bank" class="fixed hidden xl:!hidden top-0 left-0 right-0 bottom-0 bg-black-50 h-full z-40 w-full"></div>

        <div class="dropdown-bank-list scrollbar fixed bottom-0 left-0 z-50 hidden flex-col w-full [&:has(input:focus)]:min-h-[394px] [&:has(input:focus)]:xl:min-h-max xl:min-h-max h-max max-h-dvh pt-[12px] pb-[10px] px-[16px] bg-neutral rounded-t-[24px] shadow-shadow-bank overflow-auto [.active-drop_&]:flex xl:absolute xl:top-full xl:z-[2] xl:py-[10px] xl:px-[12px] xl:rounded-[8px] transition-all duration-300">
            <div class="flex justify-between items-center gap-[10px] mb-[22px] xl:hidden">
                <div class="flex items-center gap-[8px]">
                    <img src="{{ asset('vendor/accounts/images/account/bank/bank.webp')}}" alt="bank" class="w-[32px] h-[32px]"/>
                    <p class="text-[14px] leading-[20px] font-medium">
                        @if (count($options) > 0)
                            Chọn Ngân Hàng của bạn
                        @else
                            Chọn Ngân Hàng
                        @endif
                    </p>
                </div>
                <button 
                    class="dropdown-bank-close flex items-center justify-center w-[32px] h-[32px] text-xl bg-neutral-100 rounded-[8px]"
                    type="button"
                >
                    <img alt="close" src="{{ asset('asset/images/close-blue.webp') }}" class="w-[8px] h-[8px] object-cover"/>
                </button>
            </div>
            <x-kit.search-input class="search-mobile h-[36px] mb-[10px] [&_i]:translate-y-[2px]" placeholder=__('account.tim_kiem_ngan_hang_1') showClose closeClass="absolute right-6 xl:static"></x-kit.search-input>
            @if (count($validOptions) > 0)
                <div class="flex flex-col gap-[8px] mb-[16px]">
                    <p class="text-[12px] leading-[18px] text-neutral-1000__('account.tai_khoan_ngan_hang_cua_ban_span_class')font-medium">{{ !empty($validOptions) && count($validOptions) > 0 ? $validOptions[0]->bank_account_name : '' }}</span></p>
                    <ul class="dropdown-bank-main scrollbar grid grid-cols-2 gap-[8px] max-h-[136px] overflow-auto xl:grid-cols-3 xl:max-h-[168px]">
                        @foreach (array_reverse($validOptions) as $index => $item)
                            @php
                                $color = 'text-success-600';

                                if ($item -> bank_status === 1) {
                                    $color = 'text-warning-600';
                                }
                            @endphp
                            <li>
                                <button class="dropdown-bank-item w-full" type="button" data-name="{{ $item -> bank_account_name }}" data-no="{{ $item -> bank_account_no }}" data-status="{{ $item -> bank_status }}" data-value="{{ $item -> value }}" data-label="{{ $item -> label }}" data-image="{{ $item -> image }}">
                                    <label class="w-full cursor-pointer">
                                        <input @if($item -> value === $defaultValue) checked @endif field-type='select' name="{{ $name }}" value="{{ $item -> value }}" type="radio" class="peer" hidden />
                                        <div class="relative flex items-center gap-[8px] py-[8px] px-[12px] rounded-[10px] border border-transparent bg-neutral-100 pointer-events-none hover:border-info-200 hover:bg-neutral overflow-hidden peer-checked:border-info-200 peer-checked:bg-neutral">
                                            <img alt="bank" src="{{ $item -> image }}" class="w-[28px] h-[28px] rounded-full xl:w-[32px] xl:h-[32px]"/>
                                            <div class="flex flex-col items-start">
                                                <p class="text-[12px] leading-[18px] font-medium text-neutral-800">{{ $item -> label }}</p>
                                                <p class="text-[12px] leading-[18px] font-medium text-secondary-500">{{ $item -> bank_account_no }}</p>
                                                <p @class([
                                                    "text-[10px] leading-[14px] font-medium",
                                                    $color
                                                    ])
                                                >
                                                    {{ $item -> bank_txt }}
                                                </p>
                                            </div>

                                            @if ($index === 0)
                                                <div class="absolute top-0 right-0">
                                                    <x-accounts::ui.account.label-amount type="recent"></x-accounts::ui.account.label-amount>
                                                </div>
                                            @endif
                                        </div>
                                    </label>
                                </button>
                            </li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <div class="dropdown-bank-sub flex flex-col gap-[8px]">
                @if (count($options) > 0)
                    <p class="text-[12px] leading-[18px] text-neutral-1000">Ngân hàng khác</p>
                @endif
                <ul class="scrollbar grid grid-cols-2 gap-[8px] {{count($validOptions) > 0 ? 'max-h-[160px]' : 'max-h-[272px]'}} overflow-auto xl:gap-0 xl:max-h-[192px]">
                    @foreach ($subOptions as $item)
                        <li>
                            <button class="dropdown-bank-item w-full" type="button" data-status="1" data-value="{{ $item -> value }}" data-label="{{ $item -> label }}" data-image="{{ $item -> image }}">
                                <label class="w-full cursor-pointer">
                                    <input field-type='select' name="{{ $name }}" value="{{ $item -> value }}" type="radio" class="peer" hidden/>
                                    <div class="flex items-center gap-[8px] h-[48px] p-[12px] rounded-[8px] bg-neutral text-[12px] leading-[18px] font-medium text-neutral-1000 pointer-events-none hover:bg-neutral-50 [&:hover_p]:text-secondary-500 peer-checked:bg-neutral-50 peer-checked:text-secondary-500">
                                        <img src="{{ $item -> image }}" alt="bank" class="w-[24px] h-[24px] rounded-full"/>
                                        {{ $item -> label }}
                                    </div>
                                </label>
                            </button>
                        </li>
                    @endforeach
                </ul>
            </div>
            <div class="dropdown-bank-empty hidden justify-center items-center py-[40px] px-[20px] [&.is-empty]:flex xl:pt-[16px]">
                <p class="text-[12px] leading-[18px] text-neutral-800 text-center xl:text-[14px] xl:leading-[20px]">Không có ngân hàng tìm kiếm</p>
            </div>
        </div>
    </div>

    @if ($isRequire)
        <span class="input-error text-[12px] hidden leading-[18px] font-normal text-danger-600"></span>
    @endif
</div>
