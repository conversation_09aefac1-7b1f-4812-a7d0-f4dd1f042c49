<div class="flex flex-col gap-[12px] xl:gap-[24px]">
    <form
        class="flex flex-col gap-[20px] pt-[16px] pb-[12px] px-[12px] bg-neutral rounded-[16px] xl:gap-[24px] xl:p-0"
        id="withdraw_crypto_form"
    >
        @csrf
        <input type="hidden" name="ex_rate" value="{{ $withdrawData['cryptoCurrencyList'][0]->price ?? '' }}" class="js-ex-rate-input"/>
        <input type="hidden" name="network" value="{{ $withdrawData['cryptoCurrencyList'][0]->network[0] ?? '' }}" class="js-network-input"/>
        
        <div class="flex flex-col gap-y-[8px]">
            <div class="text-[12px] leading-[18px] text-neutral-1000">Chọn loại tiền <PERSON></div>
            <div class="grid grid-cols-2 gap-[8px]">
                @foreach ($withdrawData['cryptoCurrencyList'] as $key => $cryptoCurrency)
                    <label class="relative cursor-pointer js-crypto-currency-option peer" data-min_txt="{{ $cryptoCurrency->min_txt ?? '' }}" data-price="{{ $cryptoCurrency->price ?? '' }}" data-currency="{{ $cryptoCurrency->currency ?? '' }}" data-network="{{ $cryptoCurrency->network[0] ?? '' }}">
                        <input type="radio" name="currency" value="{{ $cryptoCurrency->currency ?? '' }}" data-min="{{ $cryptoCurrency -> min ?? 0 }}" class="hidden js-network-radio peer"
                            {{ $key === 0 ? 'checked' : '' }}>
                        <span class="absolute top-[4px] right-[4px] hidden w-[12px] h-[12px] peer-checked:block">
                            <img src="{{ asset('asset/images/check-blue-circle.webp') }}" alt="check" class="w-full h-full"/>
                        </span>
                        <div
                            @class([
                                'js-network-border flex items-center py-[10px] px-[12px] rounded-[12px] bg-neutral-50 border border-secondary-neutral-100 peer-checked:border-info-500 peer-checked:bg-neutral',
                            ])>
                            <div class="flex items-center">
                                <img src="{{ asset('vendor/accounts/images/account/deposit/crypto/' . (isset($cryptoCurrency->currency) ? strtolower($cryptoCurrency->currency) : 'default') . '.svg') }}"
                                    alt="{{ $cryptoCurrency->name ?? '' }}" class="w-[24px] h-[24px] rounded-full object-cover">
                                <div class="ml-[8px]">
                                    <div class="text-[14px] leading-[20px] font-medium text-neutral-1000">
                                        {{ $cryptoCurrency->currency ?? '' }}
                                        ({{ implode(', ', $cryptoCurrency->network ?? []) }})
                                    </div>
                                    <div class="font-medium text-[12px] leading-[18px] text-secondary-500">≈
                                        {{ $cryptoCurrency->price_txt ?? 0 }} VND
                                    </div>
                                </div>
                            </div>
                        </div>
                    </label>
                @endforeach
            </div>
        </div>

        <x-kit.input 
            label=__('account.so_tien_rut_1')
            type="text" 
            placeholder=__('account.nhap_so_tien_rut_1') 
            rightText="= 0 {{ isset($withdrawData['cryptoCurrencyList']) && count($withdrawData['cryptoCurrencyList']) > 0 ? $withdrawData['cryptoCurrencyList'][0] -> currency : '' }}"
            name="amount_withdraw"
            maxlength="11"
            type="tel"
            inputClassName="js-amount-withdraw js-account-current-balance"
            oninput="formatAmountWithdraw(this, 9)"
            currentBalance="{{ Auth::user()->balance ?? 0 }}" 
            isRequire
            class="withdraw-amount [&_.input-field-wrap]:grid [&_.input-field-wrap]:grid-cols-[auto_auto]"
            noBackground
        />

        <x-kit.input
            type="text"
            placeholder=__('account.nhap_dia_chi_vi')
            name="wallet_address"
            class="w-full h-full"
            inputClassName="js-wallet-address"
            label=__('account.Dia_chi_vi')
            isRequire
            isPaste
            noBackground
        />
        <x-kit.input
            type="tel"
            placeholder=__('account.nhap_5_so_cuoi_dien_thoai_2')
            name="phone"
            maxlength="5"
            class="w-full h-full"
            inputClassName="js-phone js-phone-input"
            oninput="formatNumber(this)"
            label=__('common.so_dien_thoai_1')
            isRequire
            noBackground
            isPasteNumber
        />

        @if (session('error'))
            <div class="text-red-500">{{ session('error') }}</div>
        @endif
        <x-kit.button disabled buttonType="submit" class="button-submit w-full mx-auto xl:w-[220px]">Rút tiền</x-kit.button>
    </form>
    <div class="flex flex-col gap-[8px] xl:gap-[16px]">
        <x-accounts::ui.account.withdraw.guide-crypto :contentClass="$withdrawData['isWithdraw'] ? 'hidden' : ''" currentCrypto="{{ $withdrawData['cryptoCurrencyList'][0]->currency ?? '' }}" minWithdraw="{{ $withdrawData['cryptoCurrencyList'][0]->min_txt ?? '' }}" />
    </div>
</div>

@pushOnce('scripts')
    @vite('resources/js/withdraw/crypto.js')
@endPushOnce
