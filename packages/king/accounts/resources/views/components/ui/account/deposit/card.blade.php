
@php
    $listCard = [];
    $validMoney = '';

    if (isset($depositData['defaultNetwork']) && isset($depositData['defaultMoney'])) {
        $listCard = $depositData['networks'][$depositData['defaultNetwork']];

        $validCard = [];

        foreach($listCard -> value as $key => $item) {
            $validCard[] = $item;
        }

        if (in_array($depositData['defaultMoney'], $validCard)) {
            $validMoney = $depositData['defaultMoney'];
        }
    } else {
        $listCard = $depositData['networks']['VIETTEL'];
    }
@endphp

<div class="flex flex-col gap-3 xl:gap-6">
    <form method="POST" id="deposit_card_form" class="flex flex-col gap-[20px] py-[16px] px-[12px] bg-neutral rounded-[16px] xl:gap-[24px] xl:p-0">
        @csrf
        <div class="flex flex-col gap-2">
            <!-- Network Selection -->
            <div class="text-xs text-neutral-1000">Chọn nhà mạng</div>
            <div class="grid grid-cols-2 gap-2 xl:grid-cols-3">
                <input class="js-card-network-withdraw-input absolute opacity-0" name="to_telcom_code" value="{{ isset($depositData['defaultNetwork']) ? $depositData['defaultNetwork'] : 'VIETTEL' }}">
                @foreach((array) ($depositData['networks'] ?? []) as $key => $phonecard)
                        <div @class([
                            'relative flex items-center px-3 py-[3px] gap-2 border rounded-[12px] border-neutral-200 bg-neutral-100',
                            '!border-info-500 !bg-neutral' => isset($depositData['defaultNetwork']) ? (isset($depositData['defaultNetwork']) && $depositData['defaultNetwork'] === $key) : $key === 'VIETTEL',
                            $phonecard -> status === 0 ? 'disabled-withdraw cursor-not-allowed' : 'js-card-network-withdraw cursor-pointer',
                        ])
                        data-value="{{ $key }}"
                        data-card="{{ json_encode($phonecard) }}"
                    >
                    <!-- checked icon -->
                    <img src="{{ asset('asset/icons/checked.svg') }}" alt="checked"
                    @class(['js-card-network-checked w-[0.625rem] aspect-square absolute top-[0.314rem] right-[0.314rem] opacity-0', 'opacity-100' =>  $key === 'VIETTEL'])>

                        <div
                            class="bg-neutral-150 w-[24px] h-[24px] rounded-full flex items-center justify-center text-secondary-500 font-bold">
                            <img src="{{ asset('/asset/icons/account/withdraw/card/' . (strtolower($key)) . '.svg') }}"
                                alt="{{ $key }}" class="w-4 h-4 [.disabled-withdraw_&]:opacity-50">
                        </div>
                        <div class="flex flex-col">
                            <div class="js-network-label text-neutral-800 text-[14px] leading-[20px] font-medium capitalize [.disabled-withdraw_&]:text-neutral-400">{{ strtolower($key) }}</div>
    
                            @if ( $phonecard -> status !== 0)
                                <div class="text-xs text-secondary-600">Phí nạp: {{ (1 - $phonecard -> rate) * 100 }}%</div>
                            @endif
                        </div>
    
                        @if ( $phonecard -> status === 0)
                            <div class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 px-[8px] bg-neutral-300 rounded-full">
                                <p class="text-[8px] leading-[12px] font-medium text-neutral">Bảo trì</p>
                            </div>
                        @endif
                    </div>
                @endforeach
            </div>
        </div>
    
        <!-- Amount Selection -->
        <div class="flex flex-col gap-2">
            <div class="text-xs text-neutral-950">Mệnh giá thẻ</div>
            <input class="js-card-amount-withdraw-input absolute opacity-0" name="card_amount" type="number" value="{{ $validMoney ? $validMoney : '' }}">
    
            <div class="js-card-amount-withdraw-list grid grid-cols-3 gap-2 xl:grid-cols-5">
                @foreach(($listCard -> value_txt ?? []) as $card)
                    <button 
                        type="button" 
                        @class([
                            'js-card-amount-withdraw-item pt-1.5 xl:pt-0 relative h-[48px] border border-neutral-150 bg-card-amount rounded-lg overflow-hidden [&.active]:border-info-500 [&.active_.card-label-amount]:text-secondary-500 [&.active]:bg-card-amount-active xl:h-[60px]',
                            'active' => isset($depositData['defaultMoney']) && $card -> key === $depositData['defaultMoney']
                        ])
                        data-value="{{ $card->key }}"
                    >
                        <div class="card-label-amount text-[12px] leading-[16px] text-neutral-1000 font-medium xl:text-[14px] xl:leading-[20px]">{{ $card->label }}</div>
                        <div class="card-label-receive text-neutral-800 text-xs">Nhận {{ number_format($card -> key *$depositData['networks']['VIETTEL'] -> rate) }}</div>
                        @if ($card -> key === 100000)
                            <div class="absolute top-0 right-0">
                                <x-accounts::ui.account.label-amount type="proposal"></x-accounts::ui.account.label-amount>
                            </div>
                        @endif
                    </button>
                @endforeach
            </div>
    
        </div>
        <!-- Input Fields -->
        <div class="grid grid-cols-1 gap-[20px] xl:grid-cols-2">
            <x-kit.input 
                class="js-card-serial-normal"
                label=__('account.so_serial_1') 
                placeholder=__('account.nhap_so_serial_1')
                type="tel"
                name="card_serial"
                isPaste
                isRequire
                maxlength="20"
                oninput="formatNumber(this)"
                isPasteNumber
                noBackground
            >
            </x-kit.input>
                <x-kit.input 
                class="js-card-serial-zing hidden"
                label=__('account.so_serial_1') 
                placeholder=__('account.nhap_so_serial_1')
                type="tel"
                name="card_serial_zing"
                isPaste
                isRequire
                maxlength="20"
                oninput="formatLetterNumber(this)"
                isPasteWithoutCharacter
                noBackground
            >
            </x-kit.input>
            <x-kit.input 
                label=__('account.ma_the_pin') 
                placeholder=__('account.nhap_ma_the_pin')
                type="tel"
                name="card_code" 
                isPaste
                isRequire
                maxlength="20"
                oninput="formatNumber(this)"
                isPasteNumber
                noBackground
            >
            </x-kit.input>
        </div>
    
        @if(session('error'))
            <div class="text-red-500">{{ session('error') }}</div>
        @endif
    
        <x-kit.button 
            buttonType="submit"
            class="button-submit js-submit-withdraw-card w-full mx-auto xl:w-[220px]"
            disabled
        >
            Nạp Tiền
        </x-kit.button>
    </form>
    <x-accounts::ui.account.bank-account.guide-card :contentClass="$depositData['isDeposit'] > 0 ? 'hidden' : ''"/>
</div>

@pushOnce('scripts')
    <script>
        window.labelAmount = `<div class="absolute top-0 right-0"><x-accounts::ui.account.label-amount :type="'proposal'"></x-accounts::ui.account.label-amount></div>`;
    </script>

    @vite(['resources/js/deposit/card.js'])
@endpushOnce
