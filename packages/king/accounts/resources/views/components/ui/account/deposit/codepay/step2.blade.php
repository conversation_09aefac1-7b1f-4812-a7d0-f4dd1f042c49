<x-accounts::ui.account.deposit.ticket>
    <div class="relative">
        @slot('qr_slot')
            <!-- Left Section - QR Code -->
            <div class="flex flex-col p-3 xl:pr-[20px] xl:pl-[28px] xl:py-3.5 pb-0">
                <div class="flex w-full justify-end xl:justify-center">
                    <div class="text-xs text-neutral-800 mb-1.5 font-normal pr-2 xl:pr-0">
                        Phiếu <span>#{{ $depositData['nicepayData']->invoice_id ?? '' }}</span>
                    </div>
                </div>
                <div class="flex justify-center">
                    <div class="w-[8.25rem] xl:[8.5rem] aspect-square mb-2 relative">
                        <img src="{{ $depositData['nicepayData']->qrcode ?? '' }}" alt="QR Code"
                            class="w-full aspect-square rounded-[10px] overflow-hidden">
                        <img
                            src="{{ asset('vendor/accounts/images/account/banks-logo/'. strtolower($depositData['nicepayData']->bank_name) .".svg") }}"
                            alt="bank"
                            class="absolute cursor-pointer top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 h-7 w-7 xl:h-9 xl:w-9 rounded-full"
                            onerror="this.src='{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}';"
                            >
                        <div onclick="downloadQrCode( '{{ $depositData['nicepayData']->qrcode ?? '' }}')"
                            href="{{ $depositData['nicepayData']->qrcode ?? '' }}"
                            class="absolute cursor-pointer -right-6 top-5 bg-neutral h-6 w-6 min-w-6 aspect-square flex items-center justify-center rounded-r-lg">
                            <img src="{{ asset('asset/icons/download.svg') }}" alt="download" class="h-6 aspect-auto">
                        </div>
                    </div>
                </div>
                <div class="flex justify-between items-center xl:justify-center">
                    <!-- Bank -->
                    <div class="h-full xl:hidden flex items-center gap-1">
                        <img src="{{ asset('vendor/accounts/images/account/banks-logo/'. strtolower($depositData['nicepayData']->bank_name) .".svg") }}" alt="{{ $depositData['nicepayData']->bank_name ?? '' }}"
                        class="h-[16px] aspect-square xl:hidden" onerror="this.src='{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}';" />
                        <p class="text-xs font-medium text-black-800">
                            {{ $depositData['nicepayData']->bank_name ?? '' }}</p>
                    </div>

                    <div class="flex justify-between gap-2">
                        <img src="{{ asset('vendor/accounts/images/account/deposit/codepay/viet-qr.png') }}" alt="Viet QR"
                            class="h-3 xl:h-3.5 w-auto">
                        <img src="{{ asset('vendor/accounts/images/account/deposit/codepay/napas.png') }}" alt="Napas"
                        class="h-3 xl:h-3.5 w-auto">
                        <img src="{{ asset('vendor/accounts/images/account/deposit/codepay/vnpay.png') }}" alt="VNpay"
                        class="h-3 xl:h-3.5 w-auto">
                    </div>
                </div>
            </div>
        @endslot

        @slot('content_slot')
            <!-- Right Section - Payment Details -->
            <div class="flex-1 pt-0 px-3 xl:px-0 xl:py-[25px] xl:pr-4">
                <!-- Timer -->
                <div
                    class="js-countdown-timer-codepay mb-[6px] xl:relative xl:left-0 absolute xl:block xl:top-0 top-3 left-3 pl-2 xl:pl-0">
                    <div class="js-countdown-progress-codepay flex items-center gap-1">
                        <img src="{{ asset('asset/icons/account/deposit/codepay/clock.svg') }}" alt="countdown"
                            class="xl:h-6 h-4 w-auto xl:rounded-lg rounded-[5.33px]">
                        <div class="js-countdown-timer codepay-countdown-timer text-secondary-400 text-xs leading-4 font-semibold">
                            00:00
                        </div>
                        <div class="js-codepay-deposit-status hidden xl:block codepay-deposit-status pending">
                            Thời gian còn lại
                        </div>
                    </div>
    
                </div>

                <!-- Payment Info -->
                <div>
                    <!-- Amount -->
                    <div class="flex justify-between items-center h-8 xl:h-[1.625rem] px-1.5">
                        <div class="flex">
                            <p class="text-black-600 text-xs font-normal w-[120px]">Số tiền:</p>
                            <p class="text-xs font-medium text-black-800">
                                {{ number_format($depositData['nicepayData']->amount ?? 0) }} VND</p>
                        </div>
                        <button type="button" x-on:click="copyText($el.dataset.text, $el.dataset.field)"
                            data-field="amount" data-text="{{ number_format($depositData['nicepayData']->amount ?? 0) }}"
                            class="js-copy-btn hover:bg-gray-100">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-4 w-auto copy-icon js-copy-icon">
                        </button>
                    </div>

                    <!-- Bank -->
                    <div class="xl:flex hidden justify-between items-center h-8 xl:h-[1.625rem] px-1.5">
                        <div class="flex">
                            <p class="text-black-600 text-xs font-normal w-[120px]">Ngân hàng:</p>
                            <div class="flex items-center gap-1">
                                <p class="text-xs font-medium text-black-800">
                                    {{ $depositData['nicepayData']->bank_name ?? '' }}</p>
                                    <img src="{{ asset('vendor/accounts/images/account/banks-logo/' . strtolower($depositData['nicepayData']->bank_name) . ".svg") }}" alt="'{{ $depositData['nicepayData']->bank_name ?? '' }}'"
                                    onerror="this.src='{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}';"
                            class="h-[14px] w-auto hidden xl:block">
                            </div>
                        </div>
                    </div>

                    <!-- Account Number -->
                    <div class="flex justify-between items-center h-8 xl:h-[1.625rem] px-1.5">
                        <div class="flex">
                            <p class="text-black-600 text-xs font-normal w-[120px]">Số tài khoản:</p>
                            <p class="text-xs font-medium text-black-800">
                                {{ $depositData['nicepayData']->bank_account_no ?? '' }}</p>
                        </div>
                        <button type="button" x-on:click="copyText($el.dataset.text, $el.dataset.field)"
                            data-field="accountNumber" data-text="{{ $depositData['nicepayData']->bank_account_no ?? '' }}"
                            class="js-copy-btn hover:bg-gray-100 rounded text-[20px]">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-4 w-auto copy-icon js-copy-icon">
                        </button>
                    </div>

                    <!-- Account Name -->
                    <div class="flex justify-between items-center h-8 xl:h-[1.625rem] px-1.5">
                        <div class="flex">
                            <p class="text-black-600 text-xs font-normal w-[120px]">Chủ tài khoản:</p>
                            <p class="text-xs font-medium text-black-800">
                                {{ $depositData['nicepayData']->bank_account_name ?? '' }}</p>
                        </div>
                        <button type="button" x-on:click="copyText($el.dataset.text, $el.dataset.field)"
                            data-field="accountName" data-text="{{ $depositData['nicepayData']->bank_account_name ?? '' }}"
                            class="js-copy-btn hover:bg-gray-100 rounded text-[20px]">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-4 w-auto copy-icon js-copy-icon">
                        </button>
                    </div>

                    <!-- Content -->
                    <div class="flex justify-between items-center h-8 xl:h-[1.625rem] px-1.5 bg-secondary-90 rounded-md">
                        <div class="flex">
                            <p class="text-black-600 text-xs font-normal w-[120px]">Nội dung:</p>
                            <p class="text-xs font-semibold text-secondary-500">
                                {{ $depositData['nicepayData']->code ?? ($depositData['nicepayData']->content ?? '') }}
                            </p>
                        </div>
                        <button type="button" x-on:click="copyText($el.dataset.text, $el.dataset.field)"
                            data-field="content"
                            data-text="{{ $depositData['nicepayData']->code ?? ($depositData['nicepayData']->content ?? '') }}"
                            class="js-copy-btn hover:bg-gray-100 rounded text-[20px]">
                            <img src="{{ asset('asset/icons/account/deposit/codepay/copy.svg') }}" alt="copy icon"
                            class="h-4 w-auto copy-icon js-copy-icon">
                        </button>

                    </div>
                </div>
            </div>
        @endslot
    </div>
</x-accounts::ui.account.deposit.ticket>

<ul class="list-disc px-4 mb-6 mt-[10px]">
    <li class="italic text-xs text-secondary-500">
        Nạp bằng tài khoản chính chủ.
    </li>
    <li class="italic text-xs text-secondary-500">
        Giao dịch sẽ bị hủy nếu nhập sai thông tin chuyển tiền.
    </li>
</ul>

<div class="flex justify-center">
    <x-kit.button button-type="button" onclick="handleConfirmDeposit()"  class="js-qr-button w-full xl:w-[220px] capitalize">
        Tạo Mã QR Mới<span class="js-remaining-time"></span>
    </x-kit.button>
</div>
