<div class="dropdown-bank relative">
    <div id="overlay-dropdown-bank" class="fixed hidden xl:!hidden top-0 left-0 right-0 bottom-0 bg-black-50 h-full z-20 w-full"></div>
    <p class="text-[12px] leading-[18px] text-neutral-1000 mb-1">{{ __('account.bank_management.bank') }}</p>
    <div class="flex cursor-pointer relative h-10 w-full rounded-lg pl-3 pr-10 border border-solid"
    id="dropdown-bank-header">
        <div id="dropdown-header" class="flex gap-x-3 items-center">
            <img src="{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}" alt="icon"
                class="w-6 h-6 object-contain" id="bank-logo-header" data-placeholder="{{ asset('vendor/accounts/images/account/banks-logo/bank-logo-default.svg') }}">
            <p class="font-normal text-sm text-neutral-1000">{{ __('account.dropdown_bank.default_txt') }}</p>
        </div>
        <div class="absolute right-3 top-2/4 -translate-y-2/4">
            <img src="{{ asset('asset/images/arrow-down.png') }}" alt="arrow-down" id="arrow-dropdown-bank"
                class="w-5 h-5 object-contain transition-all duration-200">
        </div>
    </div>
    <div id="content-dropdown"
        class="max-h-dvh pt-[12px] pb-[10px] px-[16px] bg-neutral hidden fixed bottom-0 [&:has(input:focus)]:min-h-[394px] [&:has(input:focus)]:xl:min-h-fit left-0 right-0 z-20 xl:min-h-fit xl:bottom-auto xl:absolute xl:left-0 xl:top-full xl:p-[10px] mt-1 [box-shadow:0px_4px_10.8px_0px_#48484D26] rounded-t-3xl xl:rounded-lg w-full xl:z-[5]">
        <div class="xl:hidden flex justify-between items-center">
            <div class="flex items-center gap-2">
                <img src="{{ asset('vendor/accounts/images/account/bank/bank-active.svg') }}" alt="icon bank"
                class="w-8 h-8 object-contain" >
                <p class="text-sm font-medium text-neutral-1000">Chọn Ngân Hàng</p>
            </div>
            <button id="js-close-dropdown-bank" class="w-8 h-8 bg-neutral-100 rounded-md flex justify-center items-center">
                <img alt="close" src="{{ asset('asset/images/close-blue.webp') }}" class="w-2 h-2 object-cover"/>
            </button>
        </div>
        <div class="mt-[22px] mb-[10px] xl:mt-0">
            <x-kit.search-input placeholder="Tìm kiếm ngân hàng" closeClass="absolute right-6 xl:static" class="dropdown-search-mobile bg-neutral border-neutral-150 border-solid [&>i]:!text-secondary-500" size="medium" showClose
                id="bank-search-input"></x-kit.search-input>
        </div>
        <div id="no-results">
            <p class="text-center text-sm text-neutral-400 py-10 hidden">
                {{ __('account.dropdown_bank.no_data') }}
            </p>
        </div>
        <div id="bank-list" class="grid grid-cols-2 gap-[8px] max-h-[272px] xl:max-h-[12rem] xl:gap-0 overflow-y-auto">
            @foreach ($listBanks as $bank)
                <div id="dropdown-item-bank"
                    class="cursor-pointer xl:hover:bg-neutral-50 rounded-lg flex items-center w-full gap-x-2 h-[3rem] p-3"
                    onclick="handleSelectedBank({{ json_encode($bank) }})">
                    <img src="{{ asset('vendor/accounts/images/account/banks-logo/' . strtolower($bank->bank_code ?? '') . '.svg') }}"
                        alt="logo" class="w-6 h-auto aspect-square"/>
                    <span id="bank-name" class="text-xs text-neutral-1000">{{ $bank->bank_name }}</span>
                </div>
            @endforeach
        </div>
    </div>
</div>
