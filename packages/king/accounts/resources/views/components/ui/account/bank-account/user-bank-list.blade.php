<div class="grid grid-cols-2 gap-[10px]">
    @foreach (array_reverse($userBanks) as $index => $userBank)
        <div @class([
            "relative bg-neutral-50 rounded-xl py-2 xl:py-2.5 px-3 xl:px-4 box-border flex items-center gap-2 overflow-hidden xl:gap-[14px]",
            "border border-info-200 !bg-neutral" => $index === 0
        ])>
            <img src="{{ asset('vendor/accounts/images/account/banks-logo/' . strtolower($userBank->bank_code ?? '') . '.svg') }}"
                alt="{{ $userBank->bank_name ?? '' }}" class="h-[1.75rem] xl:h-8 w-auto aspect-square" />
            <div>
                <p class="text-[12px] leading-[18px] text-neutral-800 xl:text-base font-medium">{{ $userBank->bank_name ?? '' }}</p>
                <p class="text-[12px] leading-[18px] text-secondary-500 xl:text-base font-medium">{{ $userBank->bank_account_no ?? '' }}</p>
                <p @class([
                    'text-[10px] leading-[14px]',
                    'text-warning-600' => $userBank->bank_status == 1,
                    'text-success-600' => $userBank->bank_status != 1,
                ])>{{ $userBank->bank_txt }}</p>
            </div>
            @if ($index === 0)
                <div class="absolute top-0 right-0">
                    <x-accounts::ui.account.label-amount type="recent" class="w-[48px]"></x-accounts::ui.account.label-amount>
                </div>
            @endif
        </div>
    @endforeach
</div>
