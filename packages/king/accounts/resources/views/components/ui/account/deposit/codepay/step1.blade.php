@php
    $depositConfig = config('account.deposit');
    $isShowPromotionContent = isset($depositData['packages']) && is_array($depositData['packages']) && count($depositData['packages']) === 1 && $depositData['packages'][0]->id === 2;
@endphp

 <!-- Amount Input Section -->
 <div class="mb-6">
     <div class="relative mb-2 amount-input-wrapper">
         <x-kit.input isRequire maxlength='7' type="tel" placeholder=__('account.nhap_so_tien_nap') rightText="= 0 VND"
            id="amount-input" label=__('account.so_tien_nap') name="amount"
            data-default="{{ isset($depositData['defaultMoney']) ? $depositData['defaultMoney'] : '' }}"
            class="[&_.input-field-wrap]:grid [&_.input-field-wrap]:grid-cols-[auto_auto] [&_div]:bg-neutral [&_div]:border-neutral-150 text-black-350" />
     </div>
     <!-- Quick Amount Buttons -->
     <div class="grid grid-cols-4 xl:grid-cols-6 gap-2">
         @foreach ([100, 200, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000] as $amount)
             <button type="button" data-amount="{{ number_format($amount) }}" @class([
                 'amount-btn custom-amount-btn relative bg-[radial-gradient(99.3%_105.95%_at_49.63%_0%,_#FFFFFF_0%,_#F1F3F6_100%)]',
                 '!border-info-500 !text-secondary-500 active' => isset($depositData['defaultMoney']) ? $depositData['defaultMoney'] === $amount : $amount == 500,
             ])>
                 @if ($amount == 500)
                     <div
                         class="absolute top-0 right-0 h-[11px] w-[46px] flex justify-center items-center bg-warning-700 rounded-bl-[8px]">
                         <span class="font-medium text-[8px] text-neutral">Đề xuất</span>
                     </div>
                 @endif
                 <span>{{ number_format($amount) }} K</span>
             </button>
         @endforeach
     </div>
 </div>

 <!-- Promotions Section -->
 <div class="mb-6">
    @if (isset($depositData['packages']) && is_array($depositData['packages']) && count($depositData['packages']) > 1)
        <div class="flex justify-between items-center mb-3">
            <h2 class="text-xs text-black-1000">Chọn khuyến mãi</h2>

            <div class="xl:hidden gap-2 hidden">
                <button
                    class="promotion-prev w-8 aspect-square text-neutral-600 border rounded-lg flex items-center justify-center">
                    <i class="icon-arrow-left-fill "></i>
                </button>
                <button
                    class="promotion-next w-8 aspect-square text-neutral-600 border rounded-lg flex items-center justify-center">
                    <i class="icon-arrow-right-fill"></i>
                </button>
            </div>
        </div>
     @endif
     <!-- Pc Promotions Swiper Section -->
     <div class="promotions-group js-promotion-pc">
         <div class="grid grid-cols-2 gap-2 xl:gap-4">
             @if (isset($depositData['packages']) && is_array($depositData['packages']) && count($depositData['packages']) > 0)
                @if (count($depositData['packages']) > 1)
                    @foreach ($depositData['packages'] as $key => $package)
                        @if ($package->id === 2 || $package->id === 1)
                            <div class="swiper-slide">
                                <label class="promotions-item relative px-[25px] overflow-hidden">
                                    <img src={{ asset('vendor/accounts/images/account/deposit/promo-card.webp') }} alt="promo-card"
                                        class="absolute promo-image top-0 left-0 bottom-0">
                                    <input type="radio" name="packageId" value="{{ $package->id }}" class="hidden" data-multiplier="{{ $package -> multiplier }}" data-promotion="{{ $package -> promotion }}" data-max="{{ $package -> max_amount }}">
                                    <img src={{ asset('asset/icons/account/deposit/codepay/checked.svg') }} alt="checked icon"
                                        class="absolute checked-icon opacity-0 right-0 -top-[1px] w-4 h-auto aspect-square">

                                    <div class="flex flex-col items-center gap-0.5">
                                        <div class="promotions-label text-[10px] xxs:text-xs xxs:leading-[1.125rem] font-medium text-neutral-800 uppercase text-center line-clamp-2">
                                            {{ $package->description ?? '' }}
                                        </div>
                                    </div>
                                </label>
                            </div>
                        @endif
                    @endforeach
                 @else
                    <input type="hidden" name="packageId" value="{{ $depositData['packages'][0]->id }}" class="promotion-package hidden" data-multiplier="{{ $depositData['packages'][0] -> multiplier }}" data-promotion="{{ $depositData['packages'][0] -> promotion }}" data-max="{{ $depositData['packages'][0] -> max_amount }}">
                 @endif
             @endif
         </div>
     </div>
     <div class="promotion-content rounded-lg bg-neutral-250 overflow-hidden p-2 mt-1 {{ $isShowPromotionContent ? 'block' : 'hidden' }}">
         <div class="flex justify-between items-center px-2 py-1.5 xl:border-b-[1px] border-neutral-100 last:border-none">
            <p class="text-xs text-neutral-950">Khuyến mãi</p>
            <p class="promotion-amount text-xs font-medium text-secondary-500"></p>
        </div>
        <div class="flex justify-between items-center px-2 py-1.5 xl:border-b-[1px] border-neutral-100 last:border-none">
            <p class="text-xs text-neutral-950">Thực nhận</p>
            <p class="promotion-money text-xs font-medium text-secondary-500"></p>
        </div>
        <div class="flex justify-between items-center px-2 py-1.5 xl:border-b-[1px] border-neutral-100 last:border-none">
            <p class="text-xs text-neutral-950">Số vòng cược</p>
            <p class="promotion-multiplier text-xs font-medium text-secondary-500"></p>
        </div>
        <div class="flex justify-between items-center px-2 py-1.5 xl:border-b-[1px] border-neutral-100 last:border-none">
            <p class="text-xs text-neutral-950">Tiền cược yêu cầu</p>
            <p class="promotion-bet text-xs font-medium text-secondary-500"></p>
        </div>
     </div>
 </div>

 <div class="flex justify-center">
     <x-kit.button button-type="submit" id="js-create-qr-btn" class="xl:w-[220px] w-full capitalize">
        Tạo mã QR
     </x-kit.button>
 </div>
