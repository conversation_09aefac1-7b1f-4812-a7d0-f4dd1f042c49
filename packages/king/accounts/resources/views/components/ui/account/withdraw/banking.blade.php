@php
    if(isset($withdrawData['userBankInfo']) && $withdrawData['userBankInfo']){
        $defaultData =  $withdrawData['userBankInfo'];
    };

    $isFirst = isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) === 0;
    $bankStatus = !$isFirst ? $defaultData -> bank_status : 1;
@endphp

<div class="flex flex-col gap-[12px] xl:gap-[24px]">
    <div class="witdraw-banking w-full mx-auto py-[16px] px-[12px] bg-neutral rounded-[16px] xl:p-0">
        <form 
            id="withdraw_bank_form" 
            class="flex flex-col gap-[32px] w-full" 
            data-step="{{ isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) > 0 ? 2 : 1 }}"
        >
            <div class="flex flex-col gap-[20px] w-full xl:gap-[24px]">
                @if (isset($withdrawData['userBankList']) && count($withdrawData['userBankList']) === 0)
                    <div class="flex items-center gap-[8px]">
                        <img src="{{ asset('vendor/accounts/images/account/bank/bank.webp')}}" class="w-[32px] h-[32px]" alt="bank"/>
                        <p class="text-[14px] leading-[20px] font-medium text-neutral-1000">Thiết lập tài khoản Ngân hàng</p>
                    </div>
                @endif
                <div class="flex flex-col gap-[4px]">
                    <x-accounts::ui.common.dropdown-bank label=__('account.ngan_hang_9') defaultValue="{{ !$isFirst ? $defaultData -> value : ''}}" isRequire name="to_bank_code" :subOptions="$withdrawData['bankList']" :options="$withdrawData['userBankList']" placeholder=__('account.chon_ngan_hang_cua_ban_1')></x-accounts::ui.common.dropdown-bank>
                    <div 
                        @class([
                            "witdraw-banking__info hidden grid-cols-[89px_1fr] gap-[4px] py-[8px] px-[16px] rounded-[8px] [&.is-hidden]:grid bg-bank-account xl:grid-cols-[120px_1fr]",
                            "is-hidden" => !$isFirst
                        ])
                    >
                        <p class="text-[12px] leading-[18px] text-neutral-800">Chủ tài khoản:</p>
                        <p class="withdraw-name-text text-[12px] leading-[18px] text-neutral-1000">@if(isset($defaultData) && $defaultData) {{ $defaultData -> bank_account_name }} @endif</p>
                        <p class="text-[12px] leading-[18px] text-neutral-800">Số tài khoản:</p>
                        <p class="withdraw-no-text text-[12px] leading-[18px] text-secondary-500">@if(isset($defaultData) && $defaultData) {{ $defaultData -> bank_account_no }} @endif</p>
                    </div>
                </div>

                <div 
                    @class([
                        "witdraw-banking__info-group grid grid-cols-1 gap-[20px] w-full [&.is-hidden]:hidden xl:flex xl:flex-nowrap",
                        "is-hidden" => !$isFirst
                    ])
                >
                    <x-kit.input isPasteNumber type="tel" maxlength="16" oninput="formatNumber(this)" noBackground value="{{ !$isFirst ? $defaultData -> bank_account_no : '' }}" class="withdraw-no xl:w-full" name="to_bank_no" isRequire label=__('account.so_tai_khoan_2') placeholder=__('account.nhap_so_tai_khoan_2')></x-kit.input>
                    @if (!isset($withdrawData['userBankList']) || count($withdrawData['userBankList']) <= 0)
                        <x-kit.input isPasteLetter maxlength="50" onkeypress="return (event.charCode > 64 && event.charCode < 91) || (event.charCode > 96 && event.charCode < 123) || (event.charCode == 32)" onfocusout="formatNameUppercase(this)" noBackground value="{{ !$isFirst ? $defaultData -> bank_account_name : '' }}" class="withdraw-name xl:w-full [&_input]:uppercase" isRequire name="to_bank_name" label=__('account.ten_tai_khoan') placeholder=__('account.nhap_ten_tai_khoan_3')></x-kit.input>
                    @else
                        <input type="hidden" name="to_bank_name" value="{{ !$isFirst ? $defaultData -> bank_account_name : '' }}">
                    @endif
                </div>
                <div class="flex flex-col gap-[8px]">
                    <x-kit.input 
                        oninput="formatAmountWithdraw(this, 7)"
                        maxlength="9" 
                        class="withdraw-amount [&_.input-field-wrap]:grid [&_.input-field-wrap]:grid-cols-[auto_auto]" 
                        name="amount_withdraw" 
                        rightText='= 0 VND' 
                        isRequire 
                        type="tel" 
                        label=__('account.so_tien_rut_1') 
                        placeholder=__('account.nhap_so_tien_rut_1')
                        noBackground
                    >
                    </x-kit.input>
                    <div class="amount-group-bank grid grid-cols-4 gap-[8px]">
                        @foreach ([100, 500, 1000, 2000, 5000, 10000, 20000, 50000, 100000, 200000, 500000] as $amount)
                            <button type="button"
                                value="{{ $amount }}"
                                class="amount-button relative flex justify-center items-center h-[48px] border border-neutral-150 bg-card-amount rounded-[8px] overflow-hidden [&.selected]:border-info-500 [&.selected_.card-label-amount]:text-secondary-500 [&.selected]:bg-card-amount-active xl:h-[42px]"
                            >
                                @if ($amount === 500)
                                    <div class="absolute top-0 right-0">
                                        <x-accounts::ui.account.label-amount type="proposal"></x-accounts::ui.account.label-amount>
                                    </div>
                                @endif
                                <span class="amount-label text-[12px] leading-[18px] text-neutral-1000 font-medium xl:text-[14px] xl:leading-[20px]">{{ number_format($amount) }} K</span>
                            </button>
                        @endforeach
                    </div>
                </div>
                <x-kit.input
                    oninput="formatNumber(this)" 
                    maxlength="5" 
                    class="withdraw-phone {{ $bankStatus === 1 ? '' : 'hidden'}}" 
                    name="phone" 
                    isRequire 
                    type="tel" 
                    label=__('common.so_dien_thoai_1') 
                    placeholder=__('account.nhap_5_so_cuoi_dien_thoai_2')
                    noBackground
                >
                </x-kit.input>
                <x-kit.button disabled buttonType="submit" class="button-submit w-full mx-auto capitalize xl:max-w-[220px]">Rút tiền</x-kit.button>
            </div>
        </form>
    </div>
    <x-accounts::ui.account.withdraw.guide-banking :contentClass="$withdrawData['isWithdraw'] ? 'hidden' : ''"/>
</div>

@pushOnce('scripts')
    @vite(['resources/js/withdraw/bank.js'])
    @vite(['resources/js/dropdown-bank.js'])

    <script>
        window.addEventListener('DOMContentLoaded', () => {
            const witdrawBanking = $('.witdraw-banking');
            const step = witdrawBanking.attr('step');

            if (Number(step) === 2) {
                const addBankModal = `<x-ui.modal size="lg" :id="'add-bank-modal'"><x-accounts::ui.account.add-bank isFromModal :withdrawData="$withdrawData"></x-accounts::ui.account.add-bank></x-ui.modal>`
                
                openModal(addBankModal, false, 'add-bank-modal', true);
            }
            
            handleDropdown();
            handleAddBank();
            handleDropdownBank();
        })
    </script>
@endpushOnce
