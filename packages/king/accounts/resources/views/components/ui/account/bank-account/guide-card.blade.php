@props([
    "class" => "",
    "contentClass" => ""
])

<x-kit.collapse :$contentClass class="{{ $class }}" id="guide-withdraw" title="{{ __('account.guide_deposit.title') }}" icon="{{ 'asset/icons/docs.svg' }}">
    <div class="flex flex-col gap-y-2 mb-[0.375rem]">
        <div class="item-guide px-4 py-3 bg-neutral-50 rounded-lg">
            <ul class="flex flex-col gap-y-1 [&_li]:leading-[18px] list-disc [&_li]:max-w-[calc(100%-5px)] [&_li_p]:translate-x-[-5px] [&_li::marker]:text-neutral-800 pl-[25px]">
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_deposit.card.text_1') }}</p>
                </li>
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_deposit.card.text_2') }}</p>
                </li>
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_deposit.card.text_3') }}</p>
                </li>
            </ul>
        </div>
    </div>
</x-kit.collapse>
