@php
    $step = isset($depositData['nicepayData']) && $depositData['nicepayData'] ? 2 : 1;
    $instruction = __('deposit.codepay.instruction');
@endphp
<section>
    <form method="POST" action="{{ $step == 1 ? route('en.deposit.codepay') : route('en.deposit.codepay.cancel') }}"
        id="deposit-codepay-form" class="w-full mx-auto bg-neutral p-3 xl:pb-0 xl:px-0 pt-0 rounded-2xl overflow-hidden">
        @csrf
        <!-- Progress Steps -->
        <div class="flex items-center justify-between mb-11 max-w-[32.25rem] mx-auto gap-[6px] px-6 pt-3 xl:pt-0">
            <div class="relative flex items-center flex-col gap-3">
                <span class='absolute -bottom-5 text-xs font-normal italic text-nowrap text-black-800 min-w-12 text-center'>N<PERSON><PERSON> tiền</span>
                <div @class([
                    'relative w-4 h-4 box-border rounded-full',
                    'border-[6px] border-secondary-500' => $step < 2,
                ])>
                    @if ($step >= 2)
                        <img src={{ asset('asset/icons/checked.svg') }} alt="checked" class="absolute top-0 left-0 w-4">
                    @endif
                </div>
            </div>
            <div @class([
                'flex-1 border-b',
                'border-dashed border-secondary-400' => $step == 1,
                'border-secondary-400' => $step >= 2,
            ])></div>

            <div class="relative flex items-center flex-col gap-3">
                <span @class([
                    'absolute -bottom-5 text-xs font-normal italic text-nowrap text-black-400 min-w-[68px]',
                    '!text-black-800' => $step >= 2,
                ])>Thanh toán</span>
                <div @class([
                    'relative js-step-codepay-two w-4 h-4 box-border rounded-full',
                    'border-[2px]' => $step < 2,
                    'border-[6px] border-secondary-500' => $step == 2,
                ])>
                    <img src={{ asset('asset/icons/checked.svg') }} alt="checked"
                        class="js-check-codepay-two hidden absolute top-0 left-0 w-4">
                </div>
            </div>
            <div @class([
                'js-progress-step-three flex-1 border-b',
                'border-dashed' => $step < 2,
                'border-dashed border-secondary-400' => $step == 2,
                'border-secondary-400' => $step > 2,
            ])></div>
            <div class="relative flex items-center flex-col gap-3">
                <span @class([
                    'absolute -bottom-5 text-xs font-normal italic text-nowrap text-black-400 min-w-[68px]',
                    '!text-black-800' => $step > 2,
                ])>Hoàn thành</span>
                <div @class([
                    'w-4 h-4 js-step-codepay-three box-border rounded-full ',
                    'border-[2px]' => $step <= 2,
                    'border-[6px] border-secondary-500' => $step > 2,
                ])>
                </div>
            </div>
        </div>
        @if ($step == 1)
            <x-accounts::ui.account.deposit.codepay.step1 :depositData="$depositData" />
        @else
            <x-accounts::ui.account.deposit.codepay.step2 :depositData="$depositData" />
        @endif
    </form>

    <!-- instruction -->
    <div class="mt-3 xl:mt-6">
        <x-accounts::ui.account.deposit.guide-deposit :contentClass="$depositData['isDeposit'] > 0 ? 'hidden' : ''"></x-accounts::ui.account.deposit.guide-deposit>
    </div>
</section>

@pushOnce('scripts')
    <script>
        const submitCancelForm = () => document.forms["deposit-codepay-form"].submit();
        
        const handleConfirmDeposit = () => {
            const nicepayData = @json($depositData['nicepayData'] ?? null);
            openNotiModal('Huỷ giao dịch?', `Xác nhận tạo phiếu nạp mới. <br /> Phiếu #${nicepayData.id} sẽ bị huỷ`, 'Xác nhận', 'Từ chối', '/asset/images/popup/img-cancel-transaction.webp',
                async () => {
                    const res = await fetchData('/account/deposit/codepay/cancel', {
                        nicepay_id: nicepayData.id,
                    }, { ajax: true }, '', '');
                    if (res.status === 'OK') {
                        window.location.reload();
                    }
                } ,
                () => {},
                '',
                false,
                () => {},
                false,
                true
            )          
        };
        // Define the data first
        window.codepayConfig = {
            nicepayData: @json($depositData['nicepayData'] ?? null),
            step: @json($step),
            expiryTimeUtc: @json($depositData['nicepayData']->expired_at_utc ?? null),
            createdTime: @json($depositData['nicepayData']->expired_countdown_time ?? null)
        };
        
    </script>
    @vite(['resources/js/deposit/codepay.js'])
@endpushOnce
