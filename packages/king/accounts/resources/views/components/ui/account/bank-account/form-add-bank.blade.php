@props(['userBanks', 'listBanks'])
<div class="form-bank">
    <form id="add-bank" class="flex flex-col">
        <div class="mb-[16px]">
            <x-accounts::ui.account.bank-account.dropdown-bank :listBanks="$listBanks" />
            <input type="hidden" id="selected-bank" name="selected_bank" value="">
            <p id="bank-error" class="text-sm text-danger-600 hidden"><PERSON><PERSON> lòng chọn ngân hàng.</p>
        </div>
        <div class="flex flex-col justify-between items-start gap-[16px] mb-[16px] xl:flex-row">
            <div class="relative w-full">
                <x-kit.input label='Số tài khoản' name="bank_account_no" isRequire type="tel" placeholder="Nhập số tài khoản"
                    id="bank_account_no" classNameInput="w-full"
                    class="[&_div]:bg-neutral [&_div]:border-neutral-200" maxlength="20">
                </x-kit.input>
                <p id="account-no-error" class="text-sm text-danger-600 hidden"></p>
            </div>
            @if (!isset($userBanks[0]->bank_account_name))
                <div class="relative w-full">
                    <x-kit.input label='Chủ tài khoản' isRequire name="bank_account_name" placeholder="Nhập tên tài khoản"
                        id="bank_account_name" class="[&_div]:bg-neutral [&_div]:border-neutral-200"
                        maxlength="60">
                    </x-kit.input>
                    <p id="account-name-error" class="text-sm text-danger-600 hidden"></p>
                </div>
            @endif
        </div>
        <x-kit.button buttonType="submit" id="submit-btn"
            class="flex w-full xl:w-[220px] mx-auto h-10 capitalize disabled:opacity-50 disabled:cursor-not-allowed"
            disabled>
            {{ __('account.dropdown_bank.btn_add_bank') }}
        </x-kit.button>
    </form>
</div>

@pushOnce('scripts')
    @vite(['resources/js/account/bank-management.js'])
@endpushOnce
