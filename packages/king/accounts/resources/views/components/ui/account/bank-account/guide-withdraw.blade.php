@props([
    "class" => "",
    "isClose" => false,
    "title" => __('account.guide_withdraw.title')
])

<x-kit.collapse :contentClass="$isClose ? 'hidden' : ''" class="{{ $class }}" id="guide-withdraw" :title="$title" icon="{{ 'asset/icons/docs.svg' }}">
    <div class="flex flex-col gap-y-2 mb-[0.375rem]">
        <div class="item-guide px-4 py-3 bg-neutral-50 rounded-lg">
            <div class="font-bold text-[10px] leading-[calc(14/10)] mb-2 text-secondary-500 uppercase">
                {{ __('account.guide_withdraw.noted.title') }}
            </div>
            <ul class="flex flex-col gap-y-1 [&_li]:leading-[18px] list-disc [&_li]:max-w-[calc(100%-5px)] [&_li_p]:translate-x-[-5px] [&_li::marker]:text-neutral-800 pl-[25px]">
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.noted.text_1') }}</p>
                </li>
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.noted.text_2') }}</p>
                </li>
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.noted.text_3') }}</p>
                </li>
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.noted.text_4') }}</p>
                </li>
            </ul>
        </div>
        <div class="item-guide px-4 py-3 bg-neutral-50 rounded-lg xl:min-h-[82px]">
            <div class="font-bold text-[10px] leading-[calc(14/10)] mb-2 text-secondary-500 uppercase">
                {{ __('account.guide_withdraw.qr_code.title') }}
            </div>
            <ul class="flex flex-col gap-y-1 [&_li]:leading-[18px] list-disc [&_li]:max-w-[calc(100%-5px)] [&_li_p]:translate-x-[-5px] [&_li::marker]:text-neutral-800 pl-[25px]">
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.qr_code.text_1') }}</p>
                </li>
            </ul>
        </div>
        <div class="item-guide px-4 py-3 bg-neutral-50 rounded-lg xl:min-h-[82px]">
            <div class="font-bold text-[10px] leading-[calc(14/10)] mb-2 text-secondary-500 uppercase">
                {{ __('account.guide_withdraw.account.title') }}
            </div>
            <ul class="flex flex-col gap-y-1 [&_li]:leading-[18px] list-disc [&_li]:max-w-[calc(100%-5px)] [&_li_p]:translate-x-[-5px] [&_li::marker]:text-neutral-800 pl-[25px]">
                <li class="relative">
                    <p class="text-xs text-neutral-800 font-normal leading-[18px]">{{ __('account.guide_withdraw.account.text_1') }}</p>
                </li>
            </ul>
        </div>
    </div>
</x-kit.collapse>
