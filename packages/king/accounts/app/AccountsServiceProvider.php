<?php

namespace King\Accounts;

use Illuminate\Contracts\Container\Container;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;

class AccountsServiceProvider extends ServiceProvider
{
    protected $app;
    public function __construct(Container $app)
    {
        $this->app = $app;
    }

    public function boot(Router $router)
    {
        $this->mergeConfigFrom(__DIR__ . '/../config/account.php', 'account');
        $this->mergeConfigFrom(__DIR__ . '/../config/package-promotion.php', 'package-promotion');

        $router->pushMiddlewareToGroup('web', \King\Accounts\Http\Middleware\CheckHistoryMiddleware::class);
        $router->pushMiddlewareToGroup('web', \King\Accounts\Http\Middleware\GatewayAuthMiddleware::class);

        // Load routes only if routes are not cached
        if (! $this->app->routesAreCached()) {
            $this->loadRoutesFrom(__DIR__ . '/../routes/web.php');
        }

        $this->loadViewsFrom(__DIR__ . '/../resources/views', 'accounts');

        Blade::componentNamespace('King\\Accounts\\View\\Components', 'accounts');

        $this->publishes([
            __DIR__ . '/../resources/views/components' => resource_path('views/vendor/accounts/components'),
        ], 'accounts-layouts');

        $this->publishes([
            __DIR__ . '/../resources/images' => public_path('vendor/accounts/images'),
        ], 'accounts-assets');
    }

    public function register()
    {
    }
}
