<?php

namespace King\Accounts\View\Components\ui\account\withdraw;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class card extends Component
{
    public $withdrawData;

    /**
     * Create a new component instance.
     */
    public function __construct($withdrawData)
    {
        $this->withdrawData = $withdrawData;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('accounts::components.ui.account.withdraw.card');
    }
}
