<?php

namespace King\Accounts\View\Components\ui\account\deposit;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class crypto extends Component
{
    public $depositData;

    /**
     * Create a new component instance.
     */
    public function __construct($depositData)
    {
        $this->depositData = $depositData;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('accounts::components.ui.account.deposit.crypto');
    }
}
