<?php

namespace King\Accounts\Services;

use App\Services\BaseService;
use App\Services\GatewayApi;
use Illuminate\Support\Facades\Cookie;
use King\Accounts\Enums\AccountEndpoint;
use Symfony\Component\HttpFoundation\Response;

class AccountService extends BaseService
{
    public const COOKIE_USER_TEXT = 'user';

    public const TIME_COOKIE = 1440;
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getListTransaction($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::LIST_TRANSACTION->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );
            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getListBet($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::LIST_BET->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );
            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getAccountInfo($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_INFO->value,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getAccountCommission($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_COMMISSION->value,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getAccountBet($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_BET->value,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getAccountVerificationStatus($request)
    {
        $verificationStatusDefault = (object)[
            'deposit' => false,
            'bank' => false,
            'tele' => false,
            'is_show_freespin' => false,
        ];
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::ACCOUNT_VERIFICATION_STATUS->value,
                cookies: ['lang' => $request->lang],
                type: GatewayApi::PROMOTION_PREFIX,
            );
            if (self::isSuccessResponse($response)) {
                $request->session()->put('verificationStatus', $response->data);
                return;
            }
            $request->session()->put('verificationStatus', $verificationStatusDefault);
            return;
        } catch (\Exception $e) {
            $request->session()->put('verificationStatus', $verificationStatusDefault);
            return;
        }
    }

    public function getSlotInfo($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::SLOT_INFO->value,
                cookies: ['lang' => $request->lang],
                type: GatewayApi::PROMOTION_PREFIX,
            );
            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getCasinoInfo($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: AccountEndpoint::CASINO_INFO->value,
                cookies: ['lang' => $request->lang],
                type: GatewayApi::PROMOTION_PREFIX,
            );
            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }


    public function changeInfo($request)
    {
        $dataRq = $request->all();
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: AccountEndpoint::UPDATE_INFO->value,
            data: $dataRq,
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );
        $cookie = Cookie::get(self::COOKIE_USER_TEXT);
        if ($cookie) {
            $cookie = json_decode($cookie);
            if ((isset($response->user->fullname) && $response->user->fullname) || (isset($request->fullname) && $request->fullname)) {
                $cookie->fullname = $response->user->fullname ?? $request->fullname;
                Cookie::queue(self::COOKIE_USER_TEXT, json_encode($cookie), self::TIME_COOKIE, null, null, false, false);
            }
        }
        return $response;
    }

}
