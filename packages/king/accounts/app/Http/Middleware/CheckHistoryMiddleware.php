<?php

namespace King\Accounts\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use King\Accounts\Services\AccountService;
use King\Accounts\Traits\TransactionFilterTrait;
use Symfony\Component\HttpFoundation\Response;

class CheckHistoryMiddleware
{
    use TransactionFilterTrait;

    protected $accountService;

    public function __construct(AccountService $accountService)
    {
        $this->accountService = $accountService;
    }

    public function handle(Request $request, Closure $next): Response
    {
        try {
            $method = $request->method();
            $currentPath = $request->path();
            if ($method === 'GET' && ($currentPath === 'account/deposit' || $currentPath === 'account/withdraw')) {
                $transactions = $this->accountService->getListTransaction($request, [
                    'page' => 1,
                    'limit' => 100,
                ]);
                if (!empty($transactions) && isset($transactions->data)) {
                    $actionType = request()->url() === route('en.deposit.index') ? 'DEPOSIT' : 'WITHDRAW';
                    $transactionHistory = $this->filterTransactions($transactions, $actionType);

                    $redirectResponse = $this->handleTransactionRedirection($request, $transactionHistory, strtolower($actionType));
                    if ($redirectResponse) {
                        return $redirectResponse;
                    }
                }
            }
        } catch (\Exception $e) {
            return $next($request);
        }

        return $next($request);
    }

    // Abstracted function to handle redirection based on transaction method
    protected function handleTransactionRedirection($request, $transactionHistory, $type)
    {
        if ($transactionHistory->isNotEmpty()) {
            $firstTransaction = $transactionHistory->first();
            if (isset($firstTransaction->method)) {
                $redirectPaths = [
                    'phone_card' => "account/{$type}/card",
                    'cryptopay' => "account/{$type}/crypto",
                    'ewallet' => "account/{$type}/ewallet",
                    'momo' => "account/{$type}/ewallet",
                    'viettelmoney' => "account/{$type}/ewallet",
                    'p2p' => "account/{$type}/p2p",
                ];

                $currentPath = $request->path();
                $method = $firstTransaction->method;
                if (isset($redirectPaths[$method]) && $currentPath !== $redirectPaths[$method]) {
                    return redirect("/{$redirectPaths[$method]}");
                }
            }
        }
        return null;
    }
}
