<?php

namespace King\Accounts\Http\Middleware;

use App\Auth\GatewayUser;
use App\Services\AuthService;
use App\Services\GatewayApi;
use Closure;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use King\Accounts\Services\AccountService;
use Symfony\Component\HttpFoundation\Response;

class GatewayAuthMiddleware
{
    // Api gateway service
    protected $GatewayApi;
    protected $authService;
    protected $accountService;


    // Defined protected page here
    protected $ProtectedPathPattern = 'account*';

    protected $PublicPaths = ['signup', 'login'];

    public function __construct(GatewayApi $GatewayApi, AuthService $authService, AccountService $accountService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->authService = $authService;
        $this->accountService = $accountService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $passHeaders = [];
        $headersAll = $request->headers->all();

        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }

        try {
            $userCookie = $request->cookie('user');

            if (!empty($userCookie)) {
                $user = new GatewayUser(json_decode($userCookie));
                Auth::login($user);

                if (in_array($request->path(), $this->PublicPaths)) {
                    return redirect('/account');
                }
            } else {
                if (!Auth::check() && $this->isProtectedPage($request->path())) {
                    return redirect('/?type=modal-login');
                }
            }
        } catch (Exception $ex) {
            return $this->logoutAndRedirect($request, $next);
        }

        return $next($request);
    }

    protected function isProtectedPage(string $path): bool
    {
        return Str::is($this->ProtectedPathPattern, $path);
    }

    protected function logoutAndRedirect(Request $request, Closure $next)
    {
        $this->authService->logout();
        if ($this->isProtectedPage($request->path())) {
            return redirect('/');
        }

        return $next($request);
    }
}
