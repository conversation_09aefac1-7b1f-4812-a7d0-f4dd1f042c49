<?php

namespace King\Accounts\Helpers;

use Illuminate\Support\Facades\Route;

class BodyClass
{
    protected array $classes = [];

    public function __construct()
    {
        $this->classes = [
            'font-sans',
            'antialiased',
            'max-xl:overflow-hidden',
            'route-' . Route::current()->getName(),
        ];
    }

    public function shouldHideHeader(): self
    {
        if (in_array(Route::current()->getName(), config('account.hide_header_routes'))) {
            $this->classes[] = 'hide-header-mobile';
        }
        return $this;
    }

    public function render(): string
    {
        return implode(' ', $this->classes);
    }

    public function __toString(): string
    {
        return $this->render();
    }

    public static function make(): self
    {
        return (new self())->shouldHideHeader();
    }
}
