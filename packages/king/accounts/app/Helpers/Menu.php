<?php

if (!function_exists('getMenuItemTag')) {
    function getMenuItemTag($item): string
    {
        $isButton = $item['isButton'] ?? false;
        $isLogin = !empty($item['isLogin']);

        return ($isButton || $isLogin) ? 'span' : 'a';
    }
}

if (!function_exists('setMenuActiveClassByRegex')) {
    function setMenuActiveClassByRegex(?string $pattern): string
    {
        if (empty($pattern)) {
            return '';
        }

        $currentPath = request()->path();
        $isActive = preg_match($pattern, $currentPath);

        return $isActive ? 'active' : '';
    }
}
if (!function_exists('setMenuActiveClassByAlias')) {
    function setMenuActiveClassByAlias($alias)
    {
        return request()->url() === $alias ? 'active' : '';
    }
}

if (!function_exists('setMenuActiveClassByPath')) {
    function setMenuActiveClassByPath($path)
    {
        return request()->path() === ltrim($path, '/') ? 'active' : '';
    }
}
