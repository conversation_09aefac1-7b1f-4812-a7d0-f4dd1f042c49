<?php

if (! function_exists('formatAmount')) {
    function formatAmount($amount, $symbol = '')
    {
        $formattedNumber = number_format($amount, 0, '.', ',');
        return $symbol ? "{$formattedNumber} {$symbol}" : $formattedNumber;
    }
}

if (! function_exists('formatToK')) {
    function formatToK($amount)
    {
        return formatAmount($amount, 'K');
    }
}

if (! function_exists('formatVndToK')) {
    function formatVndToK($amount)
    {
        $amountInK = (float) $amount / 1000;
        return formatAmount($amountInK, 'K');
    }
}

if (! function_exists('formatKToVnd')) {
    function formatKToVnd($amount, $symbol = 'VND')
    {
        $amountInK = (float) $amount * 1000;
        return formatAmount($amountInK, $symbol);
    }
}

if (! function_exists('addSymbolToNumber')) {
    function addSymbolToNumber($number = null, $symbol = 'K', $emptyValue = '-')
    {
        if ($number === null) {
            return $emptyValue;
        }

        return "{$number} {$symbol}";
    }
}
