<?php

namespace King\Accounts\Helpers;

class GameHelper
{
    /**
     * @return string
     */
    public static function convertSlugToGameType($gameType): string
    {
        $gameTypeMapping = [
            'game-bai' => 'game_cards',
            'ban-ca' => 'fishing',
            'tai-xiu' => 'sicbo',
            'xoc-dia' => 'xocdia',
            'rong-ho' => 'dragontiger',
            'game-khac' => 'other',
            'no-hu' => 'nohu',
            'quay-so-number-games' => 'lottery',
            'game-nhanh' => 'instant',
            'quay-slots' => 'slots',
            'table-games' => 'tables',
        ];
        if (isset($gameTypeMapping[$gameType])) {
            return $gameTypeMapping[$gameType];
        }
        return $gameType;
    }

    /**
     * @return string
     */
    public static function convertGameTypeToSlug($gameType): string
    {
        $gameTypeMapping = [
            'game_cards' => 'game-bai',
            'fishing' => 'ban-ca',
            'sicbo' => 'tai-xiu',
            'xocdia' => 'xoc-dia',
            'dragontiger' => 'rong-ho',
            'other' => 'game-khac',
            'nohu' => 'no-hu',
            'lottery' => 'quay-so-number-games',
            'instant' => 'game-nhanh',
            'slots' => 'quay-slots',
            'tables' => 'table-games',
        ];
        if (isset($gameTypeMapping[$gameType])) {
            return $gameTypeMapping[$gameType];
        }
        return $gameType;
    }
}
