<?php

namespace King\Accounts\Traits;

trait TransactionFilterTrait
{
    /**
     * Filter transactions based on action type and status
     *
     * @param object $transactions
     * @param string $actionType
     * @return \Illuminate\Support\Collection
     */
    protected function filterTransactions($transactions, string $actionType)
    {
        if (empty($transactions) || !isset($transactions->data)) {
            return collect([]);
        }

        $transactionHistory = collect($transactions->data)->filter(function ($transaction) use ($actionType) {
            return $transaction->action === $actionType
                && strtoupper($transaction->status) === 'FINISHED'
                && ($actionType === 'DEPOSIT' ? strtoupper($transaction->type) === 'PAYMENT' : true);
        })->values();
        return $transactionHistory;
    }

    /**
     * Update deposit tabs based on transaction history
     *
     * @param array $depositTabs
     * @param \Illuminate\Support\Collection $transactionHistory
     * @return array
     */
    protected function updateDepositTabs(array $depositTabs, $transactionHistory)
    {
        if (count($transactionHistory) > 0) {
            return array_map(function ($tab) {
                if ($tab['status'] === 'propose') {
                    $tab['status_text'] = '';
                    $tab['status'] = '';
                }
                return $tab;
            }, $depositTabs);
        }

        return $depositTabs;
    }
}
