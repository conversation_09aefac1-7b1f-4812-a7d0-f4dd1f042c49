<?php

namespace King\Accounts\Traits;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;

trait AccountOverviewTrait
{
    public const METHOD_CARD = 'phone_card';

    /**
     * Filter and process valid transactions
     *
     * @param array $listTransaction
     * @return array
     */
    protected function processValidTransactions($listTransaction)
    {
        $validTransaction = [];
        $nicepayData = Auth::user() ? Cache::get('nicepayData' . (Auth::user()->getAuthIdentifier() ?? '')) : '';

        if (count($listTransaction) > 0) {
            $filterTransaction = array_filter(
                $listTransaction,
                function ($item) {
                    return $item->action === 'DEPOSIT'
                        && $item->status !== 'CANCEL'
                        && $item->status !== 'DRAFT'
                        && ($item->method === 'nicepay' || $item->method === self::METHOD_CARD);
                },
            );

            if (count($filterTransaction) > 0) {
                foreach ($filterTransaction as $item) {
                    if ($item->method === self::METHOD_CARD) {

                        $cardAmount =  $this->getClosestCardAmount($item->amount);
                        $item->card_amount_txt = $cardAmount / 1000;
                        // VIETTEL is default network
                        $network = isset($item->to_bank_code) ? $item->to_bank_code : 'VIETTEL';

                        $item->link = '/account/deposit/card?network=' . $network . '&money=' . $cardAmount;
                    } else {
                        $item->link = '/account/deposit?money=' . $item->amount;
                    }

                    // Filter out transactions with duplicate amounts/card values
                    $filterTransaction = array_filter(
                        $validTransaction,
                        function ($transaction) use ($item) {
                            if ($item->method === self::METHOD_CARD) {
                                return $transaction->card_amount_txt === $item->card_amount_txt;
                            }
                            return $transaction->method === $item->method && $transaction->amount === $item->amount;
                        },
                    );

                    // Keep only latest card transaction
                    if ($item->method === self::METHOD_CARD) {
                        $lastCardTransaction = null;
                        foreach ($filterTransaction as $t) {
                            $lastCardTransaction = $t;
                        }
                        $filterTransaction = $lastCardTransaction ? [$lastCardTransaction] : [];
                    }

                    if (count($filterTransaction) === 0) {
                        $validTransaction[] = $item;
                    }
                }

                $validTransaction = array_slice($validTransaction, 0, 3);
            }
        }

        return $validTransaction;
    }

    /**
     * Get the closest amount from predefined values
     *
     * @param float $amount
     * @return int
     */
    protected function getClosestCardAmount($amount = 0)
    {
        $predefinedAmounts = [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000];

        $closest = null;
        $minDiff = PHP_FLOAT_MAX;

        foreach ($predefinedAmounts as $value) {
            $diff = abs($amount - $value);
            if ($diff < $minDiff) {
                $minDiff = $diff;
                $closest = $value;
            }
        }

        return $closest;
    }
}
