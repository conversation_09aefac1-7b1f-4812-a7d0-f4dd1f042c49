<?php

namespace App\Logger;

use Exception;
use Illuminate\Support\Facades\Log;

class Logger
{
    public static function logger(string $channel, string $msg, ?array $trace = null)
    {
        $logstatus = config('app.logger_api.status', false);
        if (!$logstatus) {
            return true;
        }
        $message = "\n====MESSAGE============================================"
            . "\n" . $msg
            . "\n";

        if ($trace) {
            $message .= "\n===TRACE============================================="
                . "\n" . json_encode($trace)
                . "\n";
        }
        Log::channel($channel)->debug($message);
    }
    public static function exception(string $channel, Exception $exception)
    {
        static::logger($channel, $exception->getMessage() ?? '', $exception->getTrace() ?? '');
    }
}
