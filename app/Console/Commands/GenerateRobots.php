<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class GenerateRobots extends Command
{
    protected $signature = 'app:generate-robots';

    protected $description = 'Generate the robots.txt file for SEO purposes';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $content = "User-agent: *\n";
        $appUrl = config('app.url');
        $subdomain = $this->getSubdomain($appUrl);

        if ($subdomain) {
            $content .= "Disallow: /\n";
            // $content .= 'Sitemap: ' . $appUrl . "/sitemap.xml\n";
        } else {
            $content .= "Allow: /\n";
            // $content .= "Disallow: /login/\n";
            $content .= 'Sitemap: ' . $appUrl . "/sitemap.xml\n";
        }

        $filePath = public_path('robots.txt');

        if (File::exists($filePath)) {
            File::delete($filePath);
        }

        File::put($filePath, $content);
        $this->info('robots.txt file has been generated successfully.');
    }
    public function getSubdomain($url)
    {
        $host = parse_url($url, PHP_URL_HOST);
        $parts = explode('.', $host);
        if (count($parts) > 2) {
            // There is a subdomain
            return implode('.', array_slice($parts, 0, -2));
        } else {
            // No subdomain
            return null;
        }
    }
}
