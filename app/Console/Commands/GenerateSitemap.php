<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Spatie\Sitemap\Sitemap;
use Spatie\Sitemap\SitemapGenerator;
use Spatie\Sitemap\Tags\Url;

class GenerateSitemap extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-sitemap';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Auto create sitemap
        SitemapGenerator::create(config('app.url'))
            ->writeToFile(public_path('sitemap2.xml'));

        // Manually create sitemap
        $sitemap = Sitemap::create();
        // Static pages
        $sitemap->add(Url::create('/')->setLastModificationDate(Carbon::yesterday()));
        $sitemap->add(Url::create('/cong-game')->setLastModificationDate(Carbon::yesterday())->addAlternate('/th/games', 'th'));
        $sitemap->add(Url::create('/song-bai-livecasino-truc-tuyen')->setLastModificationDate(Carbon::yesterday())->addAlternate('/th/casino', 'th'));

        // Dynamic pages
        $gametypes = ['slots', 'fish'];
        foreach ($gametypes as $type) {
            $sitemap->add(Url::create("/cong-game/?type={$type}")->setLastModificationDate(Carbon::yesterday()));
        }

        $sitemap->writeToFile(public_path('sitemap.xml'));
    }
}
