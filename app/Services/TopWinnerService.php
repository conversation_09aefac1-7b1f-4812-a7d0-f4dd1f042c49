<?php

namespace App\Services;

use Symfony\Component\HttpFoundation\Response;

class TopWinnerService
{
    public const TOP_WINNER_URL = '/home/<USER>';
    public const TOP_WINNER_SLOT_URL = '/home/<USER>';
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getTopWinner($request, $isSlot = false)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: $isSlot ? self::TOP_WINNER_SLOT_URL : self::TOP_WINNER_URL,
                cookies: ['lang' => $request->lang],
            );

            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                $data = $response->data;

                $mappings = [
                    'weekBigWin' => 'weekWin',
                    'monthBigWin' => 'monthWin',
                    'recentBigWin' => 'nearWin',
                ];

                foreach ($mappings as $oldKey => $newKey) {
                    if (isset($data->$oldKey)) {
                        $data->$newKey = $data->$oldKey;
                        unset($data->$oldKey);

                        if (is_array($data->$newKey)) {
                            usort($data->$newKey, function ($a, $b) {
                                return $b->winlost - $a->winlost;
                            });
                        }
                    }
                }

                return $data;
            }

            return [
                'nearWin' => [],
                'weekWin' => [],
                'monthWin' => [],
            ];
        } catch (\Exception $e) {
            return [
                'nearWin' => [],
                'weekWin' => [],
                'monthWin' => [],
            ];
        }
    }
}
