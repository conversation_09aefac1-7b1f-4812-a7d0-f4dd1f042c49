<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\GatewayEndpoint;
use App\Helpers\DetectDeviceHelper;

final class SportsService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get sports iframe URL
     */
    public function getSportsUrl(string $gameType): array
    {
        $endpoints = [
            'bti-sports' => GatewayEndpoint::BTI_SPORTS,
            'im-sports' => GatewayEndpoint::IM_NORMAL_SPORTS,
            'saba-sports' => GatewayEndpoint::SABA_SPORTS,
            'ksports' => GatewayEndpoint::K_SPORTS,
            'virtual-k-sports' => GatewayEndpoint::K_SPORTS_VITUAL,
            'virtual-saba-sports' => GatewayEndpoint::SABA_SPORTS_VITUAL,
            'virtual-im-sports' => GatewayEndpoint::IM_SPORTS,
            'virtual-pp-sports' => GatewayEndpoint::PP_SPORTS,
        ];

        if (!array_key_exists($gameType, $endpoints)) {
            throw new \Exception('Invalid game type');
        }

        try {
            $endpoint = $endpoints[$gameType]->value;

            $request = request();
            if ($request->has(['leagueId', 'matchId'])) {
                $endpoint .= '&leagueId=' . $request->get('leagueId')
                    . '&matchId=' . $request->get('matchId');
            }
            $response = $this->gatewayApi->get(
                endpoint: $endpoint,
            );
            if (!$response || !isset($response->data) || empty($response->data->url)) {
                throw new \Exception('Invalid response format');
            }
            $url = $response->data->url ?? '';

            // Remove loginUrl parameter from the URL
            $parsedUrl = parse_url($url);
            parse_str($parsedUrl['query'] ?? '', $queryParams);
            if ((isset($gameType) && $gameType !== 'saba-sports') || DetectDeviceHelper::isMobile()) {
                $queryParams['loginUrl'] = route('en.home.index') . '?type=modal-login';
                $queryParams['registerUrl'] = route('en.home.index') . '?type=modal-register';
            }
            $parsedUrl['query'] = http_build_query($queryParams);
            $url = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'] . '?' . $parsedUrl['query'];
            if ($endpoint === GatewayEndpoint::K_SPORTS_VITUAL->value) {
                $url = $url . '&sportType=1_8';
            }

            if ($request->has(['leagueId', 'matchId'])) {
                $url = $url . '&leagueId=' . $request->get('leagueId')
                    . '&eventId=' . $request->get('matchId');
            }
            return [
                'url' => $url,
                'endpoint' => $endpoint,
            ];

        } catch (\Exception $e) {
            return [
                'url' => '',
                'endpoint' => $endpoint,
            ];
        }
    }

    /**
     * Get hot matches
     *
     * @param array $params Query parameters
     * @return array
     * @throws \Exception
     */
    public function getHotMatches(array $params = []): array
    {
        try {
            $response = $this->gatewayApi->getPromotion(
                endpoint: GatewayEndpoint::HOT_MATCHES->value,
            );
            return $response->data;

        } catch (\Exception $e) {
            return [];
        }
    }
}
