<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Jenssegers\Agent\Agent;
use Symfony\Component\HttpFoundation\Response;

class AuthService
{
    public const COOKIE_USER_TEXT = 'user';

    public const TIME_COOKIE = 1440;

    public const TOP_WIN_LIMIT_DISPLAY = 10;

    private $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function loginWithToken(Request $request, string $token)
    {
        $trackingParams = [
            'a' => $request->query('a'),
            'pxl' => $request->query('pxl'),
            'zoneid' => $request->query('zoneid'),
            'aff_id' => $request->query('aff_id'),
            'querystring' => $request->query('querystring'),
            'utm_source' => $request->query('utm_source'),
            'utm_medium' => $request->query('utm_medium'),
            'utm_campaign' => $request->query('utm_campaign'),
            'utm_term' => $request->query('utm_term'),
            'utm_content' => $request->query('utm_content'),
        ];

        $trackingParams = array_filter($trackingParams);

        $response = $this->gatewayApi->get(
            endpoint: '/user/login',
            queryparams: array_merge(['token' => $token], $trackingParams),
            headers: [],
            cookies: [],
        );
        return $this->handleReponse($response);
    }
    public function checkLogin($request)
    {
        $dataRq = $request->only('username', 'password', 'token');
        $headersAll = $request->headers->all();
        $passHeaders = $this->getDeviceInfo();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::LOGIN->value,
            data: [...$dataRq, ...$this->getDeviceInfo()],
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );

        return $this->handleReponse($response);
    }

    public function logout()
    {
        $lang = app()->getLocale();
        request()->session()->flush();
        Auth::logout();
        request()->session()->regenerateToken();
        cookie()->queue(Cookie::forget(name: AuthService::COOKIE_USER_TEXT));
        session(['locale' => $lang]);
        app()->setLocale($lang);
    }
    /**
     * Handle register
     *
     * @param $request data
     */
    public function register($request)
    {
        $dataRq = $request->all();
        $headersAll = $request->headers->all();
        $passHeaders = $this->getDeviceInfo();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $trackingParams = config('constants.affiliate_tracking_params');
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::REGISTER->value,
            data: [...$dataRq, ...$this->getDeviceInfo()],
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );

        foreach ($trackingParams as $param) {
            Cookie::queue(Cookie::forget($param));
        }
        return $this->handleReponse($response);
    }

    public function changePass($request)
    {
        $dataRq = $request->all();
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::CHANGE_PASS->value,
            data: $dataRq,
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );

        return $response;
    }

    public function changeInfo($request)
    {
        $dataRq = $request->all();
        $headersAll = $request->headers->all();
        foreach ($headersAll as $key => $value) {
            $passHeaders[$key] = $value[0];
        }
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::UPDATE_INFO->value,
            data: $dataRq,
            headers: $passHeaders,
            cookies: ['lang' => $request->lang],
        );
        $cookie = Cookie::get(self::COOKIE_USER_TEXT);
        if ($cookie) {
            $cookie = json_decode($cookie);
            if (isset($response->user->fullname) && $response->user->fullname) {
                $cookie->fullname = $response->user->fullname;
                Cookie::queue(self::COOKIE_USER_TEXT, json_encode($cookie), self::TIME_COOKIE, null, null, false, false);
            }
        }
        return $response;
    }

    /**
     * Handle response auth
     *
     * @param $response
     */
    private function handleReponse($response)
    {
        $result['status'] = !empty($response->code) && $response->code == Response::HTTP_OK ? true : false;
        $result['message'] = !empty($response->message) ? $response->message : '';
        $result['cookie'] = [];
        if ($result['status']) {
            $cookie = cookie(self::COOKIE_USER_TEXT, json_encode($response->data[0]), self::TIME_COOKIE, null, null, false, false);
            $result['cookie'] = $cookie;
            $result['data'] = !empty($response->data) ? $response->data[0] : '';
        }
        return $result;
    }

    /**
     * Handle response auth
     *
     * @param $response
     */
    public function updateCookie($user)
    {
        $userCookie = request()->cookie(self::COOKIE_USER_TEXT);
        if ($userCookie) {
            $userCookie = json_decode($userCookie);

            $userArray = (array) $user;
            $userCookieArray = (array) $userCookie;
            $mergedArray = array_merge($userCookieArray, $userArray);
            $mergedUser = (object) $mergedArray;
            $cookie = cookie(self::COOKIE_USER_TEXT, json_encode($mergedUser), self::TIME_COOKIE, null, null, false, false);
            return $cookie;
        }
        $cookie = cookie(self::COOKIE_USER_TEXT, json_encode($user), self::TIME_COOKIE, null, null, false, false);
        return $cookie;
    }

    private function getDeviceInfo()
    {
        $agent = new Agent();
        $os = $agent->platform();
        $browser = $agent->browser();
        $device = $agent->device();
        return [
            'ip' => request()->ip(),
            'os' => $os,
            'browser' => $browser,
            'device' => $device,
        ];
    }
}
