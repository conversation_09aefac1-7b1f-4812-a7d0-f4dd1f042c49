<?php

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Auth;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class DepositService
{
    protected $gatewayApi;
    public const MOMO = 'MoMo';
    public const VIETTELMONEY = 'Viettel Money';
    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getInitialData()
    {

        $passHeaders = [];
        $headersAll = request()->headers->all();
        // Either Loop through the $headersAll array
        foreach ($headersAll as $key => $value) {
            // Since $value is an array, we take the first element
            // Assuming we want the first value for each header
            $passHeaders[$key] = $value[0];
        }

        $userCookie = request()->cookie('user');

        $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_INDEX->value, headers: $passHeaders, cookies: ['user' => $userCookie]);
        return [
            'ewallets' => [
                'momo' => [
                    'key' => 'momo',
                    'name' => self::MOMO,
                    'data' => $response->data->momos ?? [],
                ],
                'viettelmoney' => [
                    'key' => 'viettelmoney',
                    'name' => self::VIETTELMONEY,
                    'data' => $response->data->viettelPays ?? [],
                ],
            ],
            'recommendDeposit' => [], // This field isn't in the response
            'packages' => $response->data->packages ?? [],
            'nicepayInfo' => $response->data->nicepayInfo ?? [],
            'p2pLink' => $response->data->p2pLink ?? '',
            'networks' => (array) $this->getPhonecardList()['cardlist'] ?? [],
            'networksStatus' => $this->getPhonecardList()['status'] ?? 0,
            'withdrawBanks' => $response -> data -> withdrawBanks ?? [],
        ];
    }

    public function createCodepayDeposit(Request $request): array
    {
        try {
            $dataRq = $request->all();
            $headersAll = $request->headers->all();
            foreach ($headersAll as $key => $value) {
                $passHeaders[$key] = $value[0];
            }
            $userCookie = $request->cookie('user');

            $amount = (int) str_replace([',', '.'], '', $dataRq['amount']);
            if (isset($dataRq['amount'])) {
                $dataRq['amount'] = $amount * 1000;
            }


            if (isset($dataRq['packageId'])) {
                $dataRq['packageId'] = (int) $dataRq['packageId'];
            }


            $response = $this->gatewayApi->post(
                endpoint: GatewayEndpoint::DEPOSIT_CODEPPAY_CREATE->value,
                data: $dataRq,
                headers: $passHeaders,
                cookies: ['lang' => $request->lang, 'user' => $userCookie],
            );
            if ($response->status === Response::$statusTexts[Response::HTTP_OK]) {
                $data = $response->data[0];
                $data->expired_countdown_time = now()->addSeconds(60);
                Cache::forget('createdNicepayTime' . Auth::user()->getAuthIdentifier());
                Cache::remember('createdNicepayTime' . Auth::user()->getAuthIdentifier(), 300, function () {
                    return now()->addSeconds(60);
                });

                Cache::remember('nicepayData' . Auth::user()->getAuthIdentifier(), 60 * 30, function () use ($data) {
                    return $data;
                });
                return [
                    'status' => Response::$statusTexts[Response::HTTP_OK],
                    'data' => $data,
                ];
            }

            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $response->message ?? 'Failed to create deposit',
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getCodepayInfo()
    {
        try {
            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_CODEPPAY_INFO->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data[0]) ? $response->data[0] : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getCryptoCurrencyList(): array
    {
        try {
            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_CRYPTO_LIST->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data) ? $response->data : [];
        } catch (\Exception $e) {
            return [];
        }
    }

    public function getCryptoAddress($network)
    {
        try {
            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_CRYPTO_ADDRESS->value, queryparams: ['network' => $network]);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data[0]) ? $response->data[0] : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getCryptoDeposit(): array
    {
        return Cache::remember('crypto_deposits' . Auth::user()->getAuthIdentifier(), 3600, function () {
            return Http::get(config('payment.crypto.deposit_endpoint'))->json()['data'];
        });
    }
    public function getEwalletCode()
    {
        try {
            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_EWALLET_CODE->value);
            return isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data) ? $response->data : null;
        } catch (\Exception $e) {
            return null;
        }
    }
    public function getPhonecardList(): array
    {
        try {
            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_CARD_NETWORKS->value);
            return [
                'status' => $response->status,
                'cardlist' => $response->status == 1 ? $response->cardlist : [],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 0,
                'cardlist' => [],
            ];
        }
    }

    public function createPhonecardDeposit(Request $request)
    {
        try {
            $dataRq = $request->all();
            if (isset($dataRq['card_amount'])) {
                $dataRq['card_amount'] = (int) str_replace([',', '.'], '', $dataRq['card_amount']);
            }

            $response = $this->gatewayApi->post(endpoint: GatewayEndpoint::DEPOSIT_CARD_CREATE->value, data: $dataRq);
            if ($response->status === Response::$statusTexts[Response::HTTP_OK]) {
                return [
                    'status' => Response::$statusTexts[Response::HTTP_OK],
                    'data' => $response->data,
                ];
            }
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
}
