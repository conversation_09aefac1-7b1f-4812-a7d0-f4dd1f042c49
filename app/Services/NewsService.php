<?php

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Symfony\Component\HttpFoundation\Response;

class NewsService extends BaseService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get List News
     *
     * @param $request
     * @return array
     */
    public function getListNews($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::LIST_NEWS->value,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Get List Category
     *
     * @param $request
     * @return array
     */
    public function getListCategory($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::LIST_CATEGORY->value,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->categories;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getDetailNews($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::POST->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }
            return null;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }


    /**
     * Get Type List of Casino Games
     *
     * @param $request
     * @param array $params
     * @return array
     */
    public function getDetailCategory($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::DETAIL_CATEGORY->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->data ?? (object)[];
            }
            return (object)[];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
    /**
     * Get Latest Posts
     *
     * @param $request
     * @param array $params
     * @return array
     */
    public function getLatestPosts($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::POST_LATEST->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->posts ?? [];
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
}
