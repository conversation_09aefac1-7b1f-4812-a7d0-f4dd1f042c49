<?php


namespace App\Services;

use App\Enums\ApiEndpointType;
use App\Enums\GatewayEndpoint;
use App\Helpers\DetectDeviceHelper;
use Symfony\Component\HttpFoundation\Response;

class AccountService extends BaseService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getListTransaction($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::LIST_TRANSACTION->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getListBet($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::LIST_BET->value,
                cookies: ['lang' => $request->lang],
                queryparams: $params,
            );

            if (self::isSuccessResponse($response)) {
                return $response;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function mapPaymentMethod(string $method): string
    {
        $methodMapping = [
            'nicepay' => 'codepay',
        ];

        return $methodMapping[$method] ?? $method;
    }

    /**
     * Lấy giao dịch nạp tiền gần nhất
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function getLastDeposit($request): string
    {
        $response = $this->getListTransaction($request, [
            'limit' => 10,
            'action' => 'DEPOSIT'
        ]);
        if (!isset($response->data) || empty($response->data)) {
            return '';
        }
        $transactions = collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->take(1)->values();

        $method = !empty($transactions->first()->method) ? $transactions->first()->method : '';
        return $this->mapPaymentMethod($method);
    }

    public function getLastDepositSuccess($request): array
    {
        $response = $this->getListTransaction($request, [
            'limit' => 20,
            'action' => 'DEPOSIT',
            'status' => 'FINISHED',
        ]);

        if (!isset($response->data) || empty($response->data)) {
            return [];
        }
        return collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->take(1)
            ->values()
            ->toArray();
    }

    /**
     * Lấy giao dịch rút tiền gần nhất
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    public function getLastWithdrawSuccess($request)
    {
        $response = $this->getListTransaction($request, [
            'limit' => 20,
            'action' => 'WITHDRAW',
            'status' => 'FINISHED'
        ]);
        if (empty($response->data)) {
            return [];
        }

        return collect($response->data)
            ->filter(function ($transaction) {
                return $transaction->type === 'PAYMENT';
            })
            ->take(1)
            ->values()
            ->toArray();
    }

    public function getUserPromotionInfo($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::ACCOUNT_INFO->value,
                cookies: ['lang' => $request->lang],
            );
            if (isset($response) && !empty((array)$response->data) && self::isSuccessResponse($response)) {
                return $response;
            }
            return null;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage()
            ];
        }
    }

    public function getUserCommission($request)
    {
        $response = $this->gatewayApi->get(
            endpoint: GatewayEndpoint::ACCOUNT_COMMISSION->value,
            cookies: ['lang' => $request->lang],
        );
        if (isset($response) && self::isSuccessResponse($response)) {
            return $response;
        }
        return null;
    }

    public function getDataDisplayUserPromotion($request)
    {
        $promotions = [];
        $responsePromotion = $this->getUserPromotionInfo($request);
        if (isset($responsePromotion->data) && !empty((array) $responsePromotion->data)) {
            $promotionData = (array) $responsePromotion->data;
            $packageResource = config('promotions.package_resource');
            if (isset($packageResource[$promotionData['package_id']])) {
                $promotionData = array_merge($promotionData, $packageResource[$promotionData['package_id']]);
            }
            array_push($promotions, $promotionData);
        }
        return $promotions;
    }

    public function getDataDisplayUserCommission($request)
    {
        function coverNumber($number)
        {
            return round(floatval(str_replace(',', '', (string)$number)));
        }

        function maxBetHeight($number, $maxBet = 0, $maxReturn = 0)
        {
            $isMobile = DetectDeviceHelper::isMobile();
            $MAX_HEIGHT = $isMobile ? 110 : 120;
            if ($maxBet == 0 && $maxReturn == 0) {
                return 0;
            }
            $newNumber = coverNumber($number);
            return round(($newNumber * $MAX_HEIGHT) / max($maxBet, $maxReturn));
        }

        $commission = [];
        $commissionResponse = $this->getUserCommission($request);
        $commissionResponse = (array) $commissionResponse;
        if (!empty($commissionResponse['data']) && count($commissionResponse['data']) > 0) {
            $maxBetValue = max(collect(array_column($commissionResponse['data'], 'stake'))->map(function ($i) {
                return coverNumber($i);
            })->toArray());
            $maxReturnValue = max(collect(array_column($commissionResponse['data'], 'commission_estimate'))->map(function ($i) {
                return coverNumber($i);
            })->toArray());
            $commission = collect($commissionResponse['data'])->map(function ($item) use ($maxBetValue, $maxReturnValue) {
                $item = (array) $item;
                return [
                    'date' => date('d/m', strtotime($item['date'])),
                    'day' => date('d/m', strtotime($item['date'])),
                    'bet' => $item['stake'],
                    'return' => $item['commission_estimate'],
                    'rolling' => $item['rolling'],
                    'betHeight' => maxBetHeight($item['stake'], $maxBetValue, $maxReturnValue),
                    'returnHeight' => maxBetHeight($item['commission_estimate'], $maxBetValue, $maxReturnValue),
                    'zindexBet' => coverNumber($item['stake']) > coverNumber($item['commission_estimate']) ? 1 : 2,
                    'zindexReturn' => coverNumber($item['commission_estimate']) > coverNumber($item['stake']) ? 1 : 2,
                ];
            })->toArray();
        } else {
            $commission = [];
            for ($i = 0; $i < 7; $i++) {
                $date = date('Y-m-d', strtotime("-$i days"));
                $commission[] = [
                    'date' => date('d/m', strtotime($date)),
                    'day' => date('d/m', strtotime($date)),
                    'bet' => 0,
                    'return' => 0,
                    'rolling' => 0,
                    'betHeight' => 0,
                    'returnHeight' => 0,
                    'zindexBet' => 1,
                    'zindexReturn' => 1,
                ];
            }
        }
        return $commission;
    }

    public function cancelPromotion($request)
    {
        $dataRq = $request->all();
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::CANCEL_PROMOTION->value,
            data: $dataRq,
            cookies: ['lang' => $request->lang],
        );
        return $response;
    }

    public function telegramOTPVerification($request)
    {
        $dataRq = $request->all();
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::TELEGRAM_OTP_VERIFICATION->value,
            data: $dataRq,
            cookies: ['lang' => $request->lang],
        );
        return $response;
    }

    public function changePassword($request)
    {
        $dataRq = [
            'password' => $request->password,
            'newPassword' => $request->newPassword,
            'confirmPassword' => $request->confirmNewPassword,
        ];
        $response = $this->gatewayApi->post(
            endpoint: GatewayEndpoint::CHANGE_PASSWORD->value,
            data: $dataRq,
            cookies: ['lang' => $request->lang],
        );
        return $response;
    }
    public function getAccountVerificationStatus($request)
    {
        $verificationStatusDefault = (object)[
            'deposit' => false,
            'bank' => false,
            'tele' => false,
            'is_show_freespin' => false,
        ];
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::ACCOUNT_VERIFICATION_STATUS->value,
                cookies: ['lang' => $request->lang],
                type: ApiEndpointType::PROMOTION,
            );


            if (self::isSuccessResponse($response)) {
                // $request->session()->put('verificationStatus', $response->data);
                return $response->data;
            }

            // $request->session()->put('verificationStatus', $verificationStatusDefault);
            return $verificationStatusDefault;
        } catch (\Exception $e) {
            // $request->session()->put('verificationStatus', $verificationStatusDefault);
            return $verificationStatusDefault;
        }
    }
}
