<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Symfony\Component\HttpFoundation\Response;

final class GameService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get Games
     */
    public function getGameByParams($request, $params = [])
    {

        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::GAME_SEARCH->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if ($response && $response->code === Response::HTTP_OK) {
                return $response->data;
            }
            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Game Types
     */
    public function getGameTypes($request, $params = [], $endpoint = GatewayEndpoint::GAME_TYPE->value)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: $endpoint,
                cookies: ['lang' => $request->lang],
            );

            if ($response && $response->code === Response::HTTP_OK) {
                $data = $response->data;
                if (!is_array($data)) {
                    $data = [];
                }
                $formattedData = array_map(function ($item) {
                    if ($item->key === 'all') {
                        $item->name = 'Tất cả';
                    }
                    return $item;
                }, $data);
                return $formattedData;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function gameUrl($request)
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::GAME_URL->value,
                queryparams: $request->all(),
                headers: ['cookie' => $request->cookie],
                cookies: ['lang' => $request->lang],
            );
            return $response;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
}
