<?php

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Symfony\Component\HttpFoundation\Response;

class CasinoService extends BaseService
{
    private GatewayApi $gatewayApi;

    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    /**
     * Get Casino Games
     *
     * @param $request
     * @param array $params
     * @return array
     */
    public function getCasinoGames($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::CASINO_SEARCH->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->data;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Type List of Casino Games
     *
     * @param $request
     * @param array $params
     * @return array
     */
    public function getTypes($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::CASINO_TYPE->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->data;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Provider List of Casino Games
     *
     * @param $request
     * @param array $params
     * @return array
     */
    public function getProviders($request, $params = [])
    {
        try {
            $response = $this->gatewayApi->get(
                endpoint: GatewayEndpoint::CASINO_PROVIDER->value,
                queryparams: $params,
                cookies: ['lang' => $request->lang],
            );

            if (self::isSuccessResponse($response)) {
                return $response->data;
            }

            return [];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
}
