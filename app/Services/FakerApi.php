<?php

namespace App\Services;

use App\Helpers\DetectDeviceHelper;
use App\Logger\Logger;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Auth;
use function PHPUnit\Framework\isNull;
use App\Enums\ApiEndpointType;

class FakerApi
{
    private const LOG_CHANNEL = 'faker_api';
    public const MAIN_PREFIX = '';

    public const PROMOTION_PREFIX = '/api-promotion/v1';
    protected $baseUrl;

    protected $defaultHeaders;

    protected $defaultCookies;

    protected $timeout;

    protected $retryAttempts;

    protected $retryDelay;


    /**
     * Constructor to init the baseurl, and other options
     *
     * @param string $baseUrl
     * @param array $defaultHeaders
     * @param int $timeout
     * @param int $retryAttempts
     * @param int $retryDelay
     */
    public function __construct($defaultHeaders = [], $defaultCookies = [], $timeout = 10, $retryAttempts = 3, $retryDelay = 100)
    {
        // config('services.gw.base_url');
        $this->baseUrl = config('services.faker.base_url');
        $this->defaultHeaders = $defaultHeaders;
        $this->defaultCookies = $defaultCookies;
        $this->timeout = $timeout;
        $this->retryAttempts = $retryAttempts;
        $this->retryDelay = $retryDelay;
    }

    /**
     * @param $key
     * @param $value
     * @return mixed
     */
    private function prepareCookieHeaderRequest($key, $value): mixed
    {
        if ($key != 'cookie') {
            return $value[0];
        }

        $cookieHeadersArray = explode(";", $value[0]);

        // TODO : update data from request variable
        $cfDeviceType = request()->header('CF-Device-Type'); // Values: mobile, tablet, decktop, null
        $isMobile = false;
        if ($cfDeviceType && $cfDeviceType !== 'desktop') {
            $isMobile = true;
        } else {
            $isMobile = DetectDeviceHelper::isMobile();
        }
        $userCookie = request()->cookie('user');
        $cookieLang = ['lang='.app()->getLocale()];
        if ($userCookie) {
            $cookieLang[] = 'user='.$userCookie;
        }
        return implode('; ', array_merge(
            $cookieHeadersArray,
            ['device=' . ($isMobile ? 'mobile' : 'desktop')],
            $cookieLang
        ));
    }

    public function get($endpoint, $queryparams = [], $headers = [], $cookies = [], $type = self::MAIN_PREFIX)
    {
        try {
            $passHeaders = [];
            $headersAll = request()->headers->all();
            // Either Loop through the $headersAll array
            foreach ($headersAll as $key => $value) {
                // Since $value is an array, we take the first element
                // Assuming we want the first value for each header
                $passHeaders[$key] = $this->prepareCookieHeaderRequest($key, $value);
            }

            $userCookie = request()->cookie('user');
            if ($userCookie) {
                $cookies['user'] = $userCookie;
            }

            $startTime = microtime(true);
            $url = $this->buildUrl($endpoint, $type, $queryparams);

            $mergedHeaders = array_merge($headers, $this->defaultHeaders, $passHeaders);

            $mergedCookies = array_merge($cookies, $this->defaultCookies);
            // maybe add logger
            $response = Http::noHost()
                ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
                ->withHeaders($mergedHeaders)->withCookies($mergedCookies, config('app.url'))->timeout($this->timeout)->retry($this->retryAttempts)->get($url, $queryparams);

            $data = $this->handleResponse($url, $response);

            $endTime = round((microtime(true) - $startTime) * 1000);

            $this->logRequest($endpoint, $queryparams, $data, $endTime);
            return $data;
        } catch (Exception $ex) {
            $errorMessage = $ex->getMessage();
            $jsonStartPos = strpos($errorMessage, '{');
            $errorResponse = $jsonStartPos !== false ? json_decode(substr($errorMessage, $jsonStartPos), true) : ['error' => $errorMessage];
            if ($ex->getCode() === 401) {
                request()->session()->flush();
                Auth::logout();
                request()->session()->regenerateToken();
                cookie()->queue(Cookie::forget(AuthService::COOKIE_USER_TEXT));
            }
            Logger::logger(self::LOG_CHANNEL, $ex->getMessage(), $ex->getTrace());
            return $errorResponse;
        }
    }

    public function post($endpoint, $data = [], $headers = [], $cookies = [], $type = self::MAIN_PREFIX)
    {
        try {
            $passHeaders = [];
            $headersAll = request()->headers->all();
            // Either Loop through the $headersAll array
            foreach ($headersAll as $key => $value) {
                // Since $value is an array, we take the first element
                // Assuming we want the first value for each header
                $passHeaders[$key] = $value[0];
            }

            $userCookie = request()->cookie('user');
            if ($userCookie) {
                $cookies['user'] = $userCookie;
            }
            $url = $this->buildUrl($endpoint, $type);
            $mergedHeaders = array_merge($headers, $this->defaultHeaders, $passHeaders);
            $mergedCookies = array_merge($cookies, $this->defaultCookies);
            $mergedHeaders['content-type'] = 'application/json';
            $mergedHeaders['accept'] = 'application/json';

            $response = Http::noHost()
                ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
                ->withHeaders($mergedHeaders)
                ->withCookies($mergedCookies, config('app.url'))
                ->timeout($this->timeout)
                ->retry($this->retryAttempts)
                ->post($url, $data);

            return $this->handleResponse($url, $response);
        } catch (Exception $ex) {
            $errorMessage = $ex->getMessage();
            $jsonStartPos = strpos($errorMessage, '{');
            $errorResponse = $jsonStartPos !== false ? json_decode(substr($errorMessage, $jsonStartPos), true) : ['error' => $errorMessage];
            if ($ex->getCode() === 401) {
                request()->session()->flush();
                Auth::logout();
                request()->session()->regenerateToken();
                cookie()->queue(Cookie::forget(AuthService::COOKIE_USER_TEXT));
            }
            Log::error("POST Request to {$endpoint} failed " . $ex->getMessage());
            return $errorResponse;
        }
    }

    protected function buildUrl($endpoint, $type = self::MAIN_PREFIX, $queryparams = [])
    {
        if ($type === ApiEndpointType::PROMOTION) {
            $baseUrl = $this->baseUrl . self::PROMOTION_PREFIX;
        } else {
            $baseUrl = $this->baseUrl . '/api/v1';
        }

        $url = rtrim($baseUrl, '/') . '/' . ltrim($endpoint, '/');

        // Thêm query parameters nếu có
        if (!empty($queryparams)) {
            $url .= '?' . http_build_query($queryparams);
        }

        return $url;
    }

    protected function handleResponse($url, $response)
    {
        if ($response->successful()) {
            return $response->object();
        }

        if ($response->serverError() || $response->clientError()) {
            Log::error("HTTP request to {$url} faild", [
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        }

        throw new Exception('HTTP request daild with status code:' . $response->status());
    }

    protected function logRequest($path, $payload, $response, $requestTime)
    {
        $message = json_encode([
            'path' => $path,
            'time' => "{$requestTime} ms",
            'payload' => $payload,
            'response' => $response,
        ], JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES);

        Logger::logger(self::LOG_CHANNEL, $message);
    }

    /**
     * Send GET request to promotion API endpoint
     *
     * @param string $endpoint
     * @param array $queryparams
     * @param array $headers
     * @param array $cookies
     * @return mixed
     */
    public function getPromotion($endpoint, $queryparams = [], $headers = [], $cookies = [])
    {
        return $this->get($endpoint, $queryparams, $headers, $cookies, ApiEndpointType::PROMOTION);
    }

    public function postPromotion($endpoint, $queryparams = [], $headers = [], $cookies = [])
    {
        return $this->post($endpoint, $queryparams, $headers, $cookies, ApiEndpointType::PROMOTION);
    }
}
