<?php

namespace App\Services;

use App\Enums\GatewayEndpoint;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class WithdrawService
{
    protected $gatewayApi;
    public function __construct(GatewayApi $gatewayApi)
    {
        $this->gatewayApi = $gatewayApi;
    }

    public function getInitialData()
    {
        $cryptoResponse = $this->getWithdrawCryptoData();
        return [
            'cryptoCurrencyList' => $cryptoResponse,
        ];
    }

    public function getPaymentList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_INDEX->value, cookies: ['user' => $userCookie]);

            return $response -> data;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getUserBankList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::ACCOUNT_USERBANK->value, cookies: ['user' => $userCookie]);

            return $response -> data;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
    public function getBankList()
    {
        try {
            $userCookie = request()->cookie('user');

            $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::DEPOSIT_INDEX->value, cookies: ['user' => $userCookie]);

            return $response -> data;
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function getWithdrawCryptoData()
    {
        $response = $this->gatewayApi->get(endpoint: GatewayEndpoint::WITHDRAW_CRYPTO->value);
        if (isset($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK] && isset($response->data)) {
            return $response->data;
        }
        return [];
    }

    public function withdrawCrypto(Request $request)
    {
        try {
            $data = $request->all();
            $data['address'] = $request['wallet_address'];
            if (isset($data['amount_withdraw'])) {
                $data['amount_withdraw'] = (int) str_replace([',', '.'], '', $data['amount_withdraw']);
                $data['amount'] = strval(intval($data['amount_withdraw']));
            }
            $response = $this->gatewayApi->post(endpoint: GatewayEndpoint::WITHDRAW_CRYPTO_CREATE->value, data: $data);
            return [
                'status' => $response->status,
                'message' => $response->message,
                'data' => $response->data,
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function withdrawCard(Request $request)
    {
        try {
            $data = $request->all();
            $data['card_status'] = 1;
            $response = $this->gatewayApi->post(endpoint: GatewayEndpoint::WITHDRAW_CARD_CREATE->value, data: $data);
            return [
                'status' => $response->status,
                'message' => $response->message,
                'data' => $response->data,
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function createBank(Request $request)
    {
        try {
            $data = $request->all();
            $response = $this->gatewayApi->post(endpoint: GatewayEndpoint::ACCOUNT_CREATE_BANK->value, data: $data);
            return [
                'status' => $response->status,
                'message' => $response->message,
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function withdrawbank(Request $request)
    {
        try {
            $data = $request->all();
            $response = $this->gatewayApi->post(endpoint: GatewayEndpoint::WITHDRAW_BANK->value, data: $data);
            return [
                'status' => $response->status,
                'message' => $response->message,
            ];
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }
}
