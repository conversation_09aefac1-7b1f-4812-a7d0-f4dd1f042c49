<?php

namespace App\Providers;

use App\Http\Middleware\HeaderModifier;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as RouteService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends RouteService
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();
        $this->routes(function () {
            Route::prefix('api/v1')
                ->name('api.v1.')
                ->middleware(['throttle:api', HeaderModifier::class])
                ->group((base_path('routes/api.php')));

            Route::prefix('api-promotion/v1')
                ->name('api-promotion.v1.')
                ->middleware(['throttle:api', HeaderModifier::class])
                ->group((base_path('routes/api.php')));
        });
    }

    protected function configureRateLimiting()
    {

        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(100)->by($request->user()?->id ?: $request->ip())->response(function (Request $request, array $headers) {
                return response()->json([
                    'message' => 'Too many requests, Bro!',
                ], 429);
            });
        });
    }
}
