<?php

namespace App\Providers;

use App\Auth\GatewayUserProvider;
use App\Services\GatewayApi;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use Psr\Http\Message\RequestInterface;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(GatewayApi::class, function ($app) {
            return new GatewayApi(
                config('services.gw.base_url'),
                ['Accept' => 'application/json'],
                [],
                3,
                3,
                100,
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $sqliteFilePath = database_path('database.sqlite');

        if (!File::exists($sqliteFilePath)) {
            File::put($sqliteFilePath, '');
            logger()->info('SQLite file created: ' . $sqliteFilePath);
        }

        //
        Auth::provider('gateway', function ($app, array $config) {
            return new GatewayUserProvider();
        });

        // remove host
        Http::macro('noHost', function () {
            return Http::withRequestMiddleware(
                function (RequestInterface $request) {
                    return $request->withoutHeader('host');
                },
            );
        });
    }
}
