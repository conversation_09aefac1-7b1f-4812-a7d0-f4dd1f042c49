<?php

namespace App\Enums;

enum GatewayEndpoint: string
{
    case LOGIN = '/login';

    case REFRESH = '/refresh';

    case REGISTER = '/register';

    case GAME_SEARCH = '/game/search';

    case GAME_PROVIDER = '/game/provider';

    case GAME_TYPE = '/game/types';

    case CASINO_SEARCH = '/casino/search';

    case CASINO_TYPE = '/casino/types';


    case CASINO_PROVIDER = '/casino/provider';

    case GAME_URL = '/gameUrl';

    case CASINO_URL = '/casinoUrl';

    case SABA_SPORTS = '/athena/sportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register&login=true';

    case K_SPORTS = '/tp/ksportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register&login=true';

    case BTI_SPORTS = '/tp/ssportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register&login=true';

    case IM_NORMAL_SPORTS = '/gameUrl?p=im&gId=sport';

    case IM_SPORTS = '/gameUrl?p=im&gId=virtualsport';

    case PP_SPORTS = '/gameUrl?p=pragmatic&gId=vpfh3';

    case K_SPORTS_VITUAL = '/tp/ksportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register&login=true&leagueId&matchId=&sportType=1_8';

    case SABA_SPORTS_VITUAL = '/athena/virtualSportUrl?loginPath=?type=modal-login&registerPath=?type=modal-register&login=true';

    case HOT_MATCHES = '/hotmatch';

    case DEPOSIT_INDEX = '/payment/indexdeposit';

    case DEPOSIT_CODEPPAY_CREATE = '/payment/nicepay';

    case DEPOSIT_CODEPPAY_INFO = '/payment/nicepayInfo';

    case DEPOSIT_CRYPTO_LIST = '/payment/crypto/deposit';

    case DEPOSIT_CRYPTO_ADDRESS = '/payment/crypto/address';

    case DEPOSIT_EWALLET_CODE = '/payment/ewalletCode';

    case LIST_CATEGORY = '/categories';

    case LIST_NEWS = '/indexnews';

    case DETAIL_CATEGORY = '/category';

    case DEPOSIT_CARD_NETWORKS = '/payment/gwinfo';

    case DEPOSIT_CARD_CREATE = '/payment/depositcard';

    case WITHDRAW_CRYPTO = '/payment/crypto/withdraw';

    case WITHDRAW_CRYPTO_CREATE = '/payment/withdraw-crypto';

    case WITHDRAW_BANK = '/payment/withdrawbank';

    case POST = '/post';

    case POST_LATEST = '/posts/latest';

    case LIST_TRANSACTION = '/lsgd';

    case LIST_BET = '/lsb';

    case WITHDRAW_CARD_CREATE = '/payment/withdrawcard';

    case ACCOUNT_USERBANK = '/account/userbank';

    case ACCOUNT_CREATE_BANK = '/account/createBank';

    case ACCOUNT_INFO = '/account/info';

    case ACCOUNT_COMMISSION = '/account/commission';

    case CHANGE_PASS = '/user/resetPasswordByOtp';

    case UPDATE_INFO = '/updateInfo';

    case ACCOUNT_BET = '/account/bet';

    case ACCOUNT_VERIFICATION_STATUS = '/verification/status';

    case SLOT_INFO = '/cashback/slot';

    case CASINO_INFO = '/cashback/livecasino';

    case SLOT_JACKPOT = '/slot/jackpot';

    case SERVER_TIME = '/home/<USER>';

    case REWARD_EVENT_INFO = '/lixi/newyear';

    case REWARD_EVENT_LIST = '/lixi/list';

    case TOP_RACING_EVENT_LIST = '/top/event';

    case POPUP = '/popup';

    case POPUP_CLOSE = '/popup/close/';
    
    case RECAPTCHA_SITEVERIFY = '/recaptcha/api/siteverify';
}
