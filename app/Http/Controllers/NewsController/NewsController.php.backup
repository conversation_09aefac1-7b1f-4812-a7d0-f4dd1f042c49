<?php

namespace App\Http\Controllers\NewsController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\NewsService;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public const LIMIT_NEWS = 4;
    public const LIMIT_NEWS_SWIPER = 10;
    public const LIMIT_NEWS_CATEGORY = 7;

    /**
     * @var NewsService
     */
    protected $newsService;

    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }
    public function index(Request $request)
    {
        // config SEO
        $seo = generateSeoMetaData('tin-tuc');
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => 'Trang chủ',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.news.index'),
                            'name' => 'Tin tức',
                        ],
                    ],
                ],
            ]),

            generateJsonLd('NewsArticle', [
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => url()->current(),
                ],
                'headline' => $seo->title,
                'description' => $seo->title,
                'image' => [
                    '@type' => 'ImageObject',
                    'url' => '',
                ],
                'datePublished' => '',
                'dateModified' => '',
                'author' => [
                    '@type' => 'Organization',
                    'name' => 'Domain',
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => '',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => '',
                    ],
                ],
                'keywords' => [],
                'articleBody' => '',
            ]),
        ];

        $name = __('common.news_breadcrumb');

        $listNewsRes = $this->newsService->getListNews($request);

        
        $listCategory = $listNewsRes->categories ?? [];
        $baseThumb = $listNewsRes->base_thumb ?? "";
        
        $mainBanner = $listCategory[0]->posts[0] ?? [] ;
        
        $listBanner = [];

        foreach (array_slice($listCategory, 1, 3) as $category) {
            if (!empty($category->posts)) {
                $listBanner[] = $category->posts[0]; 
            }
        }


        $breadCrump = [
            [
                'name' => $name,
                'url' => $request->url(),
            ],
        ];

        return view('pages.news', [
            'breadCrump' => $breadCrump,
            'title' => $name,
            'listCategory' => $listCategory,
            'mainBanner' => $mainBanner,
            'listBanner' => $listBanner,
            'baseThumb' =>$baseThumb
        ]);
    }

    public function newsDetail(Request $request, $slug)
    {

        $params = ['alias' => $slug];

        $detailNewsResponse = $this->newsService->getDetailNews($request, $params);

        if (empty($detailNewsResponse)) {
            return redirect(UrlPathEnum::NEWS->value);
        }



        $listCategoryResponse = $this->newsService->getListCategory($request);

        $paramsCategory = ['limit' => self::LIMIT_NEWS, 'alias' => $detailNewsResponse->post->category->alias];
        $detailCategory = $this->newsService->getDetailCategory($request, $paramsCategory);
        $detailCategory = (object) $detailCategory;

        $name = __('common.news_breadcrumb');
        $backURL = UrlPathEnum::NEWS->value;

        $breadCrump = [
            [
                'name' => $name,
                'url' => $backURL,
            ],
            [
                'name' => $detailNewsResponse->post->category->name ?? '',
                'url' => $request->url(),
            ],
        ];

        $relatedPosts = [];
        if (isset($detailNewsResponse->relatedPosts)) {
            $filteredPosts = array_filter($detailNewsResponse->relatedPosts, function ($post) {
                return !empty((array)$post); // Kiểm tra object không rỗng
            });
            $relatedPosts = array_slice($filteredPosts, 0, 3);
        }
        $totalRelatedPosts = isset($detailNewsResponse->relatedPosts) ? count($detailNewsResponse->relatedPosts) : 0;
        $topPosts = isset($detailCategory->topPosts) ? array_slice($detailCategory->topPosts, 0, 4) : [];
        $post = isset($detailNewsResponse->post) ? $detailNewsResponse->post : [];

        // override SEO
        $seo = generateSeoMetaData();
        $seo->title = $detailNewsResponse->post->title;
        $seo->description = $detailNewsResponse->post->meta_description;
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => 'Trang chủ',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.news.index'),
                            'name' => 'Tin tức',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '3',
                        'item' => [
                            '@id' => url()->current(),
                            'name' => $detailNewsResponse->post->category->name ?? '',
                        ],
                    ],
                ],
            ]),

            generateJsonLd('NewsArticle', [
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => url()->current(),
                ],
                'headline' => $seo->title,
                'description' => $seo->title,
                'image' => [
                    '@type' => 'ImageObject',
                    'url' => '',
                ],
                'datePublished' => '',
                'dateModified' => '',
                'author' => [
                    '@type' => 'Organization',
                    'name' => 'Domain',
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => '',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => '',
                    ],
                ],
                'keywords' => explode(',', $detailNewsResponse->post->meta_keywords ?? ''),
                'articleBody' => $detailNewsResponse->post->content ?? '',
            ]),
        ];

        return view('pages.news-detail', [
            'title' => $name,
            'breadCrump' => $breadCrump,
            'listCategory' => $listCategoryResponse,
            'post' => $post,
            'relatedPosts' => $relatedPosts,
            'topPosts' => $topPosts,
            'totalRelatedPosts' => $totalRelatedPosts,
        ]);
    }

    public function category(Request $request, $slug)
    {

        $seoNewsPath = [
            'huong-dan-ca-cuoc' => 'betting-guide',
            'soi-keo-bong-da' => 'soi-keo-bong-da',
            'tin-tong-hop' => 'tin-tong-hop',
            'blog-lo-de' => 'soi-cau-lo-de',
            'du-doan-xo-so' => 'du-doan-xo-so',
        ];


        $page = $request->get('page', 1);

        $paramsCategory = ['limit' => self::LIMIT_NEWS_CATEGORY, 'alias' => $slug, 'page' => $page];
        $detailCategory = $this->newsService->getDetailCategory($request, $paramsCategory);
        $listCategoryResponse = $this->newsService->getListCategory($request);
        $detailCategory = (object) $detailCategory;
        if (empty($detailCategory) ||   empty((array)$detailCategory->category)) {
            return redirect(UrlPathEnum::NEWS->value);
        }

        $topPosts = isset($detailCategory->topPosts) ? array_slice(array_filter($detailCategory->topPosts, function ($post) {
            return !empty($post);
        }), 0, 4) : [];
        $posts = isset($detailCategory->posts) ? array_filter($detailCategory->posts, function ($post) {
            return !empty($post);
        }) : [];
        $category = $detailCategory->category;
        $name = __('common.news_breadcrumb');
        $backURL = UrlPathEnum::NEWS->value;

        $pagination = [
            'current_page' => $page,
            'per_page' => self::LIMIT_NEWS_CATEGORY,
            'total' => $detailCategory->totalPost,
        ];

        $breadCrump = [
            [
                'name' => $name,
                'url' => $backURL,
            ],
            [
                'name' => $detailCategory->category->name ?? '',
                'url' => $request->url(),
            ],
        ];

        // config SEO
        $seo = generateSeoMetaData(isset($seoNewsPath[$slug]) ? $seoNewsPath[$slug] : 'tin-tuc');
        $seo->title = $detailCategory->category->meta_title ?? '';
        $seo->description = $detailCategory->category->meta_description ?? '';
        $seo->keywords = $detailCategory->category->meta_keywords ?? '';
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => 'Trang chủ',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.news.index'),
                            'name' => 'Tin tức',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '3',
                        'item' => [
                            '@id' => url()->current(),
                            'name' => $detailCategory->category->name ?? '',
                        ],
                    ],
                ],
            ]),

            generateJsonLd('NewsArticle', [
                'mainEntityOfPage' => [
                    '@type' => 'WebPage',
                    '@id' => url()->current(),
                ],
                'headline' => $seo->title,
                'description' => $seo->title,
                'image' => [
                    '@type' => 'ImageObject',
                    'url' => '',
                ],
                'datePublished' => '',
                'dateModified' => '',
                'author' => [
                    '@type' => 'Organization',
                    'name' => 'Domain',
                ],
                'publisher' => [
                    '@type' => 'Organization',
                    'name' => '',
                    'logo' => [
                        '@type' => 'ImageObject',
                        'url' => '',
                    ],
                ],
                'keywords' => explode(',', $detailNewsResponse->post->meta_keywords ?? ''),
                'articleBody' => $detailNewsResponse->post->content ?? '',
            ]),
        ];


        return view('pages.category', [
            'topPosts' => $topPosts,
            'posts' => $posts,
            'category' => $category,
            'breadCrump' => $breadCrump,
            'title' => $name,
            'pagination' => $pagination,
            'listCategory' => $listCategoryResponse,
            'currentCategory' => $slug,
        ]);
    }
}
