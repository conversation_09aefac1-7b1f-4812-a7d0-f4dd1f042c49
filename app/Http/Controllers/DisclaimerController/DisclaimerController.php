<?php

namespace App\Http\Controllers\DisclaimerController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DisclaimerController extends Controller
{
    public function index(Request $request)
    {
        //config SEO
        generateSeoMetaData('disclaimer');

        $navList = config('info.navList');
        $pageData = config('disclaimer');
        $content = $pageData['content'];
        $breadCrump = [['name' => $pageData['title'], 'url' => $request->url()]];
        $mobileBreadCrump = [
            ['name' => $pageData['help'], 'url' =>  UrlPathEnum::HELP],
            ['name' => $pageData['mobileTitle'], 'url' => $request->url()],
        ];
        $title = ['mobile' => $pageData['mobileTitle'], 'pc' => $pageData['title']];

        return view('pages.disclaimer', [
            'navList' => $navList,
            'breadCrump' => $breadCrump,
            'content' => $content,
            'mobileBreadCrump' => $mobileBreadCrump,
            'title' => $title,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }
}
