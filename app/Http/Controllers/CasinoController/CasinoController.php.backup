<?php

namespace App\Http\Controllers\CasinoController;

use App\Enums\Common;
use App\Enums\GatewayEndpoint;
use App\Enums\UrlPathEnum;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\CasinoService;
use App\Services\GameService;
use App\Services\GatewayApi;
use App\Traits\GameFilterTrait;
use GuzzleHttp\Promise\Utils;
use Illuminate\Http\Request;

class CasinoController extends Controller
{
    use GameFilterTrait;

    // Api gateway service
    protected $GatewayApi;
    private $gameService;
    private $casinoService;
    public function __construct(GatewayApi $GatewayApi, GameService $gameService, CasinoService $casinoService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->gameService = $gameService;
        $this->casinoService = $casinoService;
    }

    public function index(Request $request, $type = null)
    {
        //Get games
        if (!$type) {
            $type = Common::TYPE_ALL->value;
        } elseif ($type && $type !== 'favorite') {
            $request->merge(['type' => $type]);
        } elseif ($type === 'favorite') {
            $request->merge(['sort' => $type]);
        }
        
        // if the request is made via js, return only the game list
        if ($request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            $response = $this->getGames($request, $this->GatewayApi,  GatewayEndpoint::CASINO_SEARCH->value);
            return $response;
        }

        $promises = [
            'casinoGamesRes' => $this->getGames($request, $this->GatewayApi, GatewayEndpoint::CASINO_SEARCH->value),
            'gameType' => $this->gameService->getGameTypes(request: $request, endpoint: GatewayEndpoint::CASINO_TYPE->value),
            "streamGamesRes" => $this->casinoService->getCasinoGames(request(), ['limit' => 7, 'partner' => 'rik,go,b52', 'sort' => '', 'page' => 1]),
            "newStreamGamesRes" => $this->casinoService->getCasinoGames(request(), ['limit' => 6, 'partner' => 'go,rik,b52', 'sort' => 'new', 'page' => 1])
        ];

        $responses = Utils::all($promises)->wait();

        $casinoGamesRes = $responses['casinoGamesRes'];
        $gameType = $responses['gameType'];
        $streamGamesRes = $responses['streamGamesRes'];
        $newStreamGamesRes = $responses['newStreamGamesRes'];

        $gamesTotal = !empty($casinoGamesRes->data->total) ? $casinoGamesRes->data->total : 0;
        $games = ! empty($casinoGamesRes->data->items) ? $casinoGamesRes->data->items : [];
        $streamGames = $streamGamesRes->items ?? [];
        $newStreamGames = $newStreamGamesRes->items ?? [];

        $filterKey = 'casinoFilter';
        $prefixLink = UrlPathEnum::CASINO->value;
        $activeFilter = $this->getActiveFilter($request);
        $filters = $this->getFilters($filterKey, $type, $gameType, $prefixLink);

        $swiperConfig = config('constants.swiperConfig');
        $isValidUrl = $this->checkIsValidGameType($type, $gameType, '/song-bai-livecasino-truc-tuyen');
        abort_if(!$isValidUrl, 404);

        // config SEO
        $seoCasinoPath = [
            'all' => 'casino',
            'sexy' => 'sexy',
            'tai-xiu' => 'sicbo',
            'baccarat' => 'baccarat',
            'xoc-dia' => 'xocdia',
            'poker' => 'poker',
            'blackjack' => 'blackjack',
            'roulette' => 'roulette',
            'rong-ho' => 'dragon-tiger',
            'game-khac' => 'casino-other',
        ];

        $breadCrumbText = isset(config('casino.breadcrumbText')[$type]) ? config('casino.breadcrumbText')[$type] : __('common.casino');
        $seo = generateSeoMetaData(isset($seoCasinoPath[$type]) ? $seoCasinoPath[$type] : 'casino');
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [
                    [
                        '@type' => 'ListItem',
                        'position' => '1',
                        'item' => [
                            '@id' => route('en.home.index'),
                            'name' => 'Trang chủ',
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '2',
                        'item' => [
                            '@id' => route('en.casino.index'),
                            'name' =>  __('common.casino'),
                        ],
                    ],
                    [
                        '@type' => 'ListItem',
                        'position' => '3',
                        'item' => [
                            '@id' => $request->url(),
                            'name' => $type === 'all' ? 'Tất cả' : $breadCrumbText,
                        ],
                    ],
                ],
            ]),

            generateJsonLd('Product', [
                'name' => $seo->title,
                'image' => '',
                'description' => $seo->description,
                'sku' => '',
                'brand' => [
                    '@type' => 'Brand',
                    'name' => '',
                ],
                'aggregateRating' => [
                    '@type' => 'AggregateRating',
                    'ratingValue' => '4.8',
                    'reviewCount' => '276',
                    'bestRating' => '5',
                ],
            ]),
        ];

        return view('pages.casino', [
            'games' => $games,
            'activeFilter' => $activeFilter,
            'filters' => $filters,
            'routeUrl' => $request->url(),
            'swiperConfig' => $swiperConfig,
            'gamesTotal' => $gamesTotal,
            'streamGames' => $streamGames ?? [],
            'newStreamGames' => $newStreamGames ?? [],
            'breadCrumbText' => $breadCrumbText,
        ]);
    }

    public function casinoUrl(Request $request)
    {
        return response()->json([
            'status' => 'OK',
            'data' => $request->all(),
        ]);
    }
}
