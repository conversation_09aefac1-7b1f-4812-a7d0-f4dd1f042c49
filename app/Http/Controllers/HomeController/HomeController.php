<?php

namespace App\Http\Controllers\HomeController;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\GatewayApi;
use App\Services\SportsService;
use App\Services\TopWinnerService;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HomeController extends Controller
{
    // TODO: move it to Enums - LobbiesEnum
    public const NOHU_TYPE = 'nohu';
    public const SORT_ALL = 'all';

    // Api gateway service
    /**
     * @var GatewayApi
     */
    protected $GatewayApi;

    /**
     * @var SportsService
     */
    private $sportsService;

    /**
     * @var TopWinnerService
     */
    private TopWinnerService $topWinnerService;

    protected $authService;

    /**
     * HomeController constructor.
     * @param GatewayApi $GatewayApi
     * @param AuthService $authService
     * @param SportsService $sportsService
     * @param TopWinnerService $topWinnerService
     */
    public function __construct(
        GatewayApi $GatewayApi,
        SportsService $sportsService,
        TopWinnerService $topWinnerService,
        AuthService $authService,
    ) {
        $this->GatewayApi = $GatewayApi;
        $this->sportsService = $sportsService;
        $this->topWinnerService = $topWinnerService;
        $this->authService = $authService;
    }

    public function index(Request $request)
    {

        $token = $request->query('token');

        if ($token) {
            $result = $this->authService->loginWithToken($request, $token);
            if ($result['status']) {
                $result['status'] = 'OK';
                return redirect()->route('en.home.index')->withCookie($result['cookie']);
            } else {
                $request->session()->flash('TokenExpired', 'Không tìm thấy người dùng');
            }
        }
        //config SEO
        $seo = generateSeoMetaData('home');
        $seo->schemas = [

            generateJsonLd('Organization', [
                'name' => $seo->title,
                'url' =>  url()->current(),
                'logo' => asset('asset/images/brand/favicon.svg'),
                'contactPoint' => [
                    '@type' => 'ContactPoint',
                    'telephone' => '+842412345678',
                    'contactType' => 'Customer Service',
                    'email' => 'support@' . strtolower(config('app.brand_name')) . '.com',
                    'availableLanguage' => ['Vietnamese', 'English'],
                ],
                'address' => [
                    '@type' => 'PostalAddress',
                    'streetAddress' => '123 Đường Nguyễn Huệ',
                    'addressLocality' => 'Quận 1',
                    'addressRegion' => 'TP. Hồ Chí Minh',
                    'postalCode' => '700000',
                    'addressCountry' => 'VN',
                ],
            ]),

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'url' => route('en.home.index'),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => 'SearchAction',
                    'target' => url('/') . '/search?q={search_term_string}',
                    'query-input' => 'required name=search_term_string',
                ],
            ]),
        ];

        $sportSwiperConfig = [
            'slidesPerView' => 3,
            'spaceBetween' => 6,
            'loop' => true,
        ];

        $responses = $this->GatewayApi->getMultiple([
            [
                'queryparams' => ['limit' => 7, 'partner' => 'vingame,rik,go,b52', 'sort' => '', 'page' => 1],
                'endpoint' => '/casino/search',
            ],
            [
                'queryparams' => ['limit' => 6, 'partner' => 'vingame,go,rik,b52', 'sort' => 'new', 'page' => 1],
                'endpoint' => '/casino/search',
            ],
            [
                'queryparams' => ['limit' => 30, 'type' => self::NOHU_TYPE, 'sort' => self::SORT_ALL, 'page' => 1],
                'endpoint' => '/game/search',
            ],
            [
                'queryparams' => ['limit' => 2],
                'endpoint' => '/posts/latest',
            ],
        ]);
        $data = array_map(function ($response) {
            if (isset($response->code) && $response->code === Response::HTTP_OK) {
                return $response->data ?? [];
            }
            return [];
        }, $responses);



        $streamGames = $data[0] ?? [];
        $newStreamGames = $data[1] ?? [];
        $nohuGames = $data[2] ?? [];
        // non-standerd gw api response
        $listNews = isset($responses[3]->code) && $responses[3]->code === Response::HTTP_OK ? $responses[3]->posts ?? [] : [];

        $hotMatches = $this->sportsService->getHotMatches();

        // Get top winners
        $cacheKey = 'top_winners_slot';
        $topWinners = cache()->remember($cacheKey, 60 * 5, function () use ($request) {
            return $this->topWinnerService->getTopWinner($request, isSlot: true);
        });
        $nearWin = $topWinners->nearWin ?? [];



        return view(
            'pages.home',
            [
                'data' => [
                    'streamGames' => $streamGames->items ?? [],
                    'nohuGames' => $nohuGames->items ?? [],
                ],
                'newStreamGames' => $newStreamGames->items ?? [],
                'sportSwiperConfig' => $sportSwiperConfig,
                'nohuList' => $nohuGames,
                'hotMatches' => $hotMatches,
                'nearWin' => $nearWin,
                'listNews' => $listNews,
            ],
        );
    }
}
