<?php

namespace App\Http\Controllers\LobbyController;

use App\Http\Controllers\Controller;
use App\Services\GatewayApi;
use Illuminate\Http\Request;

class LobbyController extends Controller
{
    // Api gateway service
    protected $GatewayApi;

    public function __construct(GatewayApi $GatewayApi)
    {
        $this->GatewayApi = $GatewayApi;
    }

    public function index(Request $request)
    {
        // override SEO
        generateSeoMetaData();
        $lang = $request->lang;
        //Get games
        $endpoint = '/game/search/';
        $gameFilter = config('constants.gameFilter');

        // parse query params
        $filter = $request->query('filter') ?? '';
        $type = $request->query('type') ?? '';
        $keyword = $request->query('keyword') ?? '';

        $queryparams = ['limit' => $gameFilter['limit'], 'filter' => $filter, 'type' => $type, 'keyword' => $keyword];

        $activeFilter = [
            'filter' => $filter,
            'type' => $type,
            'keyword' => $keyword,
        ];

        // make use of gamefilter
        // $gameFilter['limit']
        $response = $this->GatewayApi->get(endpoint: $endpoint, queryparams: $queryparams, cookies: ['lang' => $lang]);
        // Check if the request is AJAX
        if ($request->ajax() || $request->header('X-Requested-With') === 'XMLHttpRequest') {
            return $response;
        }

        return view('pages.lobby', ['data' => $response, 'gameFilter' => $gameFilter, 'activeFilter' => $activeFilter]);
    }
}
