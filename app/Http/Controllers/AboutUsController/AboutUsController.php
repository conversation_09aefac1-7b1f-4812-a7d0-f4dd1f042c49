<?php

namespace App\Http\Controllers\AboutUsController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AboutUsController extends Controller
{
    public function index(Request $request)
    {
        //config SEO
        generateSeoMetaData('about-us');

        $navList = config('info.navList');
        $pageData = config('about-us');
        $content = $pageData['content'];
        $breadCrump = [['name' => $pageData['title'], 'url' => $request->url()]];
        $mobileBreadCrump = [
            ['name' => $pageData['help'], 'url' =>  UrlPathEnum::HELP],
            ['name' => $pageData['mobileTitle'], 'url' => $request->url()],
        ];
        $title = ['mobile' => $pageData['mobileTitle'], 'pc' => $pageData['title']];

        return view('pages.about-us', [
            'navList' => $navList,
            'breadCrump' => $breadCrump,
            'content' => $content,
            'mobileBreadCrump' => $mobileBreadCrump,
            'title' => $title,
            'backUrl' => UrlPathEnum::HELP,
        ]);
    }
}
