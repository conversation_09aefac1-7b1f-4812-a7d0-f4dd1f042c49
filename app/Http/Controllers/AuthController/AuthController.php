<?php

namespace App\Http\Controllers\AuthController;

use App\Enums\GatewayEndpoint;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends Controller
{
    protected $GatewayApi;
    private $authService;

    public function __construct(GatewayApi $GatewayApi, AuthService $authService)
    {
        $this->GatewayApi = $GatewayApi;
        $this->authService = $authService;
    }

    public function index(Request $request)
    {
        // override SEO
        generateSeoMetaData();

        return view('pages.login');
    }

    public function signup(Request $request)
    {
        // override SEO
        generateSeoMetaData();

        return view('pages.signup');
    }

    public function refresh(Request $request)
    {
        try {

            $passHeaders = [];
            $headersAll = $request->headers->all();
            foreach ($headersAll as $key => $value) {
                $passHeaders[$key] = $value[0];
            }

            $userCookie = $request->cookie('user');

            $response = $this->GatewayApi->get(endpoint: GatewayEndpoint::REFRESH->value, headers: $passHeaders, cookies: ['user' => $userCookie]);


            if (!empty($response->status) && $response->status === Response::$statusTexts[Response::HTTP_OK]) {
                $cookie = $this->authService->updateCookie(user: $response->user);
                return response()->json($response)->cookie($cookie);
            }

            $this->authService->logout();
            // Handle 401
            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_UNAUTHORIZED],
                'message' => 'require login',
            ], Response::HTTP_UNAUTHORIZED);
        } catch (\Exception $e) {
            $this->authService->logout();
            // Handle 401
            return response()->json([
                'status' => Response::$statusTexts[Response::HTTP_UNAUTHORIZED],
                'message' => 'require login',
            ], Response::HTTP_UNAUTHORIZED);
        }
    }

    /**
     * Handle login
     *
     * @param request
     */
    public function handleLogin(Request $request)
    {
        $result = $this->authService->checkLogin($request);
        if ($result['status']) {
            return response()->json($result)->cookie($result['cookie']);
        }
        return response()->json($result);
    }

    /**
     * Handle login
     *
     * @param request
     */
    public function handleRegister(Request $request)
    {
        $result = $this->authService->register($request);

        if ($result['status']) {
            return response()->json($result)->cookie($result['cookie']);
        }
        return response()->json($result);
    }

    public function handleChangePass(Request $request)
    {
        $result = $this->authService->changePass($request);

        return response()->json($result);
    }

    public function handleChangeName(Request $request)
    {
        $result = $this->authService->changeInfo($request);

        return response()->json($result);
    }

    /**
     * Handle logout
     *
     * @param request
     */
    public function handleLogout(Request $request)
    {
        $this->authService->logout();
        return response()->json([
            'message' => 'Logout success',
            'status' => Response::$statusTexts[Response::HTTP_OK],
        ]);
    }
}
