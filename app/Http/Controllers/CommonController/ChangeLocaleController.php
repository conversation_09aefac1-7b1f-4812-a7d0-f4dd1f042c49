<?php

namespace App\Http\Controllers\CommonController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ChangeLocaleController extends Controller
{
    public function changeLocale(Request $request)
    {
        $available = ['vi', 'en'];
        if (in_array($request->lang, $available)) {
            session(['locale' => $request->lang]);
            app()->setLocale($request->lang);
        }
        return redirect()->back();
    }
}
