<?php

namespace App\Http\Controllers\ApiController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;

class GatewayProxyController extends Controller
{
    public function handle(Request $request, $any)
    {
        $passHeaders = [];
        $headersAll = $request->headers->all();
        // Either Loop through the $headersAll array
        foreach ($headersAll as $key => $value) {
            // Since $value is an array, we take the first element
            // Assuming we want the first value for each header
            $passHeaders[$key] = $value[0];
        }
        $endpoint = config('services.gw.base_url') . $any . str_replace($request->url(), '', $request->fullUrl());
        $response = Http::noHost()
            ->withOptions(['headers' => ['Accept-Encoding' => 'gzip, deflate, br']])
            ->withHeaders($passHeaders)->withBody($request->getContent())->send($request->method(), $endpoint);

        return response($response->body(), $response->status())->withHeaders($response->headers());
    }
}
