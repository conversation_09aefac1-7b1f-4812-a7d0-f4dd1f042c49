<?php

namespace App\Http\Controllers\SitemapController;

use App\Http\Controllers\Controller;
use App\Services\NewsService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class SitemapController extends Controller
{
    public const LIMIT_NEWS = 1000;

    /**
     * @var NewsService
     */
    protected $newsService;

    public function __construct(NewsService $newsService)
    {
        $this->newsService = $newsService;
    }

    public function index(Request $request)
    {
        $BASE = config('app.url');

        $pages = ['/general.xml', '/games.xml', '/events.xml', '/huongdan.xml', '/articles.xml', '/theloai.xml'];

        $data = [
            'sitemaps' => [
                $BASE . '/general.xml',
                $BASE . '/games.xml',
                $BASE . '/events.xml',
                $BASE . '/huongdan.xml',
                $BASE . '/articles.xml',
                $BASE . '/theloai.xml',
            ],
        ];

        return response(view('pages.sitemap.sitemap')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function general(Request $request)
    {
        $BASE = config('app.url');

        $pages = ['', '/ve-chung-toi', '/chinh-sach-bao-mat', '/dieu-khoan-dieu-kien', '/mien-trach-nhiem', '/cau-hoi-thuong-gap'];

        $pagesList = [];

        foreach ($pages as $page) {
            $data = [
                'link' =>  $BASE . $page,
                'time' => Carbon::yesterday()->toDateString(),
                'changefreq' => 'weekly',
                'priority' => 0.8,
                'lastmod' => true,
            ];

            $pagesList[] = $data;
        }

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function guide(Request $request)
    {
        $BASE = config('app.url');

        $pages = ['/huong-dan-dang-ky', '/huong-dan-nap-tien', '/huong-dan-rut-tien', '/huong-dan-giao-dich-p2p'];

        $pagesList = [];

        foreach ($pages as $page) {
            $data = [
                'link' =>  $BASE . $page,
                'time' => Carbon::yesterday()->toDateString(),
            ];

            $pagesList[] = $data;
        }

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function games(Request $request)
    {
        $BASE = config('app.url');

        $BASE_SPORT = $BASE . '/ca-do-bong-da';
        $BASE_CASINO = $BASE . '/song-bai-livecasino-truc-tuyen';
        $BASE_GAMES = $BASE . '/cong-game';

        $gameTypes = ['/all', '/favorite', '/xo-so', '/table-games', '/quay-slots', '/game-nhanh', '/game-khac', '/ban-ca', '/game-bai', '/no-hu', '/quay-so-number-games', '/keno'];
        $casinoTypes = ['/all', '/sexy', '/tai-xiu', '/baccarat', '/xoc-dia', '/poker', '/blackjack', '/roulette', '/rong-ho', '/game-khac'];
        $sportTypes = ['/ksports', '/saba-sports', '/bti-sports', '/im-sports', '/virtual-im-sports', '/virtual-k-sports', '/virtual-saba-sports', '/virtual-pp-sports'];

        $pagesList = [];

        foreach ($sportTypes as $type) {
            $data = [
                'link' =>  $BASE_SPORT . $type,
                'time' => Carbon::yesterday()->toDateString(),
            ];

            $pagesList[] = $data;
        }

        foreach ($casinoTypes as $type) {
            $data = [
                'link' =>  $BASE_CASINO . $type,
                'time' => Carbon::yesterday()->toDateString(),
            ];

            $pagesList[] = $data;
        }

        foreach ($gameTypes as $type) {
            $data = [
                'link' =>  $BASE_GAMES . $type,
                'time' => Carbon::yesterday()->toDateString(),
            ];

            $pagesList[] = $data;
        }

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function events(Request $request)
    {
        $BASE = config('app.url');

        $pages = ['/events', '/events?tab=promotions', '/events?tab=events', '/promo/hoan-tra-khong-gioi-han', '/promo/khuyen-mai-100-lan-nap-dau-tien'];

        $pagesList = [];

        foreach ($pages as $page) {
            $data = [
                'link' =>  $BASE . $page,
                'time' => Carbon::yesterday()->toDateString(),
                'changefreq' => 'weekly',
                'priority' => 0.8,
                'lastmod' => true,
            ];

            $pagesList[] = $data;
        }

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function category(Request $request)
    {
        $BASE = config('app.url');

        $BASE_CATEGORY = $BASE . '/tin-tuc/';

        $listCategoryResponse = $this->newsService->getListCategory($request) ?? [];

        $pagesList = [];

        foreach ($listCategoryResponse as $page) {
            $data = [
                'link' =>  $BASE_CATEGORY . $page->alias,
                'time' => Carbon::yesterday()->toDateString(),
                'changefreq' => 'daily',
                'priority' => 0.8,
                'lastmod' => true,
            ];

            $pagesList[] = $data;
        }

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }

    public function articles(Request $request)
    {
        $BASE = config('app.url');
        $BASE_CATEGORY = $BASE . '/tin-tuc/post/';

        $listCategoryResponse = $this->newsService->getListCategory($request) ?? [];

        $pagesList = [];

        if (count($listCategoryResponse) > 0) {
            foreach ($listCategoryResponse as $key => $category) {
                $params = ['limit' => self::LIMIT_NEWS, 'alias' => $category->alias];
                $detailCategory = $this->newsService->getDetailCategory($request, $params);

                if (isset($detailCategory->posts)) {
                    if (count($detailCategory->posts) > 0) {
                        foreach ($detailCategory->posts as $post) {
                            $data = [
                                'link' =>  $BASE_CATEGORY . $post->alias,
                                'time' => Carbon::yesterday()->toDateString(),
                            ];

                            $pagesList[] = $data;
                        }
                    }
                }
            }
        };

        $data = [
            'pages' => $pagesList,
        ];

        return response(view('pages.sitemap.sitemap-item')->with($data), 200, [
            'Content-Type' => 'text/xml',
        ]);
    }
}
