<?php

namespace App\Http\Controllers\PolicyController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PolicyController extends Controller
{
    public function getPolicy(Request $request)
    {
        //config SEO
        generateSeoMetaData('policy');

        $navList = config('info.navList');
        $pageData = config('policy');
        $content = $pageData['content'];
        $breadCrump = [['name' => $pageData['title'], 'url' => $request->url()]];
        $mobileBreadCrump = [
            ['name' => $pageData['help'], 'url' =>  UrlPathEnum::HELP],
            ['name' => $pageData['mobileTitle'], 'url' => $request->url()],
        ];
        $title = ['mobile' => $pageData['mobileTitle'], 'pc' => $pageData['title']];

        return view('pages.policy', [
            'navList' => $navList,
            'breadCrump' => $breadCrump,
            'content' => $content,
            'mobileBreadCrump' => $mobileBreadCrump,
            'title' => $title,
            'backUrl' => UrlPathEnum::HELP,
        ], );
    }
}
