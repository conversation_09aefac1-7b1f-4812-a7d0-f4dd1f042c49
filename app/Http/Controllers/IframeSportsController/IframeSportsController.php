<?php

namespace App\Http\Controllers\IframeSportsController;

use function App\Helpers\generateJsonLd;
use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\SportsService;
use Illuminate\Http\Request;

class IframeSportsController extends Controller
{
    public function handleSports(Request $request, SportsService $sportsService, $gameType)
    {
        $seoSportPath = [
            'ksports' => 'k-sport',
            'saba-sports' => 'saba-sport',
            'bti-sports' => 'bti-sport',
            'im-sports' => 'im-sport',
            'virtual-im-sports' => 'virtual-im-sport',
            'virtual-k-sports' => 'virtual-k-sport',
            'virtual-saba-sports' => 'virtual-saba-sport',
            'virtual-pp-sports' => 'virtual-pp-sport',
        ];

        // config SEO
        $seo = generateSeoMetaData($seoSportPath[$gameType]);
        $seo->schemas = [

            generateJsonLd('WebSite', [
                'name' => $seo->title,
                'alternateName' => '',
                'url' => url()->current(),
                'description' => $seo->description,
                'potentialAction' => [
                    '@type' => '',
                    'target' => '',
                    'query-input' => '',
                ],
            ]),

            generateJsonLd('BreadcrumbList', [
                'itemListElement' => [],
            ]),
        ];

        try {
            $data = $sportsService->getSportsUrl($gameType);
            $iframeUrl = $data['url'];
            $endpoint = $data['endpoint'];
            $isMobile = $request->get('isMobile');

            return view('pages.iframe-sports', [
                'iframeUrl' => $iframeUrl,
                'isMobile' => $isMobile,
                'endpoint' => $endpoint,
            ]);

        } catch (\Exception $e) {
            \Log::error('Error loading game URL: ' . $e->getMessage());
            return back()->with('error', 'Unable to load the game. Please try again later.');
        }
    }
}
