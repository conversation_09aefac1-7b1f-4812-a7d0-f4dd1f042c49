<?php

namespace App\Http\Controllers\EventsController;

use App\Enums\UrlPathEnum;

use function App\Helpers\generateSeoMetaData;

use App\Http\Controllers\Controller;
use App\Services\NewsService;
use App\Services\FakerApi;
use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;
use App\Enums\GatewayEndpoint;
use Carbon\Carbon;

class EventsController extends Controller
{
    public const LIMIT_NEWS = 4;

    /**
     * @var NewsService
     */
    protected $newsService;
    private FakerApi $fakerApi;
    private GatewayApi $gatewayApi;

    public function __construct(NewsService $newsService, FakerApi $fakerApi, GatewayApi $gatewayApi)
    {
        $this->newsService = $newsService;
        $this->fakerApi = $fakerApi;
        $this->gatewayApi = $gatewayApi;
    }

    public function index(Request $request)
    {
        generateSeoMetaData('events');

        $tabList = config('events.tabList');

        $validEvent = config('events.statusEvent.valid');
        $beforeEvent = config('events.statusEvent.before');
        $expireEvent = config('events.statusEvent.expire');

        $formatFunction = function ($n) {
            return ($n['value']);
        };

        $formatTabList = array_map($formatFunction, $tabList);

        $handleCheckTab = function ($value, $formatTabList) {
            return in_array($value, $formatTabList);
        };

        $tab = $request->input('tab', 'all');

        $selectedTab = $handleCheckTab($tab, $formatTabList) ? $tab : 'all';

        $promotionList = config('events.promotionList');
        $eventList = config('events.eventList');
        $rewardEvent = config('events.rewardEvent');
        $topRacingEvent = config('events.topRacingEvent');

        $targetFilter = array_filter(
            $tabList,
            function ($item) use ($tab) {
                return $item['value'] === $tab;
            },
        );

        $validRewardEvent = request()->get('validRewardEvent');
        $beforeRewardEvent = request()->get('beforeRewardEvent');
        $validTopRacingEvent = request()->get('validTopRacingEvent');
        $beforeTopRacingEvent = request()->get('beforeTopRacingEvent');

        $startTopRacingEvent = config('topracing.startTopRacingEvent');
        $startRewardEvent = config('rewardgoldenhour.startRewardEvent');

        $validEventList = [];
        $beforeEventList = [];
        $expireEventList = [];

        if ($selectedTab === 'events' || $selectedTab === 'all') {
            if (isset($beforeRewardEvent) && isset($validRewardEvent)) {
                if (!$beforeRewardEvent) {
                    if ($validRewardEvent) {
                        $rewardEvent['status'] = $validEvent;
                        $rewardEvent['startTime'] = $startRewardEvent;
                        array_unshift($validEventList, $rewardEvent);
                    } else {
                        $rewardEvent['status'] = $expireEvent;
                        array_unshift($expireEventList, $rewardEvent);
                    }
                } else {
                    $rewardEvent['status'] = $beforeEvent;
                    array_unshift($beforeEventList, $rewardEvent);
                }
            }
      
            if (isset($beforeTopRacingEvent) && isset($validTopRacingEvent)) {
                if (!$beforeTopRacingEvent) {
                    if ($validTopRacingEvent) {
                        $topRacingEvent['status'] = $validEvent;
                        $topRacingEvent['startTime'] = $startTopRacingEvent;
                        array_unshift($validEventList, $topRacingEvent);
                    } else {
                        $topRacingEvent['status'] = $expireEvent;
                        array_unshift($expireEventList, $topRacingEvent);
                    }
                } else {
                    $topRacingEvent['status'] = $beforeEvent;
                    array_unshift($beforeEventList, $topRacingEvent);
                }
            }
        }

        if (count($validEventList) > 0) {
            usort($validEventList, function ($a, $b) {
                $now = Carbon::now('Asia/Ho_Chi_Minh');

                $aDate = Carbon::createFromFormat('d/m/Y', $a['startTime']);
                $bDate = Carbon::createFromFormat('d/m/Y', $b['startTime']);

                $aDiff = abs($now->diffInSeconds($aDate, false));
                $bDiff = abs($now->diffInSeconds($bDate, false));

                return $aDiff <=> $bDiff;
            });
        }

        $allList = array_merge($validEventList, $beforeEventList, $expireEventList, $eventList, $promotionList);

        $list = $allList;

        if ($selectedTab === 'promotions') {
            $list = $promotionList;
        } elseif ($selectedTab === 'events') {
            $list = array_merge($validEventList, $beforeEventList, $expireEventList, $eventList);
        }
        
        return view('pages.events', [
            'selectedTab' => $selectedTab,
            'list' => $list,
            'routeUrl' => $request->url(),
            'title' => array_pop($targetFilter)['label'] ?? '',
        ]);
    }

    public function promo(Request $request, $slug)
    {


        $promotionList = config('events.promotionList');

        $filterPromotion = array_filter($promotionList, function ($person) use ($slug) {
            return $person['slug'] === $slug;
        });

        if (count($filterPromotion) === 0) {
            return redirect(UrlPathEnum::EVENTS_PROMOTIONS->value);
        }

        $params = ['limit' => self::LIMIT_NEWS, 'alias' => 'soi-keo-bong-da'];
        $detailCategory = $this->newsService->getDetailCategory($request, $params);

        $detailCategory = (object) $detailCategory;
        $topPosts = isset($detailCategory->topPosts) ? $detailCategory->topPosts : [];

        $targetPromotion = array_shift($filterPromotion);

        $title = ucfirst(strtolower($targetPromotion['name'] ?? ''));
        $breadCrump = [
            [
                'name' => __('common.promotions'),
                'url' => UrlPathEnum::EVENTS_PROMOTIONS->value,
            ],
            [
                'name' => $title,
                'url' => $request->url(),
            ],
        ];
        // override SEO
        if ($slug) {
            generateSeoMetaData($slug);
        }

        return view('pages.promo', [
            'promotionSlug' => $slug,
            'breadCrump' => $breadCrump,
            'title' => $title,
            'topPosts' => array_slice($topPosts, 0, 4),
        ]);
    }

    public function event(Request $request, $slug)
    {


        $eventList = config('events.eventList');

        $filterEvent = array_filter($eventList, function ($event) use ($slug) {
            return $event['slug'] === $slug;
        });

        if (count($filterEvent) === 0) {
            return redirect(UrlPathEnum::EVENTS_PROMOTIONS->value);
        }
        // Get top posts for the event
        $params = ['limit' => self::LIMIT_NEWS, 'alias' => 'soi-keo-bong-da'];
        $detailCategory = $this->newsService->getDetailCategory($request, $params);
        $detailCategory = (object) $detailCategory;
        $topPosts = isset($detailCategory->topPosts) ? $detailCategory->topPosts : [];

        $targetEvent = array_shift($filterEvent);

        $title = ucfirst(strtolower($targetEvent['name'] ?? ''));
        $breadCrump = [
            [
                'name' => __('common.promotions'),
                'url' => UrlPathEnum::EVENTS_PROMOTIONS->value,
            ],
            [
                'name' => $title,
                'url' => $request->url(),
            ],
        ];
        // override SEO
        if ($slug) {
            generateSeoMetaData($slug);
        }

        return view('pages.promo', [
            'promotionSlug' => $slug,
            'breadCrump' => $breadCrump,
            'title' => $title,
            'topPosts' => array_slice($topPosts, 0, 4),
        ]);
    }

    public function rewardGet(Request $request)
    {
        try {
            if (App::environment('local') && env('FAKER_BASE_URL')) {
                $data = $this->fakerApi->getPromotion(GatewayEndpoint::REWARD_EVENT_INFO->value);
            } else {
                $data = $this->gatewayApi->getPromotion(GatewayEndpoint::REWARD_EVENT_INFO->value);
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function rewardPost(Request $request)
    {
        try {
            if (App::environment('local') && env('FAKER_BASE_URL')) {
                $data = $this->fakerApi->postPromotion(GatewayEndpoint::REWARD_EVENT_INFO->value);
            } else {
                $data = $this->gatewayApi->postPromotion(GatewayEndpoint::REWARD_EVENT_INFO->value);
            }
            
            return response()->json($data);
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function rankList(Request $request)
    {
        try {
            $startTime = $_GET['startTime'] ?? null;
            $endTime = $_GET['endTime'] ?? null;

            $data = $this->gatewayApi->getPromotion(GatewayEndpoint::TOP_RACING_EVENT_LIST->value, ['startTime' => $startTime, 'endTime' => $endTime]);

            return response()->json($data);
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function popup(Request $request)
    {
        try {
            $data = $this->gatewayApi->getPromotion(GatewayEndpoint::POPUP->value);

            return response()->json($data);
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function popupClose(Request $request)
    {
        try {
            $id = $request->route('id');
            $data = $this->gatewayApi->postPromotion(GatewayEndpoint::POPUP_CLOSE->value . $id);

            return response()->json($data);
        } catch (\Exception $e) {
            return [
                'status' => Response::$statusTexts[Response::HTTP_BAD_REQUEST],
                'message' => $e->getMessage(),
            ];
        }
    }

    public function timeServer(Request $request)
    {
        return [
            'code' => 200,
            'data' => Carbon::now('Asia/Ho_Chi_Minh') -> format('Y-m-d H:i:s'),
            'status' => "OK"
        ];
    }
}
