<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Services\GatewayApi;
use App\Enums\GatewayEndpoint;
use Illuminate\Support\Facades\Auth;

use Carbon\Carbon;

class DetectGameAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $listCheckGame = [
            'quayso/atom',
            'quayso/numbergame1',
            'quayso/numbergame2',
            'quayso/quayso1',
            'quayso/quayso2',
            'quayso/quayso5',
            'lode-3-mien',
            'lo-de-sieu-toc',
            'mega645',
            'power655',
            'md5',
            'daga/ga28',
            'daga/ws168',
        ];

        $listCheckGameSport = [
            'ca-do-bong-da/ksports',
            'ca-do-bong-da/saba-sports',
            'ca-do-bong-da/bti-sports',
            'ca-do-bong-da/im-sports',
            'ca-do-bong-da/virtual-im-sports',
            'ca-do-bong-da/virtual-k-sports',
            'ca-do-bong-da/virtual-saba-sports',
            'ca-do-bong-da/virtual-pp-sports',
        ];

        $currentPath = $request->path();

        if (in_array($currentPath, $listCheckGame)) {
            if (Auth::user()) {
                if (Auth::user() -> is_updated_fullname == null || Auth::user() -> is_updated_fullname == 1) {
                   return $next($request);
                } else {
                    $type = $request->query('type');

                    if ($type == 'modal-change-name') {
                       return $next($request);
                    } else {
                        $fullUrl = url()->full();

                        $parsed = parse_url($fullUrl);
                        $path = $parsed['path'] ?? '';
                        parse_str($parsed['query'] ?? '', $query);

                        $query['type'] = 'modal-change-name';

                        $newUrl = url($path) . '?' . http_build_query($query);

                        return redirect($newUrl);
                    }
                }
            } else {
                return redirect()->route('en.home.index', ['type' => 'modal-login']);
            }
        } elseif (in_array($currentPath, $listCheckGameSport)) {
            if (Auth::user()) {
                if (Auth::user() -> is_updated_fullname == null || Auth::user() -> is_updated_fullname == 1) {
                   return $next($request);
                } else {
                    $type = $request->query('type');

                    if ($type == 'modal-change-name') {
                       return $next($request);
                    } else {
                        $fullUrl = url()->full();

                        $parsed = parse_url($fullUrl);
                        $path = $parsed['path'] ?? '';
                        parse_str($parsed['query'] ?? '', $query);

                        $query['type'] = 'modal-change-name';

                        $newUrl = url($path) . '?' . http_build_query($query);

                        return redirect($newUrl);
                    }
                }
            } else {
                return $next($request);
            }
        } else {
            return $next($request);
        }
    }
}
