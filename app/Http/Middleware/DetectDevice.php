<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class DetectDevice
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $cfDeviceType = $request->header('CF-Device-Type'); // Values: mobile, tablet, desktop, null
        $isMobile = false;
        if ($cfDeviceType && strtolower($cfDeviceType) !== 'desktop') {
            $isMobile = true;
        } else {
            $userAgent = strtolower($request->header('User-Agent', ''));

            $tabletRegex = '/(tablet|ipad|playbook)|(android(?!.*mobile))/i';
            $mobileRegex = '/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i';
            $shortAgentRegex = '/^(1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d))/i';

            if (preg_match($tabletRegex, $userAgent) || preg_match($mobileRegex, $userAgent) || preg_match($shortAgentRegex, substr($userAgent, 0, 4))) {
                $isMobile = true;
            }
        }
        // Inject into request and views
        $request->merge(['isMobile' => $isMobile]);
        View::share('isMobile', $isMobile);
        App::instance('isMobile', $isMobile);

        return $next($request);
    }

}
