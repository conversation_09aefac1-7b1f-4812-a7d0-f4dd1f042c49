<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HeaderModifier
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // if (app()->environment('production')) {
        //     $request->headers->remove('host');
        // }

        //IP from cloudflare
        $x_forwarded_for = $request->headers->get('CF-Connecting-IP') ?? '';
        $request->headers->set('x-forwarded-for', $x_forwarded_for);

        return $next($request);
    }
}
