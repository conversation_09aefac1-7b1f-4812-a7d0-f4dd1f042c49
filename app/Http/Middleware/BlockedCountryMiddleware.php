<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use <PERSON><PERSON><PERSON>\Location\Facades\Location;
use Symfony\Component\HttpFoundation\Response;

class BlockedCountryMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!config('app.enable_block_countries')) {
            return $next($request);
        }

        $blockedCountries = explode(',', config('app.blocked_country'));

        // Prefer Cloudflare's fast header
        $cfCountry = $request->header('CF-IPCountry');
        if ($cfCountry && in_array($cfCountry, $blockedCountries)) {
            return response()->view('errors.403');
        }

        // Fallback only if no CF header (not proxied by CF)
        if (!$cfCountry) {
            $ip = $request->header('CF-Connecting-IP') ?? $request->ip();
            $position = Location::get($ip); // SLOW!
            if ($position && in_array($position->countryCode, $blockedCountries)) {
                return response()->view('errors.403');
            }
        }

        return $next($request);
    }
}
