<?php

namespace App\Http\Middleware;

use App\Enums\GatewayEndpoint;
use App\Services\GatewayApi;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class JackpotMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $gatewayApi = app(GatewayApi::class);
        $data = $gatewayApi->get(
            endpoint: GatewayEndpoint::SLOT_JACKPOT->value,
            queryparams: [],
            cookies: ['lang' => $request->lang],
        );

        $jackpot = (object)[];
        if (isset($data->code) && $data->code == 200 && $data->data) {
            $jackpot = $data->data;
        }
        $request->merge(['jackpot' => json_encode($jackpot)]);
        return $next($request);
    }
}
