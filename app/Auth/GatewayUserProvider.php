<?php

namespace App\Auth;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Contracts\Auth\UserProvider;

class GatewayUserProvider implements UserProvider
{
    public function retrieveById($identifier)
    {
        $gatewayUser = new GatewayUser($identifier);

        return $gatewayUser;
    }

    public function retrieveByToken($identifier, $token)
    {
        $gatewayUser = new GatewayUser($identifier);

        return $gatewayUser;
    }

    public function updateRememberToken($identifier, $token)
    {
    }

    public function retrieveByCredentials(array $credentials)
    {
        return false;
    }

    public function rehashPasswordIfRequired(Authenticatable $user, array $credentials, bool $force = false)
    {
    }

    public function validateCredentials(Authenticatable $user, array $credentials)
    {
    }
}
