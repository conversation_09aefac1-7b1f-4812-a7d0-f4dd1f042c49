<?php

namespace App\Auth;

use Illuminate\Contracts\Auth\Authenticatable;

class GatewayUser implements Authenticatable
{
    public $id;
    public string|null $token = null;
    public string|null $fullname = null;
    public string|null $phone = null;
    public string|null $username = null;
    public string|bool|null $username_new = null;
    public string|null $boping_id = null;
    public string|null $email = null;
    public string|null $type = null;
    public string|bool|null $bank_account_no = null;
    public string|bool|null $bank_code = null;
    public string|bool|null $plan_id = null;
    public string|bool|null $package_id = null;
    public string|bool|null $bank_name = null;
    public string|null $tp_token = null;
    public string|null $register_ip = null;
    public string|null $momo_code = null;
    public string|null $code_pay = null;
    public string|null $member_id = null;
    public string|bool|null $is_verify_email = null;
    public string|bool|null $is_verify_phone = null;
    public string|bool|null $is_verify_tele = null;
    public string|bool|null $tele_chat_id = null;
    public string|null $kyc_status = null;
    public string|null $identity_fullname = null;
    public string|null $is_updated_fullname = null;
    public string|null $balance = null;
    public string|null $balance_txt = null;
    public string|null $last_login  = null;
    public array|null $events = null;


    public function __construct($GatewayUserData)
    {
        try {
            foreach ($GatewayUserData as $key => $value) {
                $this->{$key} = $value;
            }
        } catch (\Throwable $th) {
            //throw $th;
        }
    }
    public function getAuthIdentifierName()
    {
        return 'id';
    }

    public function getAuthIdentifier()
    {
        return $this->id;
    }

    public function getAuthPasswordName()
    {
        return 'password';
    }

    public function getAuthPassword()
    {
        return null;
    }

    public function getRememberToken()
    {
        return null;
    }

    public function setRememberToken($value)
    {
    }

    public function getRememberTokenName()
    {
        return null;
    }
}
