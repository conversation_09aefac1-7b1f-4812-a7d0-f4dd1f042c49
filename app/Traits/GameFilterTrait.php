<?php

namespace App\Traits;

use App\Services\GatewayApi;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

trait GameFilterTrait
{
    public const CONFIG_GAME = [
        'ga28' => '/gameUrl?p=ga28&gId=lobby',
        'ws168' => '/gameUrl?p=ws168&gId=lobby',
        'lo-de-sieu-toc' => '/lodeVirtualUrl',
        'lode' => '/lode',
        'quayso1' => '/gameUrl?p=vingame&gId=quayso',
        'quayso2' => '/gameUrl?p=vingame&gId=quayso2',
        'quayso5' => '/gameUrl?p=vingame&gId=quayso5',
        'atom' => '/gameUrl?p=vingame&gId=atom',
        'numbergame1' => '/gameUrl?p=vingame&gId=1000',
        'numbergame2' => '/tp/numberGame2Url',
    ];

    public const GAME_MAP = [
        'quay-so-number-games' => 'lottery',
        'keno' => 'keno',
        'no-hu' => 'nohu',
        'game-bai' => 'game_cards',
        'xo-so' => 'xo-so',
        'ban-ca' => 'fishing',
        'quay-slots' => 'slots',
        'table-games' => 'tables',
        'game-nhanh' => 'instant',
        'game-khac' => 'other',
        'favorite' => 'favorite',

        //casino
        'rong-ho' => 'dragontiger',
        'xoc-dia' => 'xocdia',
        'tai-xiu' => 'sicbo',
    ];

    private function getGameType(String $type)
    {
        if (isset(self::GAME_MAP[$type])) {
            return self::GAME_MAP[$type];
        }
        return $type;
    }

    private function getGameURLByType(String $type)
    {
        $key = array_search($type, self::GAME_MAP);

        if ($key) {
            return $key;
        }

        return $type;
    }

    private function getQueryParams(Request $request)
    {
        $limit = config('constants.gameFilter.limit');
        $filter = $request->query('filter') ?? '';
        $type = $request->query('type') ?? '';
        $keyword = $request->query('keyword') ?? '';
        $p = $request->query('p') ?? '';
        $page = $request->query('page') ?? 1;
        $sort = $request->query('sort') ?? ($filter ?? '');
        $type = $this->getGameType($type);

        $queryparams = [
            'page' => $page,
            'limit' => $limit,
            'filter' => $filter,
            'type' => $type,
            'keyword' => substr($keyword, 0, 20),
            'p' => $p === 'all' ? '' : $p,
        ];
        if ($sort) {
            $queryparams['sort'] = $sort;
        }
        if ($type === 'favorite') {
            $queryparams['sort'] = 'favorite';
        }
        if (!isset($queryparams['sort']) || !$queryparams['sort']) {
            $queryparams['sort'] = 'hot';
        }
        return $queryparams;
    }

    public function getFilters(string $key, string $type, $typeList = [], string $prefixLink = '')
    {
        $sortOptions = config("constants.{$key}.sortOptions");
        $providerOptions = $this->getProvidersByType($this->getGameType($type));
        $providerRecents = $this->getProvidersByType('recent');
        $typeOptions = [];
        if ($typeList && count($typeList)) {
            $typeOptions = array_map(function ($item) use ($prefixLink) {
                $item->link = $prefixLink . '/' . $this->getGameURLByType($item->key);
                if (!isset($item->id)) {
                    $item->id = $item->key;
                }
                return $item;
            }, $typeList);
        }
        if ($providerOptions) {

            $providerOptions = array_map(function ($provider) {
                if (!isset($provider->value)) {
                    $provider->value = $provider->key;
                    $provider->label = $provider->name;
                    $provider->icon = $provider->key;
                }
                return $provider;
            }, $providerOptions);
        }

        return [
            'typeOptions' => $typeOptions,
            'providerOptions' => $providerOptions,
            'sortOptions' => $sortOptions,
            'providerRecents' => $providerRecents,
        ];
    }

    public function getActiveFilter(Request $request)
    {
        $activeFilter = $this->getQueryParams($request);
        return $activeFilter;
    }

    public function getGames(Request $request, GatewayApi $gatewayApi, string $endpoint)
    {
        $lang = $request->lang;

        $queryparams = $this->getQueryParams($request);
        $response = $gatewayApi->get(
            endpoint: $endpoint,
            queryparams: $queryparams,
            cookies: ['lang' => $lang],
        );

        return $response;
    }



    private function getAllProviders()
    {
        $providers = $this->getCachedProviders();
        return $providers;
    }

    private function getProvidersByType($type = 'all')
    {
        $providers = $this->getProviders();

        if (!is_null($providers) && property_exists($providers, $type)) {

            return $providers->$type;
        }

        return [];
    }

    // Providers
    private $PROVIDER_CACHE_KEY = 'providers';
    private $PROVIDER_CACHE_TTL = 15 * 60; // 15 minutes
    private function getProviders()
    {
        // Determine endpoint based on current URL
        $currentPath = request()->path();
        $firstSegment = explode('/', $currentPath)[0];

        $endpoint = match ($firstSegment) {
            'cong-game' => '/game/provider',
            'song-bai-livecasino-truc-tuyen' => '/casino/provider',
            default => '/game/provider',
        };

        // Cache the response for 15 minutes using endpoint as key
        $cacheKey = $this->PROVIDER_CACHE_KEY . ':' . $endpoint;
        $response = Cache::remember($cacheKey, $this->PROVIDER_CACHE_TTL, function () use ($endpoint) {
            return $this->GatewayApi->get(endpoint: $endpoint);
        });
        return $response?->data;
    }

    private function clearProviderCache()
    {
        Cache::forget($this->PROVIDER_CACHE_KEY);
    }

    private function getGameUrl(Request $request, $slug, GatewayApi $gatewayApi)
    {
        try {
            $lang = $request->lang;
            $config = self::CONFIG_GAME[$slug];
            $response = $gatewayApi->get(endpoint: $config, cookies: ['lang' => $lang]);
            if ($response->status === Response::$statusTexts[Response::HTTP_OK]) {
                return $response->data->url;
            }
            return null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function checkIsValidGameType($type = 'all', $gameType = [], $prefix = '/cong-game')
    {
        if ($type == 'all') {
            return true;
        }

        foreach ($gameType as $item) {
            $textPath = trim(str_replace($prefix, '', $item->link), '/');
            if ($type === $textPath) {
                return true;
            }
        }

        return false;
    }
}
