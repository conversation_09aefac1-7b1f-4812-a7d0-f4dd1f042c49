<?php

if (! function_exists('flatten_classes')) {
    /**
     * Flatten an array of classes into a single string.
     *
     * @param  array  $classes
     * @return string
     */
    function flatten_classes(array $classes): string
    {
        return collect($classes)
            ->map(fn($value, $key) => is_int($key) ? $value : ($value ? $key : ''))
            ->filter()
            ->implode(' ');
    }
}
