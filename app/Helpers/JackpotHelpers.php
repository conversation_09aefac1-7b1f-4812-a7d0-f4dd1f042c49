<?php

if (! function_exists('get_jackpot_by_game_id')) {
    function get_jackpot_by_game_id(String $gameId): String
    {
        $jackpotList = json_decode(request()->jackpot);
        $jackpot = $jackpotList->{$gameId} ?? 0;

        if ($jackpot > 1000000000) {
            $jackpot = floor($jackpot / 1000000000) * 1000000000;
        } elseif ($jackpot > 1000000) {
            $jackpot = floor($jackpot / 1000000) * 1000000;
        }

        return $jackpot;
    }

    function sum_jackpot()
    {
        $jackpotList = (array) (json_decode(request()->jackpot));
        $jackpot = array_sum($jackpotList);

        if ($jackpot > 1000000000) {
            $jackpot = floor($jackpot / 1000000000) * 1000000000;
        } elseif ($jackpot > 1000000) {
            $jackpot = floor($jackpot / 1000000) * 1000000;
        }

        return $jackpot;
    }
}
