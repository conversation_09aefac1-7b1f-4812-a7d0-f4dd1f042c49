<?php

namespace App\View\Components\ui\account;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class withdraw extends Component
{
    public $withdrawData;
    /**
     * Create a new component instance.
     */
    public function __construct($withdrawData)
    {
        $this->withdrawData = $withdrawData;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.account.withdraw.index', [
            'withdrawData' => $this->withdrawData,
        ]);
    }
}
