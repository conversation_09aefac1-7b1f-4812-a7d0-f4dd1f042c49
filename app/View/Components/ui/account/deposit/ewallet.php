<?php

namespace App\View\Components\ui\account\deposit;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ewallet extends Component
{
    public $depositData;
    /**
     * Create a new component instance.
     */
    public function __construct($depositData)
    {
        $this->depositData = $this->getEwalletData($depositData);
    }

    private function getEwalletData($depositData)
    {
        $ewallets = $depositData['ewallets'] ?? [];
        $activeEwallet = null;
        foreach ($ewallets as $key => $ewallet) {
            if (count($ewallet['data']) > 0) {
                $activeEwallet = $ewallet;
                break;
            }
        }
        $depositData['ewallets'] = $ewallets;
        $activeEwallet['data'] = array_map(function ($item) {
            return array_merge((array)$item, [
                'id' => $item->account_no,
                'value' => $item->account_no,
                'label' => $item->account_name . ' - ' . $item->account_no,
            ]);
        }, $activeEwallet['data'] ?? []);

        $activeEwallet['value'] = [
            'id' => $activeEwallet['data'][0]['account_no'] ?? '',
            'value' => $activeEwallet['data'][0]['account_no'] ?? '',
            'label' => ($activeEwallet['data'][0]['account_name'] ?? '') . ' - ' . ($activeEwallet['data'][0]['account_no'] ?? ''),
        ];

        $ewalletInfo = null;
        if ($activeEwallet && count($activeEwallet['data']) > 0) {
            $ewalletInfo = $activeEwallet['data'][0];
        }
        $depositData['ewalletInfo'] = $ewalletInfo;
        $depositData['activeEwallet'] = $activeEwallet;

        return $depositData;
    }
    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.account.deposit.ewallet', ['depositData' => $this->depositData]);
    }
}
