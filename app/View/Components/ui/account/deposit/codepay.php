<?php

namespace App\View\Components\ui\account\deposit;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\View\Component;

class codepay extends Component
{
    public $depositData;
    /**
     * Create a new component instance.
     */
    public function __construct($depositData)
    {
        $this->depositData = $this->getCodepayData($depositData);
    }

    private function getCodepayData($depositData)
    {
        if (isset($depositData['nicepayData'])) {
            $this->step = 2;
            $createdNicepayTime = Cache::get('createdNicepayTime' . Auth::user()->getAuthIdentifier());
            if ($createdNicepayTime) {
                $now = now()->utc();

                if ($now->diffInMinutes($createdNicepayTime) > 1) {
                    Cache::forget('createdNicepayTime');
                    $depositData['createdTime'] = null;
                    $this->createdTime = null;
                } else {
                    $depositData['createdTime'] = $createdNicepayTime ? json_encode($createdNicepayTime) : null;
                }
            }
        }
        return $depositData;
    }
    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.account.deposit.codepay');
    }
}
