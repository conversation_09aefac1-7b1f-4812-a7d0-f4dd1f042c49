<?php

namespace App\View\Components\ui\account;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class deposit extends Component
{
    public $depositData;


    /**
     * Create a new component instance.
     */
    public function __construct($depositData)
    {
        $this->depositData = $depositData;
    }
    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.account.deposit.index');
    }
}
