<?php

namespace App\View\Components\ui\common;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\Component;

class minigame extends Component
{
    public $token;
    public $partner;
    /**
     * Create a new component instance.
     */
    public function __construct()
    {
        $token = '';
        if (Auth::check()) {
            $user = Auth::user();
            if (isset($user->package_id) && $user->package_id !== 2) {
                $token = $user->tp_token;
            }
        }
        $this->token = $token;
        $this->partner = env('MINIGAME_PARTNER', '');
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.common.minigame');
    }
}
