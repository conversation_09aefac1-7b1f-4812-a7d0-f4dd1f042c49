<?php

namespace App\View\Components\ui\home;

use App\Services\CasinoService;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Live extends Component
{
    public $gameList = [];
    public $newGameList = [];
    public $type = '';
    private $casinoService;
    /**
     * Create a new component instance.
     */
    public function __construct(CasinoService $casinoService, $type = '', $gameList = [], $newGameList = [])
    {
        $this->casinoService = $casinoService;
        $this->type = $type;
        $this->gameList = $gameList;
        $this->newGameList = $newGameList;
        if (!$gameList || !is_array($this->gameList) || !count($this->gameList) || count($this->gameList) < 7) {
            $streamGameParams = ['limit' => 7, 'partner' => 'vingame,rik,go,b52', 'sort' => '', 'page' => 1];

            $streamGames = $this->casinoService->getCasinoGames(request(), $streamGameParams);
            $this->gameList = $streamGames->items ?? [];
        } else {
            $this->gameList = $gameList;
        }
        if (!$newGameList || !is_array($this->newGameList) || !count($this->newGameList) || count($this->newGameList) < 6) {
            $newStreamGameParams = ['limit' => 6, 'partner' => 'vingame,rik,go,b52', 'sort' => 'new', 'page' => 1];
            $newStreamGames = $this->casinoService->getCasinoGames(request(), $newStreamGameParams);
            $this->newGameList = $newStreamGames->items ?? [];
        } else {
            $this->newGameList = $newGameList;
        }
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.ui.home.live', ['gameList' => $this->gameList, 'newGameList' => $this->newGameList]);
    }
}
