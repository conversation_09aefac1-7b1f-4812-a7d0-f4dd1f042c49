<?php

/**
 * Script để tạo các file translation từ hard text đã tìm thấy
 */

require_once 'find_hard_text.php';

class TranslationFileCreator
{
    private $hardTexts = [];
    private $translationKeys = [];
    private $groupedTranslations = [];

    public function __construct()
    {
        // Tìm hard text
        $finder = new HardTextFinder();
        $this->hardTexts = $finder->findHardText('.');
        
        // Tạo translation keys
        $this->generateTranslationKeys();
        
        // Nhóm translations theo category
        $this->groupTranslations();
    }

    private function generateTranslationKeys()
    {
        $counter = 1;
        $usedKeys = [];

        foreach ($this->hardTexts as $result) {
            $text = $result['text'];
            $file = $result['file'];
            
            // Tạo key dựa trên nội dung và context
            $key = $this->generateKey($text, $file, $counter);
            
            // Đảm bảo key là duy nhất
            $originalKey = $key;
            $suffix = 1;
            while (isset($usedKeys[$key])) {
                $key = $originalKey . '_' . $suffix;
                $suffix++;
            }
            
            $usedKeys[$key] = true;
            $this->translationKeys[$key] = $text;
            $counter++;
        }
    }

    private function generateKey($text, $file, $counter)
    {
        // Xác định category dựa trên file path
        $category = $this->getCategoryFromFile($file);
        
        // Tạo key từ text
        $key = $this->textToKey($text);
        
        // Kết hợp category và key
        if ($category && $key) {
            return $category . '.' . $key;
        } elseif ($category) {
            return $category . '.text_' . $counter;
        } else {
            return 'common.' . ($key ?: 'text_' . $counter);
        }
    }

    private function getCategoryFromFile($file)
    {
        // Xác định category dựa trên đường dẫn file
        if (strpos($file, 'auth') !== false) return 'auth';
        if (strpos($file, 'account') !== false) return 'account';
        if (strpos($file, 'deposit') !== false) return 'deposit';
        if (strpos($file, 'withdraw') !== false) return 'withdraw';
        if (strpos($file, 'game') !== false) return 'games';
        if (strpos($file, 'casino') !== false) return 'casino';
        if (strpos($file, 'sports') !== false) return 'sports';
        if (strpos($file, 'news') !== false) return 'news';
        if (strpos($file, 'home') !== false) return 'home';
        if (strpos($file, 'promotion') !== false) return 'promotion';
        if (strpos($file, 'config') !== false) return 'config';
        if (strpos($file, 'footer') !== false) return 'footer';
        if (strpos($file, 'header') !== false) return 'header';
        
        return 'common';
    }

    private function textToKey($text)
    {
        // Chuyển đổi text thành key
        $key = strtolower($text);
        
        // Loại bỏ dấu tiếng Việt
        $key = $this->removeVietnameseAccents($key);
        
        // Chỉ giữ lại chữ cái, số và khoảng trắng
        $key = preg_replace('/[^a-zA-Z0-9\s]/', '', $key);
        
        // Thay khoảng trắng bằng underscore
        $key = preg_replace('/\s+/', '_', trim($key));
        
        // Giới hạn độ dài
        $key = substr($key, 0, 50);
        
        return $key;
    }

    private function removeVietnameseAccents($str)
    {
        $accents = [
            'à' => 'a', 'á' => 'a', 'ạ' => 'a', 'ả' => 'a', 'ã' => 'a',
            'â' => 'a', 'ầ' => 'a', 'ấ' => 'a', 'ậ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a',
            'ă' => 'a', 'ằ' => 'a', 'ắ' => 'a', 'ặ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a',
            'è' => 'e', 'é' => 'e', 'ẹ' => 'e', 'ẻ' => 'e', 'ẽ' => 'e',
            'ê' => 'e', 'ề' => 'e', 'ế' => 'e', 'ệ' => 'e', 'ể' => 'e', 'ễ' => 'e',
            'ì' => 'i', 'í' => 'i', 'ị' => 'i', 'ỉ' => 'i', 'ĩ' => 'i',
            'ò' => 'o', 'ó' => 'o', 'ọ' => 'o', 'ỏ' => 'o', 'õ' => 'o',
            'ô' => 'o', 'ồ' => 'o', 'ố' => 'o', 'ộ' => 'o', 'ổ' => 'o', 'ỗ' => 'o',
            'ơ' => 'o', 'ờ' => 'o', 'ớ' => 'o', 'ợ' => 'o', 'ở' => 'o', 'ỡ' => 'o',
            'ù' => 'u', 'ú' => 'u', 'ụ' => 'u', 'ủ' => 'u', 'ũ' => 'u',
            'ư' => 'u', 'ừ' => 'u', 'ứ' => 'u', 'ự' => 'u', 'ử' => 'u', 'ữ' => 'u',
            'ỳ' => 'y', 'ý' => 'y', 'ỵ' => 'y', 'ỷ' => 'y', 'ỹ' => 'y',
            'đ' => 'd',
            'À' => 'A', 'Á' => 'A', 'Ạ' => 'A', 'Ả' => 'A', 'Ã' => 'A',
            'Â' => 'A', 'Ầ' => 'A', 'Ấ' => 'A', 'Ậ' => 'A', 'Ẩ' => 'A', 'Ẫ' => 'A',
            'Ă' => 'A', 'Ằ' => 'A', 'Ắ' => 'A', 'Ặ' => 'A', 'Ẳ' => 'A', 'Ẵ' => 'A',
            'È' => 'E', 'É' => 'E', 'Ẹ' => 'E', 'Ẻ' => 'E', 'Ẽ' => 'E',
            'Ê' => 'E', 'Ề' => 'E', 'Ế' => 'E', 'Ệ' => 'E', 'Ể' => 'E', 'Ễ' => 'E',
            'Ì' => 'I', 'Í' => 'I', 'Ị' => 'I', 'Ỉ' => 'I', 'Ĩ' => 'I',
            'Ò' => 'O', 'Ó' => 'O', 'Ọ' => 'O', 'Ỏ' => 'O', 'Õ' => 'O',
            'Ô' => 'O', 'Ồ' => 'O', 'Ố' => 'O', 'Ộ' => 'O', 'Ổ' => 'O', 'Ỗ' => 'O',
            'Ơ' => 'O', 'Ờ' => 'O', 'Ớ' => 'O', 'Ợ' => 'O', 'Ở' => 'O', 'Ỡ' => 'O',
            'Ù' => 'U', 'Ú' => 'U', 'Ụ' => 'U', 'Ủ' => 'U', 'Ũ' => 'U',
            'Ư' => 'U', 'Ừ' => 'U', 'Ứ' => 'U', 'Ự' => 'U', 'Ử' => 'U', 'Ữ' => 'U',
            'Ỳ' => 'Y', 'Ý' => 'Y', 'Ỵ' => 'Y', 'Ỷ' => 'Y', 'Ỹ' => 'Y',
            'Đ' => 'D'
        ];

        return strtr($str, $accents);
    }

    private function groupTranslations()
    {
        foreach ($this->translationKeys as $key => $text) {
            $parts = explode('.', $key, 2);
            $category = $parts[0];
            $subKey = $parts[1] ?? $key;
            
            if (!isset($this->groupedTranslations[$category])) {
                $this->groupedTranslations[$category] = [];
            }
            
            $this->groupedTranslations[$category][$subKey] = $text;
        }
    }

    public function createVietnameseFiles()
    {
        foreach ($this->groupedTranslations as $category => $translations) {
            $filePath = "lang/vi/{$category}.php";
            $this->createTranslationFile($filePath, $translations);
            echo "Đã tạo file: $filePath\n";
        }
    }

    public function createEnglishFiles()
    {
        foreach ($this->groupedTranslations as $category => $translations) {
            $filePath = "lang/en/{$category}.php";
            $englishTranslations = [];
            
            foreach ($translations as $key => $vietnameseText) {
                // Tạm thời sử dụng text tiếng Việt, sẽ dịch sau
                $englishTranslations[$key] = $this->translateToEnglish($vietnameseText);
            }
            
            $this->createTranslationFile($filePath, $englishTranslations);
            echo "Đã tạo file: $filePath\n";
        }
    }

    private function translateToEnglish($vietnameseText)
    {
        // Một số bản dịch cơ bản
        $basicTranslations = [
            'Đăng nhập' => 'Login',
            'Đăng ký' => 'Sign Up',
            'Tên đăng nhập' => 'Username',
            'Mật khẩu' => 'Password',
            'Số điện thoại' => 'Phone Number',
            'Email' => 'Email',
            'Xác nhận' => 'Confirm',
            'Hủy' => 'Cancel',
            'Lưu' => 'Save',
            'Tìm kiếm' => 'Search',
            'Trang chủ' => 'Home',
            'Tin tức' => 'News',
            'Thể thao' => 'Sports',
            'Casino' => 'Casino',
            'Trò chơi' => 'Games',
            'Khuyến mãi' => 'Promotions',
            'Hỗ trợ' => 'Support',
            'Tài khoản' => 'Account',
            'Nạp tiền' => 'Deposit',
            'Rút tiền' => 'Withdraw',
            'Lịch sử' => 'History',
            'Thông tin' => 'Information',
            'Cài đặt' => 'Settings',
            'Đăng xuất' => 'Logout',
            'Về chúng tôi' => 'About Us',
            'Liên hệ' => 'Contact',
            'Chính sách' => 'Policy',
            'Điều khoản' => 'Terms',
            'Bảo mật' => 'Security',
            'Nhà cái trực tuyến hàng đầu' => 'Leading Online Betting House',
        ];

        return $basicTranslations[$vietnameseText] ?? $vietnameseText;
    }

    private function createTranslationFile($filePath, $translations)
    {
        $content = "<?php\n\nreturn [\n";
        
        foreach ($translations as $key => $text) {
            $escapedText = addslashes($text);
            $content .= "    '{$key}' => '{$escapedText}',\n";
        }
        
        $content .= "];\n";
        
        // Tạo thư mục nếu chưa tồn tại
        $dir = dirname($filePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        file_put_contents($filePath, $content);
    }

    public function generateReport()
    {
        echo "=== BÁO CÁO TẠO FILE TRANSLATION ===\n\n";
        echo "Tổng số hard text: " . count($this->translationKeys) . "\n";
        echo "Số categories: " . count($this->groupedTranslations) . "\n\n";
        
        foreach ($this->groupedTranslations as $category => $translations) {
            echo "Category: {$category} - " . count($translations) . " translations\n";
        }
    }
}

// Chạy script
$creator = new TranslationFileCreator();
$creator->generateReport();
$creator->createVietnameseFiles();
$creator->createEnglishFiles();

echo "\nHoàn thành tạo file translation!\n";
