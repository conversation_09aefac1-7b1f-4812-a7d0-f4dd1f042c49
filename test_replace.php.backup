<?php

/**
 * <PERSON>ript test để thay thế hard text trên một vài file mẫu
 */

// Test trên một file config đơn giản trước
$testFile = './config/casino.php';

if (!file_exists($testFile)) {
    echo "File test không tồn tại: $testFile\n";
    exit;
}

echo "=== TEST THAY THẾ HARD TEXT ===\n\n";
echo "File test: $testFile\n\n";

// Đọc nội dung file
$content = file_get_contents($testFile);
echo "Nội dung gốc:\n";
echo $content . "\n";

// Các hard text cần thay thế trong file này
$replacements = [
    'Sòng bài' => 'casino.song_bai',
    'Yêu thích' => 'casino.yeu_thich',
    'Tài xỉu' => 'casino.tai_xiu',
    'Xóc đĩa' => 'casino.xoc_dia',
    'Rồng hổ' => 'casino.rong_ho',
    'Khác' => 'casino.khac'
];

$newContent = $content;

foreach ($replacements as $text => $key) {
    // Thay thế trong single quotes
    $newContent = str_replace("'$text'", "__('$key')", $newContent);
    // Thay thế trong double quotes
    $newContent = str_replace("\"$text\"", "__('$key')", $newContent);
}

echo "\nNội dung sau khi thay thế:\n";
echo $newContent . "\n";

// Hỏi user có muốn apply thay đổi không
echo "\nBạn có muốn apply thay đổi này không? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim($line) === 'y' || trim($line) === 'Y') {
    // Backup file gốc
    copy($testFile, $testFile . '.backup');
    
    // Ghi file mới
    file_put_contents($testFile, $newContent);
    echo "Đã apply thay đổi và tạo backup tại: $testFile.backup\n";
} else {
    echo "Không apply thay đổi.\n";
}

echo "\nTest hoàn thành!\n";
