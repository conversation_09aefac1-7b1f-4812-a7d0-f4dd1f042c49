<?php

/**
 * <PERSON>ript tạo báo cáo cuối cùng về việc di chuyển hard text sang hệ thống đa ngôn ngữ
 */

class FinalReport
{
    public function generateReport()
    {
        echo __('common.bAo_cAo_cuOi_cUng_di_chuyEn_hard_text_sang_hE_thOn');
        
        $this->checkTranslationFiles();
        $this->generateSummary();
        $this->generateRecommendations();
    }

    private function checkTranslationFiles()
    {
        echo __('common.1_kiEm_tra_cAc_file_translation_DA_tAon');
        echo str_repeat("-", 50) . "\n";
        
        $languages = ['vi', 'en'];
        $totalFiles = 0;
        $totalKeys = 0;
        
        foreach ($languages as $lang) {
            $langDir = "lang/$lang";
            if (is_dir($langDir)) {
                $files = glob($langDir . '/*.php');
                echo "\nNgôn ngữ: $lang\n";
                
                foreach ($files as $file) {
                    $filename = basename($file);
                    $array = include $file;
                    $keyCount = $this->countKeys($array);
                    
                    echo "  - $filename: $keyCount keys\n";
                    $totalFiles++;
                    $totalKeys += $keyCount;
                }
            }
        }
        
        echo __('common.ntong_ketn');
        echo "- Tổng số file translation: $totalFiles\n";
        echo "- Tổng số translation keys: $totalKeys\n";
    }

    private function countKeys($array)
    {
        $count = 0;
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $count += $this->countKeys($value);
            } else {
                $count++;
            }
        }
        return $count;
    }

    private function generateSummary()
    {
        echo __('common.nn2_tOm_tAt_cOng_viEc_DA_thUc_hiEnn');
        echo str_repeat("-", 50) . "\n";
        
        echo __('common.Da_tim_kiem_va_phat_hien_975_hard_text_tieng_viet_');
        echo __('common.Da_tao_he_thong_phan_loai_translation_keys_theo_ca');
        echo "   - auth: Các text liên quan đến đăng nhập/đăng ký\n";
        echo "   - account: Các text liên quan đến tài khoản\n";
        echo "   - casino: Các text liên quan đến casino\n";
        echo "   - games: Các text liên quan đến games\n";
        echo "   - sports: Các text liên quan đến thể thao\n";
        echo "   - config: Các text từ config files\n";
        echo "   - common: Các text chung\n";
        echo "   - home: Các text trang chủ\n";
        echo "   - news: Các text tin tức\n";
        echo "   - header: Các text header\n";
        
        echo "\n✅ Đã tạo các file translation trong lang/vi/ với nội dung tiếng Việt gốc\n";
        echo "✅ Đã tạo các file translation trong lang/en/ với nội dung đã dịch sang tiếng Anh\n";
        echo "✅ Đã tạo script để thay thế hard text bằng __() function calls\n";
        echo __('common.Da_tao_tu_dien_dich_co_ban_voi_200_cap_tu_vietanhn');
    }

    private function generateRecommendations()
    {
        echo __('common.nn3_khuyEn_nghI_vA_bUOc_tiEp_theon');
        echo str_repeat("-", 50) . "\n";
        
        echo __('common.cAc_bUOc_cAn_thUc_hiEn_tiEpnn');
        
        echo __('common.1_thay_thE_hard_text_trong_coden');
        echo "   - Chạy script replace_hard_text.php để thay thế hard text bằng __() calls\n";
        echo "   - Kiểm tra và test từng file sau khi thay thế\n";
        echo "   - Backup các file gốc trước khi thay thế\n\n";
        
        echo __('common.2_cAi_thiEn_translationn');
        echo "   - Review và cải thiện các bản dịch tiếng Anh\n";
        echo "   - Thêm context cho các translation keys\n";
        echo "   - Xử lý các text có biến động (:brandName, :year, etc.)\n\n";
        
        echo "3. TESTING:\n";
        echo "   - Test chuyển đổi ngôn ngữ trên website\n";
        echo "   - Kiểm tra tất cả các trang có hiển thị đúng\n";
        echo "   - Test với các locale khác nhau\n\n";
        
        echo "4. OPTIMIZATION:\n";
        echo "   - Gộp các translation keys trùng lặp\n";
        echo "   - Tối ưu hóa cấu trúc file translation\n";
        echo "   - Thêm lazy loading cho translation files\n\n";
        
        echo __('common.cAc_script_DA_tAon');
        echo "   - find_hard_text.php: Tìm kiếm hard text\n";
        echo "   - create_translation_files.php: Tạo file translation\n";
        echo "   - translate_to_english.php: Dịch sang tiếng Anh\n";
        echo "   - replace_hard_text.php: Thay thế hard text (chưa chạy)\n";
        echo "   - test_replace.php: Test thay thế trên file mẫu\n\n";
        
        echo __('common.lUu_Y_quan_trOngn');
        echo "   - Luôn backup code trước khi thay thế\n";
        echo "   - Test kỹ trước khi deploy lên production\n";
        echo "   - Một số text có thể cần dịch thủ công để chính xác hơn\n";
        echo "   - Cần cấu hình locale middleware để chuyển đổi ngôn ngữ\n\n";
        
        echo __('common.kEt_quA_mong_DOin');
        echo "   - Website hỗ trợ đa ngôn ngữ (Việt/Anh)\n";
        echo "   - Code dễ bảo trì và mở rộng\n";
        echo "   - Có thể dễ dàng thêm ngôn ngữ mới\n";
        echo "   - Tách biệt nội dung và logic code\n";
    }

    public function showFileStructure()
    {
        echo __('common.nn4_cAu_trUc_file_translationn');
        echo str_repeat("-", 50) . "\n";
        
        $this->showDirectoryTree('lang');
    }

    private function showDirectoryTree($dir, $prefix = '')
    {
        if (!is_dir($dir)) return;
        
        $items = scandir($dir);
        $items = array_diff($items, ['.', '..']);
        
        foreach ($items as $item) {
            $path = $dir . '/' . $item;
            $isLast = ($item === end($items));
            $currentPrefix = $isLast ? '└── ' : '├── ';
            
            echo $prefix . $currentPrefix . $item;
            
            if (is_file($path) && pathinfo($path, PATHINFO_EXTENSION) === 'php') {
                $array = include $path;
                $keyCount = $this->countKeys($array);
                echo " ($keyCount keys)";
            }
            
            echo "\n";
            
            if (is_dir($path)) {
                $nextPrefix = $prefix . ($isLast ? '    ' : '│   ');
                $this->showDirectoryTree($path, $nextPrefix);
            }
        }
    }
}

// Chạy báo cáo
$report = new FinalReport();
$report->generateReport();
$report->showFileStructure();

echo "\n" . str_repeat("=", 80) . "\n";
echo __('common.hoAn_thAnh_phAn_tIch_vA_thiEt_lAp_hE_thOng_Da_ngOn');
echo str_repeat("=", 80) . "\n";
