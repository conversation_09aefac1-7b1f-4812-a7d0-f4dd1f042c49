<?php

/**
 * <PERSON><PERSON>t để thay thế hard text bằng translation keys
 */

require_once 'find_hard_text.php';

class HardTextReplacer
{
    private $hardTexts = [];
    private $translationKeys = [];
    private $replacements = [];

    public function __construct()
    {
        // Tìm hard text
        $finder = new HardTextFinder();
        $this->hardTexts = $finder->findHardText('.');
        
        // Tạo mapping từ text sang translation key
        $this->generateTranslationMapping();
    }

    private function generateTranslationMapping()
    {
        $counter = 1;
        $usedKeys = [];

        foreach ($this->hardTexts as $result) {
            $text = $result['text'];
            $file = $result['file'];
            
            // Tạo key dựa trên nội dung và context
            $key = $this->generateKey($text, $file, $counter);
            
            // Đảm bảo key là duy nhất
            $originalKey = $key;
            $suffix = 1;
            while (isset($usedKeys[$key])) {
                $key = $originalKey . '_' . $suffix;
                $suffix++;
            }
            
            $usedKeys[$key] = true;
            $this->translationKeys[$text] = $key;
            $counter++;
        }
    }

    private function generateKey($text, $file, $counter)
    {
        // Xác định category dựa trên file path
        $category = $this->getCategoryFromFile($file);
        
        // Tạo key từ text
        $key = $this->textToKey($text);
        
        // Kết hợp category và key
        if ($category && $key) {
            return $category . '.' . $key;
        } elseif ($category) {
            return $category . '.text_' . $counter;
        } else {
            return 'common.' . ($key ?: 'text_' . $counter);
        }
    }

    private function getCategoryFromFile($file)
    {
        // Xác định category dựa trên đường dẫn file
        if (strpos($file, 'auth') !== false) return 'auth';
        if (strpos($file, 'account') !== false) return 'account';
        if (strpos($file, 'deposit') !== false) return 'deposit';
        if (strpos($file, 'withdraw') !== false) return 'withdraw';
        if (strpos($file, 'game') !== false) return 'games';
        if (strpos($file, 'casino') !== false) return 'casino';
        if (strpos($file, 'sports') !== false) return 'sports';
        if (strpos($file, 'news') !== false) return 'news';
        if (strpos($file, 'home') !== false) return 'home';
        if (strpos($file, 'promotion') !== false) return 'promotion';
        if (strpos($file, 'config') !== false) return 'config';
        if (strpos($file, 'footer') !== false) return 'footer';
        if (strpos($file, 'header') !== false) return 'header';
        
        return 'common';
    }

    private function textToKey($text)
    {
        // Chuyển đổi text thành key
        $key = strtolower($text);
        
        // Loại bỏ dấu tiếng Việt
        $key = $this->removeVietnameseAccents($key);
        
        // Chỉ giữ lại chữ cái, số và khoảng trắng
        $key = preg_replace('/[^a-zA-Z0-9\s]/', '', $key);
        
        // Thay khoảng trắng bằng underscore
        $key = preg_replace('/\s+/', '_', trim($key));
        
        // Giới hạn độ dài
        $key = substr($key, 0, 50);
        
        return $key;
    }

    private function removeVietnameseAccents($str)
    {
        $accents = [
            'à' => 'a', 'á' => 'a', 'ạ' => 'a', 'ả' => 'a', 'ã' => 'a',
            'â' => 'a', 'ầ' => 'a', 'ấ' => 'a', 'ậ' => 'a', 'ẩ' => 'a', 'ẫ' => 'a',
            'ă' => 'a', 'ằ' => 'a', 'ắ' => 'a', 'ặ' => 'a', 'ẳ' => 'a', 'ẵ' => 'a',
            'è' => 'e', 'é' => 'e', 'ẹ' => 'e', 'ẻ' => 'e', 'ẽ' => 'e',
            'ê' => 'e', 'ề' => 'e', 'ế' => 'e', 'ệ' => 'e', 'ể' => 'e', 'ễ' => 'e',
            'ì' => 'i', 'í' => 'i', 'ị' => 'i', 'ỉ' => 'i', 'ĩ' => 'i',
            'ò' => 'o', 'ó' => 'o', 'ọ' => 'o', 'ỏ' => 'o', 'õ' => 'o',
            'ô' => 'o', 'ồ' => 'o', 'ố' => 'o', 'ộ' => 'o', 'ổ' => 'o', 'ỗ' => 'o',
            'ơ' => 'o', 'ờ' => 'o', 'ớ' => 'o', 'ợ' => 'o', 'ở' => 'o', 'ỡ' => 'o',
            'ù' => 'u', 'ú' => 'u', 'ụ' => 'u', 'ủ' => 'u', 'ũ' => 'u',
            'ư' => 'u', 'ừ' => 'u', 'ứ' => 'u', 'ự' => 'u', 'ử' => 'u', 'ữ' => 'u',
            'ỳ' => 'y', 'ý' => 'y', 'ỵ' => 'y', 'ỷ' => 'y', 'ỹ' => 'y',
            'đ' => 'd',
            'À' => 'A', 'Á' => 'A', 'Ạ' => 'A', 'Ả' => 'A', 'Ã' => 'A',
            'Â' => 'A', 'Ầ' => 'A', 'Ấ' => 'A', 'Ậ' => 'A', 'Ẩ' => 'A', 'Ẫ' => 'A',
            'Ă' => 'A', 'Ằ' => 'A', 'Ắ' => 'A', 'Ặ' => 'A', 'Ẳ' => 'A', 'Ẵ' => 'A',
            'È' => 'E', 'É' => 'E', 'Ẹ' => 'E', 'Ẻ' => 'E', 'Ẽ' => 'E',
            'Ê' => 'E', 'Ề' => 'E', 'Ế' => 'E', 'Ệ' => 'E', 'Ể' => 'E', 'Ễ' => 'E',
            'Ì' => 'I', 'Í' => 'I', 'Ị' => 'I', 'Ỉ' => 'I', 'Ĩ' => 'I',
            'Ò' => 'O', 'Ó' => 'O', 'Ọ' => 'O', 'Ỏ' => 'O', 'Õ' => 'O',
            'Ô' => 'O', 'Ồ' => 'O', 'Ố' => 'O', 'Ộ' => 'O', 'Ổ' => 'O', 'Ỗ' => 'O',
            'Ơ' => 'O', 'Ờ' => 'O', 'Ớ' => 'O', 'Ợ' => 'O', 'Ở' => 'O', 'Ỡ' => 'O',
            'Ù' => 'U', 'Ú' => 'U', 'Ụ' => 'U', 'Ủ' => 'U', 'Ũ' => 'U',
            'Ư' => 'U', 'Ừ' => 'U', 'Ứ' => 'U', 'Ự' => 'U', 'Ử' => 'U', 'Ữ' => 'U',
            'Ỳ' => 'Y', 'Ý' => 'Y', 'Ỵ' => 'Y', 'Ỷ' => 'Y', 'Ỹ' => 'Y',
            'Đ' => 'D'
        ];

        return strtr($str, $accents);
    }

    public function replaceHardTextInFiles()
    {
        $processedFiles = [];
        
        foreach ($this->hardTexts as $result) {
            $file = $result['file'];
            $text = $result['text'];
            $line = $result['line'];
            
            if (!isset($this->translationKeys[$text])) {
                continue;
            }
            
            $translationKey = $this->translationKeys[$text];
            
            if (!isset($processedFiles[$file])) {
                $processedFiles[$file] = [];
            }
            
            $processedFiles[$file][] = [
                'text' => $text,
                'key' => $translationKey,
                'line' => $line
            ];
        }

        foreach ($processedFiles as $file => $replacements) {
            $this->processFile($file, $replacements);
        }
    }

    private function processFile($filePath, $replacements)
    {
        if (!file_exists($filePath)) {
            echo "File không tồn tại: $filePath\n";
            return;
        }

        $content = file_get_contents($filePath);
        $originalContent = $content;
        
        // Sắp xếp replacements theo độ dài text giảm dần để tránh thay thế nhầm
        usort($replacements, function($a, $b) {
            return strlen($b['text']) - strlen($a['text']);
        });

        foreach ($replacements as $replacement) {
            $text = $replacement['text'];
            $key = $replacement['key'];
            
            // Tạo translation function call
            $translationCall = "__('$key')";
            
            // Thay thế hard text bằng translation call
            // Tìm và thay thế trong quotes
            $patterns = [
                // Double quotes
                '/"' . preg_quote($text, '/') . '"/',
                // Single quotes  
                "/'" . preg_quote($text, '/') . "'/"
            ];
            
            foreach ($patterns as $pattern) {
                $content = preg_replace($pattern, $translationCall, $content);
            }
        }

        if ($content !== $originalContent) {
            // Backup file gốc
            $backupPath = $filePath . '.backup';
            if (!file_exists($backupPath)) {
                copy($filePath, $backupPath);
            }
            
            // Ghi file mới
            file_put_contents($filePath, $content);
            echo "Đã thay thế hard text trong: $filePath\n";
        }
    }

    public function generateReport()
    {
        echo "=== BÁO CÁO THAY THẾ HARD TEXT ===\n\n";
        echo "Tổng số hard text cần thay thế: " . count($this->hardTexts) . "\n";
        echo "Tổng số translation keys: " . count($this->translationKeys) . "\n\n";
        
        $fileCount = [];
        foreach ($this->hardTexts as $result) {
            $file = $result['file'];
            if (!isset($fileCount[$file])) {
                $fileCount[$file] = 0;
            }
            $fileCount[$file]++;
        }
        
        echo "Files cần xử lý:\n";
        foreach ($fileCount as $file => $count) {
            echo "  $file: $count hard texts\n";
        }
    }
}

// Chạy script
$replacer = new HardTextReplacer();
$replacer->generateReport();

echo "\nBắt đầu thay thế hard text...\n";
$replacer->replaceHardTextInFiles();
echo "\nHoàn thành thay thế hard text!\n";
