<?php

/**
 * <PERSON><PERSON>t để kiểm tra các translation key bị thiếu
 */

class MissingTranslationChecker
{
    private $missingKeys = [];
    private $allKeys = [];

    public function checkMissingTranslations()
    {
        // Tìm tất cả các __() calls trong code
        $this->findAllTranslationCalls();
        
        // Kiểm tra xem key nào bị thiếu trong file translation
        $this->checkMissingKeys();
        
        // Tạo báo cáo
        $this->generateReport();
    }

    private function findAllTranslationCalls()
    {
        $files = $this->getAllPhpAndBladeFiles();
        
        foreach ($files as $file) {
            $content = file_get_contents($file);
            
            // Tìm các pattern __('key') và __("key")
            $patterns = [
                "/__\('([^']+)'\)/",
                '/__\("([^"]+)"\)/',
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match_all($pattern, $content, $matches)) {
                    foreach ($matches[1] as $key) {
                        $this->allKeys[] = [
                            'key' => $key,
                            'file' => $file
                        ];
                    }
                }
            }
        }
    }

    private function getAllPhpAndBladeFiles()
    {
        $files = [];
        
        // Tìm trong resources/views
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator('resources/views', RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && preg_match('/\.(php|blade\.php)$/', $file->getPathname())) {
                $files[] = $file->getPathname();
            }
        }
        
        // Tìm trong config
        $configFiles = glob('config/*.php');
        $files = array_merge($files, $configFiles);
        
        // Tìm trong app
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator('app', RecursiveDirectoryIterator::SKIP_DOTS)
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $files[] = $file->getPathname();
            }
        }
        
        return $files;
    }

    private function checkMissingKeys()
    {
        $languages = ['vi', 'en'];
        
        foreach ($languages as $lang) {
            $langDir = "lang/$lang";
            if (!is_dir($langDir)) {
                continue;
            }
            
            // Load tất cả translation files
            $translations = [];
            $files = glob($langDir . '/*.php');
            
            foreach ($files as $file) {
                $category = basename($file, '.php');
                $translations[$category] = include $file;
            }
            
            // Kiểm tra từng key
            foreach ($this->allKeys as $keyInfo) {
                $key = $keyInfo['key'];
                $file = $keyInfo['file'];
                
                if (strpos($key, '.') !== false) {
                    list($category, $subKey) = explode('.', $key, 2);
                    
                    if (!isset($translations[$category][$subKey])) {
                        $this->missingKeys[$lang][] = [
                            'key' => $key,
                            'category' => $category,
                            'subKey' => $subKey,
                            'file' => $file
                        ];
                    }
                } else {
                    // Key không có category, tìm trong common
                    if (!isset($translations['common'][$key])) {
                        $this->missingKeys[$lang][] = [
                            'key' => $key,
                            'category' => 'common',
                            'subKey' => $key,
                            'file' => $file
                        ];
                    }
                }
            }
        }
    }

    private function generateReport()
    {
        echo "=== BÁO CÁO TRANSLATION KEYS BỊ THIẾU ===\n\n";
        
        $totalKeys = count($this->allKeys);
        echo "Tổng số translation keys được sử dụng: $totalKeys\n\n";
        
        foreach ($this->missingKeys as $lang => $keys) {
            if (empty($keys)) {
                echo "✅ Ngôn ngữ $lang: Không có key nào bị thiếu\n\n";
                continue;
            }
            
            echo "❌ Ngôn ngữ $lang: " . count($keys) . " keys bị thiếu\n";
            echo str_repeat("-", 50) . "\n";
            
            $groupedByCategory = [];
            foreach ($keys as $keyInfo) {
                $groupedByCategory[$keyInfo['category']][] = $keyInfo;
            }
            
            foreach ($groupedByCategory as $category => $categoryKeys) {
                echo "\nCategory: $category\n";
                foreach ($categoryKeys as $keyInfo) {
                    echo "  - {$keyInfo['key']} (trong file: {$keyInfo['file']})\n";
                }
            }
            echo "\n";
        }
        
        // Tạo file để thêm các key bị thiếu
        $this->generateMissingKeyFiles();
    }

    private function generateMissingKeyFiles()
    {
        foreach ($this->missingKeys as $lang => $keys) {
            if (empty($keys)) continue;
            
            $groupedByCategory = [];
            foreach ($keys as $keyInfo) {
                $groupedByCategory[$keyInfo['category']][] = $keyInfo;
            }
            
            foreach ($groupedByCategory as $category => $categoryKeys) {
                $filePath = "lang/$lang/$category.php";
                
                if (file_exists($filePath)) {
                    $existingTranslations = include $filePath;
                } else {
                    $existingTranslations = [];
                }
                
                foreach ($categoryKeys as $keyInfo) {
                    $subKey = $keyInfo['subKey'];
                    if (!isset($existingTranslations[$subKey])) {
                        $existingTranslations[$subKey] = "MISSING: " . $keyInfo['key'];
                    }
                }
                
                // Ghi lại file
                $content = "<?php\n\nreturn [\n";
                foreach ($existingTranslations as $key => $value) {
                    $escapedValue = addslashes($value);
                    $content .= "    '{$key}' => '{$escapedValue}',\n";
                }
                $content .= "];\n";
                
                file_put_contents($filePath, $content);
                echo "Đã cập nhật file: $filePath\n";
            }
        }
    }

    public function getUniqueKeys()
    {
        $uniqueKeys = [];
        foreach ($this->allKeys as $keyInfo) {
            $uniqueKeys[$keyInfo['key']] = true;
        }
        return array_keys($uniqueKeys);
    }
}

// Chạy script
$checker = new MissingTranslationChecker();
$checker->checkMissingTranslations();

echo "\n=== THỐNG KÊ ===\n";
$uniqueKeys = $checker->getUniqueKeys();
echo "Tổng số unique translation keys: " . count($uniqueKeys) . "\n";

echo "\nMột số keys được sử dụng nhiều:\n";
$keyCount = [];
foreach ($checker->allKeys as $keyInfo) {
    $key = $keyInfo['key'];
    $keyCount[$key] = ($keyCount[$key] ?? 0) + 1;
}

arsort($keyCount);
$topKeys = array_slice($keyCount, 0, 10, true);
foreach ($topKeys as $key => $count) {
    echo "  - $key: $count lần\n";
}

echo "\nHoàn thành kiểm tra!\n";
