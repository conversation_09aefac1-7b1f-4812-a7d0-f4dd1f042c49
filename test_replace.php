<?php

/**
 * <PERSON>ript test để thay thế hard text trên một vài file mẫu
 */

// Test trên một file config đơn giản trước
$testFile = './config/casino.php';

if (!file_exists($testFile)) {
    echo "File test không tồn tại: $testFile\n";
    exit;
}

echo __('common.test_thay_thE_hard_text_nn');
echo "File test: $testFile\n\n";

// Đọc nội dung file
$content = file_get_contents($testFile);
echo __('common.noi_dung_gocn');
echo $content . "\n";

// Các hard text cần thay thế trong file này
$replacements = [
    __('common.song_bai_1') => 'casino.song_bai',
    __('common.yeu_thich_1') => 'casino.yeu_thich',
    __('common.tai_xiu_1') => 'casino.tai_xiu',
    __('common.xoc_dia_1') => 'casino.xoc_dia',
    __('common.rong_ho_1') => 'casino.rong_ho',
    __('common.khac_1') => 'casino.khac'
];

$newContent = $content;

foreach ($replacements as $text => $key) {
    // Thay thế trong single quotes
    $newContent = str_replace("'$text'", "__('$key')", $newContent);
    // Thay thế trong double quotes
    $newContent = str_replace("\"$text\"", "__('$key')", $newContent);
}

echo __('common.nnoi_dung_sau_khi_thay_then');
echo $newContent . "\n";

// Hỏi user có muốn apply thay đổi không
echo "\nBạn có muốn apply thay đổi này không? (y/n): ";
$handle = fopen("php://stdin", "r");
$line = fgets($handle);
fclose($handle);

if (trim($line) === 'y' || trim($line) === 'Y') {
    // Backup file gốc
    copy($testFile, $testFile . '.backup');
    
    // Ghi file mới
    file_put_contents($testFile, $newContent);
    echo "Đã apply thay đổi và tạo backup tại: $testFile.backup\n";
} else {
    echo __('common.khong_apply_thay_doin');
}

echo __('common.ntest_hoan_thanhn');
